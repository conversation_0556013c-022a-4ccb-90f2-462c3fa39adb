import React, { useState } from "react";
import axios from "axios";

const Step1SubjectSelectionJS = ({ onSubmit, initialData }) => {
  const [subjectItems, setSubjectItems] = useState([
    { subject: "", topic: "", pdfFile: null, pdfUrl: "" }
  ]);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubjectChange = (index, value) => {
    const newItems = [...subjectItems];
    newItems[index].subject = value;
    setSubjectItems(newItems);
  };

  const handleTopicChange = (index, value) => {
    const newItems = [...subjectItems];
    newItems[index].topic = value;
    setSubjectItems(newItems);
  };

  const handlePdfChange = async (index, file) => {
    if (!file) return;
    
    const newItems = [...subjectItems];
    newItems[index].pdfFile = file;
    
    try {
      const formData = new FormData();
      formData.append("pdf", file);
      
      const response = await fetch("https://api.aiclassroom.in/pdfUpload/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();
      if (result.success) {
        newItems[index].pdfUrl = result.fileUrl;
        setSubjectItems(newItems);
      } else {
        throw new Error(result.message || "File upload failed");
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const addSubject = () => {
    setSubjectItems([...subjectItems, { subject: "", topic: "", pdfFile: null, pdfUrl: "" }]);
  };

  const removeSubject = (index) => {
    const newItems = subjectItems.filter((_, i) => i !== index);
    setSubjectItems(newItems);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all subjects
    const hasEmptyFields = subjectItems.some(item => !item.subject || !item.topic);
    if (hasEmptyFields) {
      setError("Please fill in all subject and topic fields");
      return;
    }

    setError("");
    onSubmit(subjectItems);
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md mt-8">
      <h2 className="text-2xl font-bold mb-6">What would you like to study?</h2>
      <form onSubmit={handleSubmit}>
        {subjectItems.map((item, index) => (
          <div key={index} className="mb-6 border-b pb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Subject {index + 1}</h3>
              {index > 0 && (
                <button
                  type="button"
                  onClick={() => removeSubject(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  Remove
                </button>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Exam/Subject
              </label>
              <input
                type="text"
                value={item.subject}
                onChange={(e) => handleSubjectChange(index, e.target.value)}
                placeholder="Enter Target Exam/Subject"
                className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Topic/Chapter
              </label>
              <input
                type="text"
                value={item.topic}
                onChange={(e) => handleTopicChange(index, e.target.value)}
                placeholder="Eg. Calculus, World War II, Machine Learning"
                className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload PDF (Optional)
              </label>
              <input
                type="file"
                accept=".pdf"
                onChange={(e) => handlePdfChange(index, e.target.files[0])}
                className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {item.pdfUrl && (
                <p className="text-green-500 text-sm mt-2">PDF uploaded successfully!</p>
              )}
            </div>
          </div>
        ))}

        <div className="mb-6">
          <button
            type="button"
            onClick={addSubject}
            className="text-blue-500 hover:text-blue-700 flex items-center"
          >
            <span className="mr-2">+</span> Add Another Subject
          </button>
        </div>

        {error && <div className="text-red-500 text-sm mb-4">{error}</div>}

        <div className="flex justify-end">
          <button
            type="submit"
            className="bg-blue-500 text-white px-8 py-3 rounded-md hover:bg-blue-600 
              transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
};

export default Step1SubjectSelectionJS;
