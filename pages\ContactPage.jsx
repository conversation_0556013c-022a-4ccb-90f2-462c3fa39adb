import { useState } from "react";

import {
  Mail,
  Phone,
  MapPin,
  Send,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Mic,
  BookOpen,
  Globe,
} from "lucide-react";
const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
  e.preventDefault();
  try {
    const response = await fetch("https://api.aiclassroom.in/send-email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    });

    const result = await response.json();
    if (response.ok) {
      alert("Thank you! Your message has been sent.");
      setFormData({ name: "", email: "", message: "" });
    } else {
      alert(`Failed to send message: ${result.error}`);
    }
  } catch (error) {
    alert("An error occurred. Please try again later.");
    console.error("Error submitting form:", error);
  }
};

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-50 to-white">
      {/* Header Section */}
      <header className="bg-purple-500 text-black py-6">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold text-center">
            Contact AI Classroom
          </h1>
          <p className="text-center mt-2 text-purple-200">
            Have questions? We're here to help transform your learning
            experience.
          </p>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Contact Form */}
          <div className="lg:w-1/2">
            <div className="bg-white rounded-xl shadow-lg p-6 md:p-8">
              <h2 className="text-2xl font-bold text-purple-900 mb-6">
                Send us a message
              </h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows="5"
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-purple-900 hover:bg-purple-800 text-white py-3 px-6 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors duration-200"
                >
                  <Send className="h-5 w-5" />
                  Send Message
                </button>
              </form>
            </div>
          </div>

          {/* Contact Info */}
          <div className="lg:w-1/2">
            <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 h-full">
              <h2 className="text-2xl font-bold text-purple-900 mb-6">
                Contact Information
              </h2>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-purple-100 p-3 rounded-full text-purple-900">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-medium text-lg text-gray-800">
                      Email Us
                    </h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-purple-100 p-3 rounded-full text-purple-900">
                    <Phone className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-medium text-lg text-gray-800">
                      Call Us
                    </h3>
                    <p className="text-gray-600">+91 9798219701</p>
                  </div>
                </div>

  <div>
                    <h3 className="font-medium text-lg text-gray-800">
                      Address
                    </h3>
                    <p className="text-gray-600">RnPsoft Private Limited, Patia, Bhubaneswar Odisha 751024</p>
                  </div>
                
              </div>

              <div className="mt-10">
                <h3 className="font-medium text-lg text-gray-800 mb-4">
                  Follow Us
                </h3>
                <div className="flex gap-4">
                  <a
                    href="https://www.instagram.com/abiv_v1/"
                    className="bg-purple-100 hover:bg-purple-200 p-3 rounded-full text-purple-900 transition-colors duration-200"
                  >
                    <Instagram className="h-5 w-5" />
                  </a>
                  <a
                    href="#"
                    className="bg-purple-100 hover:bg-purple-200 p-3 rounded-full text-purple-900 transition-colors duration-200"
                  >
                    <Twitter className="h-5 w-5" />
                  </a>
                  <a
                    href="https://www.linkedin.com/company/rnpsoft/"
                    className="bg-purple-100 hover:bg-purple-200 p-3 rounded-full text-purple-900 transition-colors duration-200"
                  >
                    <Linkedin className="h-5 w-5" />
                  </a>
                  <a
                    href="https://www.youtube.com/@RnPsoft"
                    className="bg-purple-100 hover:bg-purple-200 p-3 rounded-full text-purple-900 transition-colors duration-200"
                  >
                    <Youtube className="h-5 w-5" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-purple-900 text-white py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">
            Why Choose AI Classroom?
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              {/* <div className="bg-purple-800 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <LightningBolt className="h-8 w-8" />
              </div> */}
              <h3 className="font-medium text-lg mb-2">Instant Generation</h3>
              <p className="text-purple-200">Creating content in real time</p>
            </div>

            <div className="text-center">
              <div className="bg-purple-800 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Mic className="h-8 w-8" />
              </div>
              <h3 className="font-medium text-lg mb-2">Interview Mode</h3>
              <p className="text-purple-200">AI-based interview preparation</p>
            </div>

            <div className="text-center">
              <div className="bg-purple-800 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-8 w-8" />
              </div>
              <h3 className="font-medium text-lg mb-2">Comprehensive Notes</h3>
              <p className="text-purple-200">
                Well-structured downloadable notes
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-800 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Globe className="h-8 w-8" />
              </div>
              <h3 className="font-medium text-lg mb-2">Multilingual Support</h3>
              <p className="text-purple-200">Hindi and English content</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
