import { set, sub } from "date-fns";
import React, { useEffect, useState, useRef } from "react";
import { Fullscreen, Maximize, Minimize } from "lucide-react"; // Icon from Lucide
import TypingEffect from "./TypingEffect1"; // Custom Typing Effect Component
import { <PERSON>, <PERSON>, EyeOff } from "lucide-react";
import { use } from "react";
import { useLocation } from "react-router-dom";
class players {
  constructor(
    value,
    value1,
    value3,
    value4,
    value5,
    value6,
    value7,
    value8,
    value9,
    value10,
    value11
  ) {
    this.startingvalue = value;
    this.endingvalue = value1;
    this.image = value3;
    this.audio = value4;
    this.heading = value5;
    this.content = value6;
    this.summary = value7;
    this.mainImage = value8;
    this.duration = value1 - value;
    this.type = value9;
    this.ocr = value10;
    this.mappings = value11;
    this.prev = null;
    this.next = null;
  }

  setNext(next) {
    this.next = next;
  }
  setData(value, value1, value3, value4, value5, value6, value7, value8) {
    this.startingvalue = value;
    this.endingvalue = value1;
    this.image = value3;
    this.audio = value4;
    this.heading = value5;
    this.content = value6;
    this.summary = value7;
    this.mainImage = value8;
    this.duration = value1 - value;
  }
  getNext() {
    return this.next;
  }
  getPrev() {
    return this.prev;
  }
  getStartingValue() {
    return this.startingvalue;
  }
  getEndingValue() {
    return this.endingvalue;
  }
  traverse() {
    console.log("Traversing the linked list:");
    let current = this;
    while (current) {
      console.log("====================================");
      console.log(current.getStartingValue());
      console.log(current.getEndingValue());
      console.log(current.image);
      console.log(current.audio);
      console.log(current.heading);
      console.log(current.content);
      console.log(current.summary);
      console.log(current.mainImage);
      console.log(current.duration);
      console.log(current.type);
      console.log(current.ocr);
      console.log(current.mappings);
      console.log("====================================");
      current = current.getNext();
    }
  }
  finddata(value) {
    let current = this;
    while (current) {
      if (
        value >= current.getStartingValue() &&
        value <= current.getEndingValue()
      ) {
        return current;
      }
      current = current.getNext();
    }
    return null;
  }
}
function getRelativeTime(timestamp) {
  const now = Date.now();
  const diff = now - timestamp;

  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours   = Math.floor(minutes / 60);
  const days    = Math.floor(hours / 24);

  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  return `just now`;
}


async function imageUrlToBase64(imageUrl) {
  const response = await fetch(imageUrl);
  const blob = await response.blob();
  const reader = new FileReader();

  return new Promise((resolve, reject) => {
    reader.onloadend = () => {
      const base64 = reader.result.split(",")[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

async function getImgCategoryFromGemini(imageUrl, apiKey) {
  try {
    const base64Image = await imageUrlToBase64(imageUrl);

    const url = `https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
    const headers = {
      "Content-Type": "application/json",
    };

    const body = {
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `Classify This Image as any one of the following category:
  Considering the Majority Portion of the Image -
  1. Table
  2. Flowchart
  3. Diagram
  4. Graph
  5. Numericals or Math Equations
  6. Normal Image or Picture
  7. Text (Without Numericals or Math Equations)
  
  Return only the Category without any additional formating or text or headers`,
            },
            {
              inlineData: {
                mimeType: "image/png", // Adjust if not PNG
                data: base64Image,
              },
            },
          ],
        },
      ],
    };

    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return (
      result?.candidates?.[0]?.content?.parts?.[0]?.text || "No OCR found."
    );
  } catch (error) {
    return `Error: ${error.message}`;
  }
}

// Example usage:
const apiKey = "AIzaSyCQePMTO-2Ah0CqHBaHhG1uaDxMpZVWCnk";

let mydata = new players(-1, -1, "", "", "", "", "", "", "", "", []);
let copydata = mydata;
async function fetchData(item,lang) {
  //PLAY/PAUSE TRACK MOVEMENT AND SOME STYLING
  try {
    const response = await fetch("https://api.aiclassroom.in/generate-notes", {
      method: "POST",
      headers: {
        Connection: "keep-alive",
        "Content-Type": "application/json",
        Authorization:
          "Bearer 8wMnczIwpWUBKtBkcHDmUolWW6VydUgQd1ei21HNfMTJKXQm72ty70909wLlkFT_TYcn8ErnTULlSzzF8RveLw==",
      },
      body:  JSON.stringify({
        fileUrl: item,
        language: lang,
        gender: "Male",
        uid: "3456-22ded-2",
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("Response from API:", data);
    return data;
  } catch (error) {
    console.error("Error fetching data:", error);
  }
}
const NotesViewer = () => {
  const [mainImage, setMainImage] = useState("");
  const [keypoint, setKeypoint] = useState("");
  const [audioPath, setAudioPath] = useState("");
  const [sideImage, setSideImage] = useState("");
  const [towrite, setTowrite] = useState("");
  const [marker, setMarker] = useState([]);
  const [values, setvalues] = useState(0);
  const AudioRef = useRef(null);
  const [submit, setSubmit] = useState(false);
  const [link, setlink] = useState("");
  const [image, setimageset] = useState([]);
  const [toDisplay, setToDisplay] = useState(false);
  const [audioDirection, setAudioDirection] = useState(0);
  const [loaded, setLoaded] = useState(false);
  const [dataholder, setdataholder] = useState(mydata);
  const [trialdata, setTrialdata] = useState([]);
  const [data, setData] = useState(trialdata);
  const [ready, setReady] = useState(false);
  const [max, setmax] = useState(0);
  const [isplaying, setisplaying] = useState(false);
  const [currenttime, setcurrenttime] = useState(0);
  const [ocr, setOcr] = useState("");
  const [isVisible, setIsVisible] = useState(true);
  const [showLeft, setShowLeft] = useState(false);
  const [styler,setstyler]=useState({});

  const [lang, setlange] = useState("English");
  // const [selectedFile, setSelectedFile] = useState(null);
  const [showControls, setShowControls] = useState(false);
const ScrollRef=useRef(null);
  const location = useLocation();
  const selectedFILE = location.state?.selectedFile;
useEffect(() => {
  const timeout = setTimeout(() => {
    if(ScrollRef.current){

    ScrollRef.current.scrollTop = ScrollRef.current.scrollHeight;
    }
  }, 100);
  
  return () => clearTimeout(timeout);
},[styler])
  const handleUpload = () => {
    if (selectedFILE) {
      uploadPdf(selectedFILE);
    }
  };

  useEffect(() => {
    if (selectedFILE) {
      const timer = setTimeout(() => {
        handleUpload();
      }, 1000); // Delay of 1 second

      return () => clearTimeout(timer);
    }
  }, []);

  useEffect(() => {
    if (submit == false) return;

    setLoaded(false);
    (async () => {
      const response = await fetch(
        "https://api.aiclassroom.in/split-to-images",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ fileUrl:link }),
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      try {
        const pdfData = await response.json();
        console.log("PDF to Images Response:", pdfData);
        const alldata = [];

        let data1 = await fetchData(pdfData.data.Pdf_Pages_Data,lang);
        data1=data1.data
        let newdata = [];

        data1.json_data_final.forEach((jsonItem) => {
          const parsedItem = JSON.parse(jsonItem);
          newdata.push(JSON.parse(parsedItem));
        });

        console.log("Parsed data:", newdata);

        console.log("Trial data:", trialdata);
        setTrialdata(newdata);
      } catch (error) {
        console.log("Error parsing JSON:", error);
      }
      setLoaded(true);
    })();
    (async () => { })();
  }, [submit]);

  const uploadPdf = async (pdfFile) => {
    if (!pdfFile) {
      console.log("No file selected");
      return;
    }
    console.log(pdfFile);
    try {
      console.log("inside try");
      const formData = new FormData();
      formData.append("pdf", pdfFile);
      console.log("before response");
      const response = await fetch("https://api.aiclassroom.in/pdfUpload/upload", {
        method: "POST",
        body: formData,
      });
      console.log("after response");

      const result = await response.json();
      console.log("this is result ", result);

      if (result.success) {
        setlink(result.fileUrl);
      } else {
        throw new Error(result.message || "File upload failed");
      }
    } catch (err) {
      console.log(err.message);
    }
  };

  useEffect(() => {
    if (loaded == false) return;
    const playSegmentsSequentially = async () => {
      let startingNode = mydata;
      for (const item of trialdata) {
        for (const [key, value] of Object.entries(item)) {
          let typeofText = "";
          let data = "";
          let p = 0
          for (const [subKey, subValue] of Object.entries(value)) {
            if (Array.isArray(subValue)) {
              for (let i = 0; i < subValue.length; i++) {
                if (i == 0) {
                  if (p == 0) {
                    p += 1;
                    continue;
                  }
                  const addNode = new players(
                    startingNode.endingvalue + 1,
                    Math.ceil(startingNode.endingvalue + subValue[i][2]),
                    subValue[2],
                    subValue[i][1],
                    "Next Part",
                    subValue[i][0],
                    subValue[i][3],
                    subValue[1],
                    true,
                    subValue[i][0],
                    [])
                  startingNode.next = addNode;
                  addNode.prev = startingNode;
                  startingNode = addNode;
                }
                if (i === 1) {
                  console.log("Detected Image with Bounding box", subValue[i]);
                  setMainImage(subValue[i]);
                }
                if (i === 2) {
                  console.log("Cropped Image with Bounding box", subValue[i]);
                  typeofText = await getImgCategoryFromGemini(
                    subValue[i],
                    apiKey
                  ).then((data) => {
                    return data;
                  });
                  if (typeofText.includes("Text")) {
                    setToDisplay(false);
                    let sentence = subValue[3];
                    let firstKey = Object.keys(sentence)[0];
                    let firstValue = sentence[firstKey];
                    let trialpoints = "";
                    for (const [
                      summary,
                      audioUrl,
                      duration,
                      translation,
                      mappings,
                    ] of firstValue) {
                      trialpoints = trialpoints + translation + " ";
                    }
                    
                  } else {
                    setToDisplay(true);
                    setSideImage(subValue[i]);
                  }
                }
                if (i === 3) {
                  console.log("Raw OCR Text", subValue[i]);
                  setOcr(subValue[i]);
                }
                if (i === 4) {
                  const segmentsObj = subValue[i];

                  for (const [heading, segments] of Object.entries(
                    segmentsObj
                  )) {
                    setKeypoint(heading);
                    for (const [
                      summary,
                      audioUrl,
                      duration,
                      translation,
                      mappings,
                    ] of segments) {
                      if (typeofText.includes("Text")) {
                        const response = await fetch('https://api.abiv.in/prompt', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json'
                          },
                          body: JSON.stringify({
                            message: subValue[3],
                            prompt: 'Split the text Into different paragraphs using \n\n delimeter '
                          })
                        });
                        if (!response.ok) {
                          console.log("Error in splitting the text into paragraphs")
                        } else {
                          const data2 = await response.json();
                          subValue[3] = data2.output_text;
                          try {
                            const res1=await fetch("https://api.abiv.in/prompt", {  
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify({
                                message: translation,
                                prompt: 'Generate prompt for image generation using the following text: '
                              })
                          });
                          let img_gen=translation
                            if (!res1.ok) {
                              console.log("Error in generating the prompt")
                            } else {
                              const data1 = await res1.json();
                              img_gen = data1.output_text;
                            }
                            const imgRes = await fetch(
                              "https://api.aiclassroom.in/generate-image",
                              {
                                method: "POST",
                                headers: {
                                  "Content-Type": "application/json"
                                },
                                body: JSON.stringify({sentence:img_gen}),
                              }
                            );
                            data = await imgRes.json();
                            setSideImage(data.data.image);
                          } catch (e) {
                            console.error("Error fetching image:", e);
                          }
                        }

                      }
                      const addNode = new players(
                        startingNode.endingvalue + 1,
                        Math.ceil(startingNode.endingvalue + 1 + duration),
                        typeofText.includes("Text") ? data?.image : subValue[2],
                        audioUrl,
                        heading,
                        summary,
                        translation,
                        subValue[1],
                        !typeofText.includes("Text"),
                        subValue[3],
                        mappings
                      );
                      startingNode.next = addNode;
                      addNode.prev = startingNode;
                      startingNode = addNode;
                      setMarker(mappings);
                      setTowrite(summary);
                    }
                  }
                }
              }
            }
          }
        }
      }
      mydata = mydata.next;
      mydata.prev = null;
      setdataholder(mydata);
      mydata.traverse();
      setReady(true);
    };

    if (loaded) {
      playSegmentsSequentially();
    }
  }, [loaded]);

  const highlightText = (text, markers) => {
    let highlightedText = text;
    markers.forEach((marker) => {
      const normalizedMarker = normalizeText(marker);
      const regex = new RegExp(
        `(${normalizedMarker.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
        "gim"
      );
      highlightedText = highlightedText.replace(
        regex,
        (match) => `<span class="text-orange-500 font-medium">${match}</span>`
      );
    });
    return highlightedText;
  };

  const markParagraphs = (text, markers) => {
    const paragraphs = text.split("\n\n"); // Split text into paragraphs
    const markedParagraphs = paragraphs.map((paragraph) => {
      const normalizedParagraph = normalizeText(paragraph);
      const hasMarker = markers.some((marker) => {
        const normalizedMarker = normalizeText(marker);
        return normalizedParagraph.includes(normalizedMarker);
      });
      return hasMarker
        ? `<div class="bg-red-100 p-3 rounded-lg mb-2 border-l-4 border-red-400">${paragraph}</div>`
        : `<div class="mb-2">${paragraph}</div>`;
    });
    return (
      <span
        dangerouslySetInnerHTML={{ __html: markedParagraphs.join("\n\n") }}
      />
    );
  };

  function normalizeText(part) {
    return part
      .replace(/[^\S\r\n]+/g, " ") // collapse multiple spaces but preserve line breaks
      .replace(/[\u2018\u2019]/g, "'") // normalize single quotes
      .replace(/[\u201C\u201D]/g, '"') // normalize double quotes
      .replace(/–|—/g, "-") // normalize dashes
      .replace(/[^\x20-\x7E]/g, "") // remove non-ASCII/control characters
      .replace(/\s+/g, " ") // collapse all whitespace
      .trim();
  }

  // Format time for display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };
  const containerRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };
  return (
    <div className="min-h-screen bg-gray-50 font-sans overflow-x-hidden">
  {!submit ? (
    <div className="min-h-screen flex flex-col items-center bg-gradient-to-br from-blue-50 to-indigo-50 px-4 py-10">
      <div className="text-center mb-10">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          <span className="inline-block mr-2">📄</span>
          Notes Viewer
        </h1>
        <p className="text-gray-600">
          Transform your PDF notes into an interactive learning experience
        </p>
      </div>

      {link && (
        <div className="w-full max-w-xl bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex items-center text-green-600 mb-4">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            <span>PDF uploaded successfully</span>
          </div>

          <div className="mb-6">
            <iframe
              src={link}
              className="w-full h-96 border border-gray-200 rounded-lg"
              title="PDF Preview"
            ></iframe>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Choose output language:
            </label>
            <select
              onChange={(e) => setlange(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              {/* Your language options */}
              <option value="english">English</option>
              <option value="hindi">Hindi</option>
              <option value="tamil">Tamil</option>
              <option value="telugu">Telugu</option>
              <option value="kannada">Kannada</option>
              <option value="malayalam">Malayalam</option>
              <option value="bengali">Bengali</option>
              <option value="marathi">Marathi</option>
              <option value="gujarati">Gujarati</option>
              <option value="punjabi">Punjabi</option>
              <option value="urdu">Urdu</option>
              <option value="assamese">Assamese</option>
              <option value="odisha">Odia</option>
            </select>
          </div>
        </div>
      )}

      {link && (
        <button
          onClick={() => {
            setSubmit(true);
            setLoaded(false);
          }}
          className="px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium rounded-lg shadow-md hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105"
        >
          Start Learning 🚀
        </button>
      )}
    </div>
  ) : (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4 pb-40"> {/* Add bottom padding for audio bar */}
      {!loaded || !ready ? (
        <div className="flex justify-center items-center h-screen">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600 mb-4"></div>
            <p className="text-xl text-gray-700">Preparing your notes...</p>
          </div>
        </div>
      ) : (
        <div className="container mx-auto p-4 bg-[#f9f9f9] rounded-xl border-[10px] border-white shadow-[0_0_0_2px_#ccc,_inset_0_0_20px_#d9d9d9]" >
          <div className="relative flex flex-col md:flex-row gap-6" id="container">
            {/* Toggle Button */}
            <button
              onClick={() => setShowLeft(!showLeft)}
              className="absolute top-4 left-4 z-10 p-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 focus:outline-none"
            >
              {showLeft ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>

            {/* Left Panel */}
            {showLeft && (
              <div className="md:w-1/2 w-full bg-white rounded-xl p-4 shadow-lg border border-gray-100 relative">
                <button
                  onClick={() => setShowLeft(false)}
                  className="absolute top-2 right-2 text-gray-500 hover:text-red-500"
                  aria-label="Close"
                >
                  <X className="w-5 h-5" />
                </button>
                {dataholder.type ? (
                  <img
                    src={dataholder.mainImage}
                    alt="Main Visual"
                    className="w-full h-auto object-cover rounded-lg shadow-inner"
                  />
                ) : (
                  <div className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-4 rounded-lg shadow-inner border border-gray-200">
                    {dataholder?.ocr?.split(". ").map((part, index) => (
                      <div key={index} className="mb-2">
                        {markParagraphs(normalizeText(part), dataholder?.mappings)}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Right Panel */}
            <div className="flex-1 bg-white rounded-xl shadow-lg p-6 relative border border-gray-100 min-h-[500px] max-h-[800px] overflow-hidden">
              {/* Floating Image */}
              {!showLeft && dataholder.image && (
  <div className="absolute top-6 right-4 hidden md:block z-10">
    <img
      src={dataholder.image}
      alt="Reference Visual"
      className="w-[250px] h-full object-cover rounded-lg shadow-md border border-gray-200"
    />
  </div>
)}


              {showLeft && dataholder.image && (
                <div className="mb-6">
                  <div className="rounded-lg overflow-hidden shadow-md border border-gray-200">
                    <img
                      src={dataholder.image}
                      alt="Reference Visual"
                      className="w-full h-auto object-cover"
                    />
                  </div>
                </div>
              )}

              {/* Notes Content */}
              <div className="mb-6 overflow-auto max-h-[60vh] sm:max-h-[350px] min-h-[300px]" ref={ScrollRef}>
                {/* Sort and render sections */}
                {Object.entries(styler)
                  .sort((a, b) => a[1].updatedTime - b[1].updatedTime)
                  .map(([heading, value], index) => (
                    <div className={`text-gray-700 leading-relaxed mb-6 ${!showLeft ? "pr-0 md:pr-[36%]" : ""}`} key={index}>
                      <div className="flex justify-between items-center mb-1">
                        <h2 className="text-2xl font-bold text-gray-800">● {heading}</h2>
                        <span className="text-sm text-gray-400">{getRelativeTime(value.updatedTime)}</span>
                      </div>
                      <hr className="mb-2 border-gray-200" />
                      {value.content.map((content, i) => (
                        <div key={i} className="mb-2">➤ {content}</div>
                      ))}
                      
                    </div>
                  ))}
                {/* Fallback Content */}
                {Object.keys(styler).length <= 1 && (
                  <div className={`text-gray-700 leading-relaxed mb-6 ${!showLeft ? "pr-0 md:pr-[36%]" : ""}`}>
                    <div className="flex justify-between items-center mb-1">
                      <h2 className="text-2xl font-bold text-gray-800">
                        {styler[dataholder.heading] ? "" : "●" + dataholder.heading}
                      </h2>
                    </div>
                    <hr className="mb-2 border-gray-200" />
                    <div className="mb-2">
                      <TypingEffect content={dataholder.content} duration={dataholder.duration} color="black" />
                    </div>
                  </div>
                )}
                {Object.keys(styler).length > 1 && (
              <>
            <h2 className="text-2xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
              {styler[dataholder.heading]?"":"●"+dataholder.heading}
            </h2>
      
            {/* Typing Text */}
            <div
              className={`text-gray-700 leading-relaxed mb-6 `}
            >
              <TypingEffect
                content={dataholder.content}
                duration={dataholder.duration}
                color={"black"}
              />
            </div>
            </>)}
      
              </div>

              {/* Audio Controls */}
              <div className="fixed inset-x-0 bottom-0 w-full bg-gradient-to-t from-gray-900/80 to-transparent p-4 z-50">
                <div className="flex items-center mb-2">
                  <button
                    onClick={() => {
                      if (AudioRef.current) {
                        if (AudioRef.current.paused) {
                          AudioRef.current.play();
                          setisplaying(true);
                        } else {
                          AudioRef.current.pause();
                          setisplaying(false);
                        }
                      }
                    }}
                    className="p-3 bg-white rounded-full shadow-md text-indigo-600 hover:bg-indigo-50 focus:outline-none mr-3"
                  >
                    {!isplaying ? (
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </button>

                  <div className="flex-1">
                    <input
                      type="range"
                      min="0"
                      max={max}
                      value={values}
                      onChange={(e) => {
                        const val = Number(e.target.value);
                        setvalues(val);
                        AudioRef.current.currentTime = val - dataholder.startingvalue;
                        AudioRef.current.play();
                      }}
                      className="w-full h-2 bg-gray-300 rounded-lg cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-white mt-1">
                      <span>{formatTime(currenttime)}</span>
                      <span>{formatTime(dataholder.endingvalue)}</span>
                    </div>
                    <Fullscreen onClick={()=>{
                      document.getElementById("container").requestFullscreen();
                    }}></Fullscreen>
                  </div>
                </div>
              </div>

              {/* Audio Element */}
              <audio
                src={dataholder.audio}
                autoPlay
                onTimeUpdate={() => {
                  const current = AudioRef.current.currentTime + dataholder.startingvalue;
                  setcurrenttime(current);
                  setvalues(current);
                }}
                onEnded={() => {
                  if (dataholder.next) {
                    if (dataholder.next.endingvalue > max) {
                      setmax(dataholder.next.endingvalue);
                    }
                    if (styler[dataholder.heading]) {
                      const content = [...styler[dataholder.heading].content];
                      content.push(dataholder.content);
                      setstyler({
                        ...styler,
                        [dataholder.heading]: { content, updatedTime: Date.now() }
                      });
                    } else {
                      setstyler({
                        ...styler,
                        [dataholder.heading]: { content: [dataholder.content], updatedTime: Date.now() }
                      });
                    }
                    setdataholder(dataholder.next);
                  }
                }}
                controls={false}
                ref={AudioRef}
                className="hidden"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )}
</div>

  );
};
export default NotesViewer;
