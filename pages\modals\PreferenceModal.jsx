import React, { useState, useEffect } from 'react';

const languages = [
    'English',
    'Hindi',
];

function PreferenceModal({ onClose, onSubmit, initialTopic = '', initialLanguage = '' }) {
    const [topic, setTopic] = useState(initialTopic);
    const [language, setLanguage] = useState(initialLanguage);

    useEffect(() => {
        setTopic(initialTopic);
    }, [initialTopic]);

    useEffect(() => {
        setLanguage(initialLanguage);
    }, [initialLanguage]);

    const handleSubmit = (e) => {
        e.preventDefault();
        if (topic.trim() && language) {
            onSubmit({ topic, language });
        }
    };

    return (
        <div className="fixed inset-0 bg-[#F9FAFC] bg-opacity-30 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl w-full max-w-md">
                {/* Header */}
                <div className="flex items-center justify-between px-6 py-3 rounded-t-xl" style={{ background: 'linear-gradient(90deg, #1A39FF 0%, #A723FF 100%)' }}>
                    <span className="text-white font-medium">Select Your Preference</span>
                    <button onClick={onClose} className="text-white text-xl hover:text-gray-200">
                        &times;
                    </button>
                </div>
                <form className="px-6 py-8" onSubmit={handleSubmit}>
                    <div className="mb-5">
                        <label className="block text-gray-700 font-medium mb-2">Enter Your Topic</label>
                        <input
                            type="text"
                            className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-200"
                            placeholder="Type Your Topic"
                            value={topic}
                            onChange={e => setTopic(e.target.value)}
                            required
                        />
                    </div>
                    <div className="mb-8">
                        <label className="block text-gray-700 font-medium mb-2">Choose Your Preferred Language</label>
                        <select
                            className="w-full border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-200 bg-white"
                            value={language}
                            onChange={e => setLanguage(e.target.value)}
                            required
                        >
                            <option value="" disabled>Select Language</option>
                            {languages.map(lang => (
                                <option key={lang} value={lang}>{lang}</option>
                            ))}
                        </select>
                    </div>
                    <button
                        type="submit"
                        className="w-full py-3 rounded-lg text-white font-semibold text-base shadow-md transition bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                    >
                        Start Interview
                    </button>
                </form>
            </div>
        </div>
    );
}

export default PreferenceModal; 