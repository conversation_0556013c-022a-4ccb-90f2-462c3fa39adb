import React from "react";

const RefundPolicy = () => {
    return (
        <div className="bg-white min-h-screen flex items-center justify-center px-4 py-10">
            <div className="w-full max-w-5xl max-h-[90vh] overflow-hidden rounded-2xl bg-white/20 backdrop-blur-md border border-white/30 shadow-2xl p-8">
                <h1 className="text-4xl font-bold text-center text-gray-900 mb-6">
                    Refund Policy
                </h1>
                <div className="overflow-y-auto h-[70vh] pr-4 text-gray-800 text-base leading-relaxed space-y-6">
                    <p>
                        This refund and cancellation policy outlines how you can cancel or seek a refund for your <strong>AI Classroom software subscription</strong> purchased through our platform.
                    </p>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">1. Cancellations</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Requests will only be considered if made within <strong>2 days</strong> of subscribing.</li>
                            <li>Cancellation may not be possible if the account has been activated or accessed.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">2. Non-Refundable Subscriptions</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Services marked as <strong>non-refundable</strong> cannot be refunded under any circumstances.</li>
                            <li>If a service is defective or misrepresented, a refund may be considered.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">3. Defective Service</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Report technical issues to our <strong>customer service team</strong> within <strong>2 days</strong> of the problem.</li>
                            <li>Our technical team will validate the issue and may offer a refund or resolution.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">4. Unsatisfactory Service Experience</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Notify <strong>customer service</strong> within <strong>2 days</strong> if the service doesn't meet expectations.</li>
                            <li>We may issue adjustments or a refund depending on the case.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">5. Refund Processing</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Refunds approved by <strong>RNPSOFT PRIVATE LIMITED</strong> will be processed within <strong>7 days</strong> to the original payment method.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">6. Third-Party Integrations</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>If AI Classroom integrates with third-party tools, related issues must be resolved with those providers as per their own policies.</li>
                        </ul>
                    </div>

                    <p className="text-sm text-center text-gray-600 mt-4">
                        By subscribing to AI Classroom, you agree to abide by this refund policy.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default RefundPolicy;
