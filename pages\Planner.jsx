
import { useState } from "react"
import "./Planner.css"

const Planner = () => {
  const [activeTab, setActiveTab] = useState("Current Plan")

  const tabs = ["Current Plan", "History"]

  const activePlan = {
    title: "Introduction to Machine Learning",
    dateRange: "May 15 - June 10, 2023",
    hoursPerDay: 2,
    progress: 42,
  }

  const upcomingTasks = [
    {
      id: 1,
      title: "Watch Lecture 2: Supervised Learning",
      type: "video",
      time: "Tomorrow • 45 min",
    },
    {
      id: 2,
      title: "Complete Quiz 1",
      type: "assessment",
      time: "Tomorrow • 30 min",
    },
    {
      id: 3,
      title: "Read Chapter 1 of textbook",
      type: "reading",
      time: "Tomorrow • 45 min",
    },
    {
      id: 4,
      title: "Practice coding exercises",
      type: "practice",
      time: "Day 3 • 1 hour",
    },
  ]

  return (
    <div className="planner-container">
      <div className="planner-header">
        <h2>Study Planner</h2>
        <button className="primary-button">New Plan</button>
      </div>

      <div className="planner-tabs">
        {tabs.map((tab) => (
          <button key={tab} className={`tab ${activeTab === tab ? "active" : ""}`} onClick={() => setActiveTab(tab)}>
            {tab}
          </button>
        ))}
      </div>

      <div className="active-plan-card">
        <h3 className="plan-title">Active Study Plan: {activePlan.title}</h3>
        <p className="plan-date">
          {activePlan.dateRange} • {activePlan.hoursPerDay} hours/day
        </p>

        <div className="progress-section">
          <h4>Overall Progress</h4>
          <div className="progress-bar-container">
            <div className="progress-bar">
              <div className="progress-fill" style={{ width: `${activePlan.progress}%` }}></div>
            </div>
            <span className="progress-percentage">{activePlan.progress}% Complete</span>
          </div>
        </div>

        <div className="tasks-section">
          <h4>Upcoming Tasks</h4>
          <div className="tasks-list">
            {upcomingTasks.map((task) => (
              <div key={task.id} className="task-item">
                <div className={`task-indicator ${task.type}-indicator`}></div>
                <div className="task-content">
                  <h5 className="task-title">{task.title}</h5>
                  <p className="task-time">{task.time}</p>
                </div>
                <div className={`task-type ${task.type}-type`}>
                  {task.type === "video" && "Video"}
                  {task.type === "assessment" && "Assessment"}
                  {task.type === "reading" && "Reading"}
                  {task.type === "practice" && "Practice"}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Planner