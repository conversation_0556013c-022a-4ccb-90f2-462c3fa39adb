// Top imports (unchanged)
import * as SpeechSDK from "microsoft-cognitiveservices-speech-sdk";
import { createAvatarSynthesizer, createWebRTCConnection } from "./Utility";
import { avatarAppConfig } from "./config";
import { useEffect, useRef, useState } from "react";
import OpenA<PERSON> from "openai";
import SpeechRecognition, { useSpeechRecognition } from "react-speech-recognition";
import 'regenerator-runtime/runtime';

// OpenAI setup
const openai = new OpenAI({
  apiKey: "********************************************************************************************************************************************************************",
  dangerouslyAllowBrowser: true,
});

export default function Avatar() {
  const [avatarSynthesizer, setAvatarSynthesizer] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [status, setStatus] = useState("Click 'Start Interview' to begin.");
  const [interviewStarted, setInterviewStarted] = useState(false);
  const [useMic, setUseMic] = useState(true);
  const [textAnswer, setTextAnswer] = useState("");
  const [interviewActive, setInterviewActive] = useState(false);
  const [language, setLanguage] = useState("");
  const [topic, setTopic] = useState("");
  const [showModal, setShowModal] = useState(true);
  const [manualTranscript, setManualTranscript] = useState("");
  const [isManuallyListening, setIsManuallyListening] = useState(false);
  const [isLandscape, setIsLandscape] = useState(true);

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();

  const avatarVideoRef = useRef();
  const avatarAudioRef = useRef();
  const userVideoRef = useRef();

  useEffect(() => {
    const checkOrientation = () => setIsLandscape(window.innerWidth > window.innerHeight);
    checkOrientation();
    window.addEventListener("resize", checkOrientation);
    return () => window.removeEventListener("resize", checkOrientation);
  }, []);

  useEffect(() => {
    if (!browserSupportsSpeechRecognition) {
      setUseMic(false);
      setStatus("⚠️ Speech recognition not supported. Using text input.");
    }
  }, [browserSupportsSpeechRecognition]);

  const requestMicAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Close stream after permission
      return true;
    } catch (err) {
      console.error("Mic permission denied:", err);
      setStatus("❌ Mic access denied.");
      return false;
    }
  };

  const handleStartListening = async () => {
    if (!browserSupportsSpeechRecognition) {
      setStatus("❌ Browser doesn't support speech recognition.");
      return;
    }

    const allowed = await requestMicAccess();
    if (!allowed) return;

    resetTranscript();
    setManualTranscript("");
    SpeechRecognition.startListening({
      continuous: true,
      language: language === "hindi" ? "hi-IN" : "en-US",
    });
    setIsManuallyListening(true);
    setStatus("🎤 Listening... (click Stop when done)");
  };

  const handleStopListening = async () => {
    SpeechRecognition.stopListening();
    setIsManuallyListening(false);
    const spokenText = transcript.trim();
    if (spokenText) {
      setManualTranscript(spokenText);
      setStatus(`📝 Recognized: ${spokenText}`);
      const nextQuestion = await askOpenAI(spokenText);
      setChatHistory((prev) => [...prev, { role: "user", content: spokenText }]);
      await speakText(nextQuestion);
    } else {
      setStatus("⚠️ No speech detected.");
    }
  };

  const handleOnTrack = (event) => {
    if (event.track.kind === "video") {
      avatarVideoRef.current.srcObject = event.streams[0];
    } else if (event.track.kind === "audio") {
      avatarAudioRef.current.srcObject = event.streams[0];
      avatarAudioRef.current.muted = false;
      avatarAudioRef.current.play().catch(() => {});
    }
  };

  const startSession = async () => {
    const allowed = await requestMicAccess();
    if (!allowed) return;
    await startUserCamera();

    const pc = createWebRTCConnection(
      avatarAppConfig.iceUrl,
      avatarAppConfig.iceUsername,
      avatarAppConfig.iceCredential
    );
    pc.ontrack = handleOnTrack;
    pc.addTransceiver("video", { direction: "sendrecv" });
    pc.addTransceiver("audio", { direction: "sendrecv" });

    const avatar = createAvatarSynthesizer({
      voice: language === "hindi" ? "hi-IN-SwaraNeural" : "en-IN-NeerjaNeural",
    });
    await avatar.startAvatarAsync(pc);
    setAvatarSynthesizer(avatar);
    setInterviewStarted(true);
    setStatus("Interview started.");
  };

  const startUserCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      userVideoRef.current.srcObject = stream;
    } catch (err) {
      console.error("Camera error:", err);
    }
  };

  const speakText = (text) => {
    return new Promise((resolve, reject) => {
      if (!avatarSynthesizer) return reject("Avatar not ready.");
      avatarSynthesizer.speakTextAsync(
        text,
        (result) => {
          if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) resolve();
          else reject("Synthesis failed.");
        },
        (error) => reject(error)
      );
    });
  };

  const listenAnswer = () => {
    return new Promise(async (resolve, reject) => {
      if (!browserSupportsSpeechRecognition) return reject("Not supported.");

      const allowed = await requestMicAccess();
      if (!allowed) return;

      resetTranscript();
      SpeechRecognition.startListening({
        continuous: false,
        language: language === "hindi" ? "hi-IN" : "en-US",
      });

      setStatus("🎙️ Listening...");

      setTimeout(() => {
        SpeechRecognition.stopListening();
        const final = transcript.trim();
        if (!final) {
          setStatus("⚠️ No speech detected.");
          return reject("No speech detected");
        }
        resolve(final);
      }, 8000);
    });
  };

  const askOpenAI = async (input) => {
    const updatedHistory = [
      {
        role: "system",
        content: language === "hindi"
          ? `आप एक पेशेवर साक्षात्कारकर्ता हैं जो \"${topic}\" विषय पर साक्षात्कार ले रहा है। कृपया उपयोगकर्ता से एक-एक करके गहरे और उपयुक्त प्रश्न पूछें।`
          : `You are a professional interviewer. Ask one question at a time about \"${topic}\".`,
      },
      ...chatHistory,
      { role: "user", content: input },
    ];
    const response = await openai.chat.completions.create({ model: "gpt-4", messages: updatedHistory });
    const reply = response.choices[0].message.content;
    setChatHistory([...updatedHistory, { role: "assistant", content: reply }]);
    return reply;
  };

  const runInterview = async () => {
    try {
      setInterviewActive(true);
      const intro = language === "hindi"
        ? "AI साक्षात्कार मोड में आपका स्वागत है..."
        : "Welcome to AI Interview Mode. I am your interviewer Neerja. Shall we begin?";
      await speakText(intro);
      setChatHistory([{ role: "system", content: `Topic: ${topic}` }]);
      while (true) {
        setStatus("Waiting for response...");
        const userInput = useMic ? await listenAnswer() : textAnswer;
        if (!userInput) break;
        const nextQ = await askOpenAI(userInput);
        setTextAnswer("");
        await speakText(nextQ);
      }
      setInterviewActive(false);
      setStatus("Interview ended.");
    } catch (err) {
      console.error("Interview error:", err);
      setStatus("Interview failed: " + err.message);
    }
  };

  useEffect(() => {
    if (avatarSynthesizer && interviewStarted && !interviewActive) runInterview();
  }, [avatarSynthesizer, interviewStarted]);

  const continueInterview = async () => {
    if (!textAnswer) return;
    const nextQ = await askOpenAI(textAnswer);
    setTextAnswer("");
    await speakText(nextQ);
  };

  const handleModalSubmit = () => {
    if (language && topic) setShowModal(false);
  };

  // Keep all your UI logic (modal, video, buttons, etc.) same as before.

 return (
  <div className="relative font-sans antialiased text-gray-900 bg-white min-h-screen">
    {showModal && (
      <div className="fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50">
        <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-md space-y-6">
          <h2 className="text-3xl font-extrabold text-center text-blue-700">
            Interview Setup
          </h2>

          <div>
            <label htmlFor="language" className="block text-xl font-semibold mb-2 text-gray-700">
              Language:
            </label>
            <select
              id="language"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="w-full bg-gray-50 border border-gray-300 text-gray-900 py-3 px-4 rounded focus:outline-none"
            >
              <option value="">Select Language</option>
              <option value="english">English (Neerja)</option>
            </select>
          </div>

          <div>
            <label htmlFor="topic" className="block text-xl font-semibold mb-2 text-gray-700">
              Interview Topic:
            </label>
            <input
              id="topic"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="e.g., Taj Mahal"
              className="w-full py-3 px-4 border rounded"
            />
          </div>

          <button
            onClick={handleModalSubmit}
            disabled={!language || !topic}
            className="w-full bg-blue-700 hover:bg-blue-900 text-white py-4 px-8 rounded-full disabled:opacity-50"
          >
            Begin Interview
          </button>
        </div>
      </div>
    )}

    <div className="container mx-auto px-5 py-10">
      <h1 className="text-5xl font-extrabold text-center mb-10 text-blue-700">AI Interview Avatar</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
        <div className="relative rounded-3xl overflow-hidden shadow-xl max-h-[150vh]">
  <video
    ref={avatarVideoRef}
    className="w-full h-full object-cover bg-black"
    autoPlay
    muted
  />
  <audio ref={avatarAudioRef} />
  {!showModal && browserSupportsSpeechRecognition && (
  <div className="absolute top-4 right-4 z-50">
    <button
      onClick={isManuallyListening ? handleStopListening : handleStartListening}
      className={`text-white bg-blue-700 hover:bg-blue-900 rounded-full p-3 shadow-lg transition-all duration-200 ${
        isManuallyListening ? 'bg-red-600 hover:bg-red-800' : ''
      }`}
    >
      {isManuallyListening ? '⏹️' : '🎙️'}
    </button>
  </div>
  )}
  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-6">
    <strong>Interviewer:</strong> {chatHistory.findLast(m => m.role === "assistant")?.content || "..."}
  </div>

  {!isLandscape &&  (
    <div className="absolute bottom-4 right-4 w-24 h-36 rounded-xl overflow-hidden border-2 border-white shadow-lg z-40 bg-black">
      <video ref={userVideoRef} className="w-full h-full object-cover" autoPlay muted />
    </div>
  )}
</div>

        <div className="space-y-6">
          {isLandscape && (

          <div className="relative w-full h-80 rounded-3xl overflow-hidden shadow-xl">
            <video
              ref={userVideoRef}
              className="w-full h-full object-cover bg-black"
              autoPlay
              muted
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-90 text-white p-2">
              <strong>You:</strong> {chatHistory.findLast(m => m.role === "user")?.content || "..."}
            </div>
          </div>
          )}

          <div className="flex flex-col sm:flex-row sm:items-center gap-6">
            {!interviewStarted && (
            <button
              onClick={startSession}
              disabled={interviewStarted}
              className="flex-grow bg-green-600 hover:bg-green-800 text-white py-4 px-8 rounded-full disabled:opacity-50"
            >
              Start Interview
            </button>              )}


          </div>

          
            <div className="mt-8 flex gap-4">
              <input
                value={textAnswer}
                onChange={(e) => setTextAnswer(e.target.value)}
                placeholder="Type your answer..."
                className="w-full py-4 px-6 border rounded"
              />
              <button
                onClick={continueInterview}
                className="bg-blue-700 hover:bg-blue-900 text-white py-4 px-8 rounded-full"
              >
                Submit
              </button>
            </div>
          


          <p className="text-sm italic text-gray-600">{status}</p>
        </div>
      </div>
    </div>
  </div>
);

}
