import { useState } from "react";
import { API_URLS } from "./constants";

export const useUploadPdf = () => {
    const [uploading, setUploading] = useState(false);
    const [fileUrl, setFileUrl] = useState(null);
    const [error, setError] = useState(null);

    const uploadPdf = async (pdfFile) => {
        if (!pdfFile) {
            setError("No file selected");
            return;
        }

        setUploading(true);
        setError(null);

        try {
            const formData = new FormData();
            formData.append("pdf", pdfFile);

            const response = await fetch(API_URLS.PDF_UPLOAD_API, {
                method: "POST",
                body: formData,
            });

            const result = await response.json();

            if (result.success) {
                setFileUrl(result.fileUrl);
            } else {
                throw new Error(result.message || "File upload failed");
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setUploading(false);
        }
    };

    return { uploadPdf, uploading, fileUrl, error ,setFileUrl};
};
