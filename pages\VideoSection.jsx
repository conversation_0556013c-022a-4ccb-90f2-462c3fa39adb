import { useState, useEffect } from 'react';
import { IoMdClose } from "react-icons/io";
import { FaCloudDownloadAlt } from "react-icons/fa";
import { IoSend } from "react-icons/io5";
import { IoMdDownload } from "react-icons/io";
import { MdDownloadForOffline } from "react-icons/md";
import { FaMicrophone } from "react-icons/fa";
import axios from 'axios';
import { jsPDF } from 'jspdf';
import { RiChatDeleteFill } from "react-icons/ri";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import { useNavigate } from 'react-router-dom';
import logoBase64 from './logoBase64';

const mockData = {
    lesson: {
        day: 'Day 1',
        date: '5/6/2023',
        courseTitle: 'title',
        lessonTitle: 'Lesson ',
        videoUrl: '',
        isMarkedDone: false
    },
    notes: {
        keyConcepts: [
            'Particles can exhibit wave-like properties and waves can exhibit particle-like properties',
            'The de Broglie wavelength reveals a particle\'s momentum in its wavelength',
            'The double-slit experiment demonstrates quantum superposition',
            '<PERSON><PERSON><PERSON>\'s uncertainty principle limits simultaneous knowledge of position and momentum'
        ],
        summary: 'Wave-particle duality is a fundamental concept in quantum mechanics that states every particle or quantum entity may be described as either a particle or a wave. This challenges classical concepts where particles and waves were distinct phenomena. The concept emerged from observations that light could behave as both waves (interference patterns) and particles (photons) and was later extended to all matter.'
    },
    transcript: [
        { timestamp: '[00:00]', text: 'Welcome to today\'s lesson on wave-particle duality...' },
        { timestamp: '[00:30]', text: 'Let\'s start by understanding what we mean by particles and waves...' },
        { timestamp: '[01:15]', text: 'The famous double-slit experiment demonstrates this phenomenon...' }
    ],
    resources: [
        { title: 'Interactive Double-Slit Experiment Simulation', url: '#' },
        { title: 'Quantum Mechanics Textbook - Chapter 3', url: '#' },
        { title: 'Practice Problems: Wave-Particle Duality', url: '#' }
    ],
    chat: [
        {
            id: 1,
            type: 'assistant',
            content: "Hello! I'm your learning assistant! I can help you with questions, answer your questions, or provide examples. Just ask me!"
        },
        {
            id: 2,
            type: 'user',
            content: ""
        },
        {
            id: 3,
            type: 'assistant',
            content: ""
        }
    ]
};

const VideoSection = () => {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('transcript');
    const [isMarkedDone, setIsMarkedDone] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isTranscriptLoading, setIsTranscriptLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isMessageLoading, setIsMessageLoading] = useState(false);
    const [wbStrId, setWbStrId] = useState(null);
    const [topicId, setTopicId] = useState(null);
    const [summary, setSummary] = useState('');
    const [transcript, setTranscript] = useState('');
    const [videoDuration, setVideoDuration] = useState('medium');
    const [videoList, setVideoList] = useState([]);
    const [currentVideoIndex, setCurrentVideoIndex] = useState(0);

    // State for all the data that will be fetched from API
    const [lessonData, setLessonData] = useState({
        day: 'Day 1',
        date: '5/6/2023',
        courseTitle: 'Introduction to Quantum Mechanics',
        lessonTitle: 'Lesson 3: Wave-Particle Duality',
        videoUrl: '#',
    });

    const [notesData, setNotesData] = useState({
        keyConcepts: [],
        summary: ''
    });

    const [transcriptData, setTranscriptData] = useState([]);

    const [resourcesData, setResourcesData] = useState([]);

    const [chatMessages, setChatMessages] = useState(() => {
        const saved = localStorage.getItem('videoSectionChatMessages');
        return saved ? JSON.parse(saved) : mockData.chat;
    });
    const [newMessage, setNewMessage] = useState('');

    // Speech recognition setup
    const {
        transcript: speechTranscript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition
    } = useSpeechRecognition();

    // When speechTranscript changes, update the input field
    useEffect(() => {
        if (speechTranscript && listening) {
            setNewMessage(speechTranscript);
        }
    }, [speechTranscript, listening]);

    // Mic button handler
    const handleMicClick = () => {
        if (!browserSupportsSpeechRecognition) {
            alert('Your browser does not support speech recognition.');
            return;
        }
        if (listening) {
            SpeechRecognition.stopListening();
        } else {
            resetTranscript();
            SpeechRecognition.startListening({ continuous: true, language: 'en-IN' });
        }
    };

    const getDataFromLocalStorage = (wbStrId, topicId, videoDuration) => {
        setWbStrId(wbStrId);
        setTopicId(topicId);
        setVideoDuration(videoDuration);
    }

    useEffect(() => {
        console.log("wbStrId from local storage :", localStorage.getItem("wbStrId"));
        console.log("topicId from local storage :", localStorage.getItem("topicId"));

        getDataFromLocalStorage(localStorage.getItem("wbStrId"), localStorage.getItem("topicId"), localStorage.getItem("videoDuration"));
    }, [wbStrId, topicId, videoDuration]);

    useEffect(() => {
        console.log("chat---", chatMessages);
    }, [chatMessages]);

    // Persist chatMessages to localStorage whenever it changes
    useEffect(() => {
        localStorage.setItem('videoSectionChatMessages', JSON.stringify(chatMessages));
    }, [chatMessages]);

    const downloadChatAsPDF = () => {
        const doc = new jsPDF({ unit: 'pt' }); // Use points for units
        const pageWidth = doc.internal.pageSize.getWidth(); // Width of the page in points
        const margin = 32; // Margin in points (approx 0.5 inch as 1 inch = 72 points)
        const topDocMargin = 32; // Top margin for the document content in points
        let yPosition = topDocMargin; // Initial Y position for drawing

        const contentMaxWidth = pageWidth - (2 * margin); // Max width for text content within margins

        // Get current date and time in a nicely formatted string
        const now = new Date();
        const downloadDateTime = now.toLocaleDateString('en-GB') + ' ' +
            now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

        // --- Header Section ---
        // "Topic Tile Fetched" - larger and bold
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(16);
        // Assuming lessonData.lessonTitle is available in scope
        // Replace with a placeholder if lessonData is not defined in your actual context for testing
        const lessonTitle = typeof lessonData !== 'undefined' ? lessonData.lessonTitle : "Chat Topic";
        doc.text(`${lessonTitle}`, margin, 25);

        // AI Classroom text
        const aiClassroomFontSize = 12;
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(aiClassroomFontSize);

        // ------- Add logo at the top right corner ---------
        const logoWidth = 112;//140
        const logoHeight = 30;//40
        // Calculate X position for the logo to be at the top right
        const logoX = pageWidth - margin - logoWidth;

        doc.addImage(logoBase64, 'PNG', logoX, topDocMargin - 20, logoWidth, logoHeight); // Adjust '10' as needed

        // Positioned at topDocMargin

        // yPosition was topDocMargin (36)
        // This yPosition update was for spacing after AI Classroom text in the original logic flow
        yPosition += 18 * 1.2; // Current yPosition = 36 + 21.6 = 57.6

        // Date and time of download
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text(` ${downloadDateTime}`, margin, 45); // Fixed position for date

        // Space after the entire header block before chat content begins
        // The largest y used in header was for AI Classroom text line (yPosition was 57.6) or date (45)
        // Original code used yPosition for AI classroom, then incremented it, then placed date at fixed 45.
        // Let's ensure yPosition is correctly set after header.
        // If AI Classroom text was at topDocMargin (36), and date at 45, title at 25.
        // The next yPosition update in original: yPosition += (10 * 1.2) * 2;
        // This should be relative to the bottom of the header elements.
        // Let's set yPosition to a safe start after header:
        yPosition = topDocMargin + (10 * 1.2) * 2; // A common starting point after a header block. Max of (25, 36 for AI Class, 45 for Date) + spacing.
        // Let's use a simpler, more robust way:
        yPosition = 60; // A fixed start after the header elements placed at 25, 36, 45. Adjust as needed.


        // --- Chat Messages Section ---
        const chatFontSize = 10;
        // This is the original chatLineHeight, crucial for the 'else' branch.
        const originalChatLineHeight = chatFontSize * 3;

        const splitTextIntoLines = (text, maxWidthForSplit) => {
            return doc.splitTextToSize(text, maxWidthForSplit);
        };

        // Assuming chatMessages is available in scope
        // Replace with placeholder if chatMessages is not defined for testing
        const exampleChatMessages = [
            { type: 'system', content: 'Session started' }, // Example: will be filtered out by index !== 0
            { type: 'user', content: 'Hello! How can I assist you today?' },
            { type: 'assistant', content: 'Quantum mechanics is a fascinating branch of physics that deals with the behavior of very small particles like atoms and subatomic particles. It explores how these particles can exist in multiple states at once and how they can exhibit both wave-like and particle-like properties. If you have any specific questions, feel free to ask!' },
            { type: 'user', content: 'Thanks! What is superposition?' },
            { type: 'assistant', content: 'Superposition is a fundamental principle of quantum mechanics. It states that, much like waves in classical physics, any two (or more) quantum states can be added together ("superposed") and the result will be another valid quantum state.' },
            { type: 'user', content: 'Interesting.' }
        ];
        const messagesSource = typeof chatMessages !== 'undefined' ? chatMessages : exampleChatMessages;

        const messagesToDisplay = messagesSource.filter((message, index) => {
            return index !== 0 && message.content && message.content.trim() !== '';
        });

        const boxPadding = 8; //
        const textLineHeightInBox = chatFontSize * 2; //

        for (let i = 0; i < messagesToDisplay.length; i++) {
            let message = messagesToDisplay[i];
            let nextMessage = (i + 1 < messagesToDisplay.length) ? messagesToDisplay[i + 1] : null;

            doc.setFont('helvetica', 'normal'); // Reset default font for each block
            doc.setFontSize(chatFontSize);

            if (message.type === 'user' && nextMessage && nextMessage.type === 'assistant') {

                const prefixUser = 'Me : ';
                const userContent = prefixUser + message.content;

                const userLines = splitTextIntoLines(userContent, contentMaxWidth - (2 * boxPadding));

                const prefixAI = 'AI : ';
                const aiContent = prefixAI + nextMessage.content;
                const aiLines = splitTextIntoLines(aiContent, contentMaxWidth - (2 * boxPadding));

                const userTextHeight = userLines.length * textLineHeightInBox;
                const aiTextHeight = aiLines.length * textLineHeightInBox;

                let spaceBetweenUserAndAIInBox = 0;
                if (userLines.length > 0 && aiLines.length > 0) {
                    spaceBetweenUserAndAIInBox = textLineHeightInBox * 0.6; // Gap between Me and AI text within the box
                }

                const contentHeightInBox = userTextHeight + spaceBetweenUserAndAIInBox + aiTextHeight;
                const boxHeight = contentHeightInBox + (3 * boxPadding); // Total height of the box

                // Page break check for the entire box
                if (yPosition + boxHeight > doc.internal.pageSize.getHeight() - topDocMargin) {
                    doc.addPage();
                    yPosition = topDocMargin;
                }

                // Draw the box
                doc.setDrawColor(200, 200, 200); // Light grey border for the box
                doc.setFillColor(248, 248, 248); // Very light grey fill for the box
                doc.rect(margin, yPosition, contentMaxWidth, boxHeight, 'FD'); // Fill and Draw

                // Render text inside the box
                // Initial baseline for the first line of text inside the box
                let yTextInBox = yPosition + boxPadding + chatFontSize;

                userLines.forEach(line => {
                    doc.text(line, margin + boxPadding, yTextInBox);
                    yTextInBox += textLineHeightInBox;
                });

                // If there was user text and there will be AI text, add the specific gap
                if (userLines.length > 0 && aiLines.length > 0) {
                    yTextInBox += spaceBetweenUserAndAIInBox;
                }

                aiLines.forEach(line => {
                    doc.text(line, margin + boxPadding, yTextInBox);
                    yTextInBox += textLineHeightInBox;
                });

                yPosition += boxHeight + (textLineHeightInBox * 0.75); // Move yPosition down by box height + some margin after the box

                i++; // Increment i because we've processed the nextMessage as well
            } else {
                // --- ORIGINAL MESSAGE RENDERING (SINGLE MESSAGE) ---
                // This part adheres to "don't change anything else" using originalChatLineHeight.
                doc.setFont('helvetica', 'normal');
                doc.setFontSize(chatFontSize);

                const prefix = message.type === 'user' ? 'Me : ' : 'AI : ';
                const fullTextMessage = prefix + message.content;
                const textLines = splitTextIntoLines(fullTextMessage, contentMaxWidth); // Use full contentMaxWidth

                // Original block-level page break estimation
                const originalBlockEstimatedHeight = textLines.length * originalChatLineHeight;
                const originalGapAfterAiMessage = (message.type === 'assistant' && (i + 1) < messagesToDisplay.length) ? originalChatLineHeight : 0;
                const originalTotalSpaceNeededForBlock = originalBlockEstimatedHeight + originalGapAfterAiMessage;

                if (textLines.length > 0 && (yPosition + originalTotalSpaceNeededForBlock) > (doc.internal.pageSize.getHeight() - topDocMargin) && yPosition !== topDocMargin) {
                    doc.addPage();
                    yPosition = topDocMargin;
                }

                // Original line-by-line rendering with its per-line page break logic
                textLines.forEach((line) => {
                    // If the space for the current line (using originalChatLineHeight) would overflow
                    if ((yPosition + originalChatLineHeight) > (doc.internal.pageSize.getHeight() - topDocMargin) && yPosition !== topDocMargin) {
                        doc.addPage();
                        yPosition = topDocMargin;
                    }
                    doc.text(line, margin, yPosition); // yPosition is the baseline
                    yPosition += originalChatLineHeight; // Increment to next baseline using original large spacing
                });

                // Original gap logic after an AI message
                if (message.type === 'assistant' && (i + 1) < messagesToDisplay.length) {
                    // The original code just added this. If it caused overflow,
                    // the *next* message's block check would handle it.
                    yPosition += originalChatLineHeight;
                }
            }
        }

        doc.save(`chat_export_${now.toISOString().split('T')[0]}.pdf`);
    };
    const fetchSummary = async () => {
        if (!lessonData.videoUrl) return;

        setIsTranscriptLoading(true);
        console.log('Fetching summary for...', lessonData.videoUrl);

        const url = 'http://localhost:8000/api/v1/notesAndImages/getTranscript/';

        try {
            const response = await axios.put(url, {
                videoURL: lessonData.videoUrl
            });
            console.log('responce---', response);

            console.log("Fetching transcription for video:", lessonData.videoUrl);
            setTranscript(response.data.data.Transcription_Data || 'No transcript available.');
            console.log("Transcription response:", response.data);
        } catch (error) {
            console.error('Error fetching transcript:', error);
            setTranscript('Unable to load transcript. Please try again later.');
        } finally {
            setIsTranscriptLoading(false);
        }
    };

    // Update fetchYoutubeData with better error handling
    useEffect(() => {
        const fetchYoutubeData = async () => {
            setIsLoading(true);
            setError(null); // Clear any previous errors

            if (!wbStrId || !topicId || !videoDuration) {
                setIsLoading(false);
                return;
            }

            const url = `http://localhost:8000/api/v1/videoData/generateVideoLink/${wbStrId}/${topicId}/${videoDuration}`;
            try {
                const response = await axios.get(url);
                console.log('Youtube Data:', response.data);

                if (response.data?.data?.videoList?.length > 0) {
                    const videos = response.data.data.videoList;
                    setVideoList(videos);

                    // Set initial video data
                    const firstVideo = videos[0];
                    setCurrentVideoIndex(0);
                    setLessonData(prev => ({
                        ...prev,
                        videoUrl: firstVideo.videoURL,
                        courseTitle: response.data.data.topicName,
                        lessonTitle: response.data.data.topicName,
                        date: response.data.data.date
                    }));

                    setNotesData(prev => ({
                        ...prev,
                        summary: firstVideo.description
                    }));
                } else {
                    setError('No videos available for this lesson.');
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                const errorMessage = error.response?.data?.message ||
                    error.message ||
                    'Failed to fetch video data. Please try again later.';
                setError(errorMessage);
            } finally {
                setIsLoading(false);
            }
        };

        fetchYoutubeData();
    }, [wbStrId, topicId, videoDuration]);

    // Separate useEffect for transcript updates
    useEffect(() => {
        const updateTranscript = async () => {
            if (!lessonData.videoUrl) return;
            await fetchSummary();
        };

        updateTranscript();
    }, [lessonData.videoUrl]); // This will update transcript when video URL changes

    // Update handleVideoSwitch to not trigger the initial load
    const handleVideoSwitch = async (index) => {
        if (index >= 0 && index < videoList.length && index !== currentVideoIndex) {
            const selectedVideo = videoList[index];
            setCurrentVideoIndex(index);

            // Update lesson data with new video
            setLessonData(prev => ({
                ...prev,
                videoUrl: selectedVideo.videoURL,
                courseTitle: selectedVideo.title,
                lessonTitle: selectedVideo.title
            }));

            setNotesData(prev => ({
                ...prev,
                summary: selectedVideo.description
            }));

            // Clear transcript before fetching new one
            setTranscript('');
        }
    };

    const handleSendMessage = async () => {
        if (newMessage.trim()) {
            // Add user message to chat
            const newUserMessage = {
                id: chatMessages.length + 1,
                type: 'user',
                content: newMessage
            };

            const updatedMessages = [...chatMessages, newUserMessage];
            setChatMessages(updatedMessages);
            setNewMessage('');
            setIsMessageLoading(true);

            try {
                // Prepare conversation history with the counselor prompt and lesson context
                const messagesForApi = [
                    {
                        role: "system",
                        content: `You are a kind and supportive academic counselor for students.\n\nYour role:\n- Listen with empathy and validate emotions.\n- Give clear, caring, and helpful responses.\n- Use a friendly and conversational tone, not robotic or formal.\n- Comfort stressed students and guide confused ones.\n- If unsure what they mean, ask a kind follow-up.\n- Always reply in not more than 50 words.\n- Only when asked \"Who are you?\", reply: \"I'm an AI assistant developed by RnPsoft Private Limited to help students academically and emotionally.\"\n- Never mention OpenAI, ChatGPT, or any other AI provider.\n\nCurrent lesson: ${lessonData?.courseTitle || 'N/A'} - ${lessonData?.lessonTitle || 'N/A'}\nKey concepts: ${notesData?.keyConcepts?.join(', ') || 'Not specified'}`
                    },
                    // Map all previous messages with correct roles
                    ...updatedMessages.map(msg => ({
                        role: msg.type === 'user' ? 'user' : 'assistant',
                        content: msg.content
                    }))
                ];

                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ********************************************************************************************************************************************************************`
                    },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: messagesForApi,
                        temperature: 0.7
                    }),
                });

                const data = await response.json();
                if (data.choices && data.choices[0].message) {
                    const assistantMessage = {
                        id: updatedMessages.length + 1,
                        type: 'assistant',
                        content: data.choices[0].message.content
                    };
                    setChatMessages([...updatedMessages, assistantMessage]);
                }
            } catch (error) {
                console.error('Error:', error);
                // Add error message to chat
                const errorMessage = {
                    id: updatedMessages.length + 1,
                    type: 'assistant',
                    content: "Sorry, I'm having trouble responding right now. Please try again later."
                };
                setChatMessages([...updatedMessages, errorMessage]);
            } finally {
                setIsMessageLoading(false);
            }
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            handleSendMessage();
        }
    };

    const handleMarkAsDone = async () => {
        const newStatus = !isMarkedDone;
        setIsMarkedDone(newStatus);

        try {
            // Update status on the server
            await fetch('api/lesson/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ isMarkedDone: newStatus }),
            });
        } catch (err) {
            console.error('Error updating lesson status:', err);
            // Silently fail - the UI is already updated
        }
    };

    // Delete chat handler
    const deleteChat = () => {
        setChatMessages([]); // Clear chat
        setIsMessageLoading(true);
        setTimeout(() => {
            setChatMessages([mockData.chat[0]]); // Show default assistant message
            setIsMessageLoading(false);
        }, 1000);
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex flex-col items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                <div className="text-xl text-gray-600">Loading lesson data...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="bg-white rounded-xl shadow-md p-6">
                        <div className="flex flex-col items-center justify-center text-center">
                            <div className="text-red-500 text-5xl mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Content</h3>
                            <p className="text-gray-600 mb-6">{error}</p>
                            <button
                                onClick={() => window.location.reload()}
                                className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                            >
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    const translator = (word1, word2) =>
        localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
            ? word1
            : localStorage.getItem("lang")
                ? word2
                : word1;

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            {/*header*/}
            <div className="max-w-7xl mx-auto mb-4">
                <div className="p-4 border-b- border-gray-200">
                    <div className="flex justify-center">
                        <div className="ml-3 w-full flex justify-between items-center">
                            <div className="flex flex-col mb-4">
                                <h1 className="text-2xl font-bold text-gray-900">{lessonData.courseTitle}</h1>
                                <span className="text-gray-600 mt-1">{lessonData.date.substring(0, 10)}</span>
                            </div>
                            <div className="flex items-center justify-center">
                                <button className="p-2 hover:bg-gray-100 rounded-lg"
                                    onClick={() => { navigate("/structured-breakdown") }}
                                >
                                    <button className="p-2 hover:bg-gray-100 rounded-lg"
                                        onClick={() => { navigate("/structured-breakdown") }}
                                    >
                                        <IoMdClose />
                                    </button>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content Area */}
                    <div className="lg:col-span-2">
                        {/* Header Card */}
                        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
                            <div className="p-4">
                                {/* Course Title */}
                                <h2 className="text-xl font-semibold text-gray-900 mb-2">{lessonData.courseTitle}</h2>

                                {/* Lesson Title with Mark as Done Toggle */}
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg text-gray-700">{lessonData.lessonTitle}</h3>

                                    {/* <div className="flex items-center gap-2">
                                        <button
                                            onClick={handleMarkAsDone}
                                            className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ease-in-out"
                                            style={{ backgroundColor: isMarkedDone ? '#3b82f6' : '#d1d5db' }}
                                            aria-pressed={isMarkedDone}
                                            aria-labelledby="mark-as-done-label"
                                        >
                                            <span
                                                className={`inline-block h-5 w-5 transform rounded-full bg-white shadow transition-transform duration-300 ease-in-out ${isMarkedDone ? 'translate-x-5' : 'translate-x-1'
                                                    }`}
                                            />
                                        </button>
                                        <span id="mark-as-done-label" className="text-sm font-medium text-gray-700">{!isMarkedDone ? translator("Mark as Done", "डॉन करें") : translator("Mark as Not Done", "डॉन नहीं करें")}</span>
                                    </div> */}

                                </div>
                            </div>

                            {/* Video Container */}
                            <div className="bg-black rounded-lg mx-4 mb-4 overflow-hidden">
                                <div className="relative" style={{ paddingTop: "56.25%" }}>
                                    <iframe
                                        className="absolute top-0 left-0 w-full h-full"
                                        src={`https://www.youtube.com/embed/${lessonData.videoUrl?.split('v=')[1]?.split('&')[0] || ''}`}
                                        title={lessonData.lessonTitle}
                                        frameBorder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                        allowFullScreen
                                    ></iframe>
                                </div>
                            </div>

                            {/* Download Notes Button */}
                            <div className="flex justify-end px-4 mb-4">
                                <button className="flex items-center gap-2 text-blue-600 hover:text-blue-950"
                                    onClick={() => {
                                        localStorage.setItem("ytLinkforNotes", lessonData.videoUrl);
                                        navigate("/resource-notes");
                                        console.log('taking you to resource notes');
                                    }}
                                >
                                    <IoMdDownload />
                                    <span className="font-medium">{translator("Download Notes", "नोट्स डाउनलोड करें")}</span>
                                </button>
                            </div>

                            {/* Tabs and Content */}
                            <div className="border-t border-gray-200">
                                {/* Tab Navigation */}
                                <div className="flex border-b border-gray-200">
                                    <button
                                        onClick={() => setActiveTab('transcript')}
                                        className={`px-6 py-3 text-sm font-medium ${activeTab === 'transcript'
                                            ? 'bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] text-white rounded-t-lg'
                                            : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        {translator("Transcript", "पाठानेत्रावरील टेक्स्ट")}
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('resources')}
                                        className={`px-6 py-3 text-sm font-medium ${activeTab === 'resources'
                                            ? 'bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] text-white rounded-t-lg'
                                            : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        {translator("More Videos", "और वीडियो")}
                                    </button>
                                </div>

                                {/* Tab Content */}
                                <div className="p-6">
                                    {activeTab === 'transcript' && (
                                        <div>
                                            <h4 className="text-lg font-semibold text-gray-900 mb-4">{translator("Video Transcription", "वीडियो ट्रांसक्रिप्शन")}</h4>
                                            <div className="max-h-[300px] overflow-y-auto">
                                                {isTranscriptLoading ? (
                                                    <div className="flex items-center justify-center py-8">
                                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                                        <span className="ml-3 text-gray-600">Loading transcript...</span>
                                                    </div>
                                                ) : (
                                                    <div className="text-gray-700">
                                                        {transcript || 'No transcript available.'}
                                                    </div>
                                                )}
                                            </div>
                                            {/*}
                                            <h4 className="text-lg font-semibold text-gray-900 mt-6 mb-4">{translator("Summary", "सारांश")}</h4>
                                            <p className="text-gray-700">
                                                {notesData.summary}
                                            </p>
                                            */}
                                        </div>
                                    )}

                                    {activeTab === 'resources' && (
                                        <div>
                                            <h4 className="text-lg font-semibold text-gray-900 mb-4">{translator("Available Videos", "उपलब्ध वीडियो")}</h4>
                                            <ul className="space-y-3">
                                                {videoList.map((video, index) => (
                                                    <li key={index}
                                                        className={`cursor-pointer p-3 rounded-lg transition-all ${index === currentVideoIndex
                                                            ? 'hover:bg-gray-100'
                                                            : 'hover:bg-gray-100'
                                                            }`}
                                                        onClick={() => handleVideoSwitch(index)}
                                                    >
                                                        <div className="flex flex-col gap-1">
                                                            <h5 className={`font-medium ${index === currentVideoIndex
                                                                ? ''
                                                                : 'text-gray-900'
                                                                }`}>
                                                                {video.title}
                                                            </h5>
                                                            <p className="text-sm text-gray-600 line-clamp-2">
                                                                {video.description}
                                                            </p>
                                                        </div>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Sidebar - Ask Your Doubts */}
                    <div className="lg:col-span-1">
                        <div className="bg-white rounded-xl shadow-md overflow-hidden h-[calc(100vh-8rem)] flex flex-col sticky top-6">
                            {/* Chat Header */}
                            <div className="flex justify-between items-center bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] p-4 flex-shrink-0">
                                <button onClick={deleteChat} title="Delete Chat">
                                    <RiChatDeleteFill className="text-white h-8 w-8 cursor-pointer hover:opacity-80" />
                                </button>
                                <h3 className="text-lg font-semibold text-white">{translator("Ask Your Doubts", "अपने सवाल पूछें")}</h3>
                                <button onClick={downloadChatAsPDF} title="Download Chat">
                                    <MdDownloadForOffline className="text-white h-8 w-8 cursor-pointer hover:opacity-80" />
                                </button>
                            </div>

                            {/* Chat Messages */}
                            <div className="p-4 flex-grow overflow-y-auto">
                                <div className="space-y-4">
                                    {chatMessages.map((message) => (
                                        <div
                                            key={message.id}
                                            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                                        >
                                            <div
                                                className={`max-w-xs p-3 rounded-lg ${message.type === 'user'
                                                    ? 'bg-[#E0E7FF] text-gray-900'
                                                    : 'bg-gray-100 text-gray-900'
                                                    }`}
                                            >
                                                <p className="text-sm">{message.content}</p>
                                            </div>
                                        </div>
                                    ))}
                                    {isMessageLoading && (
                                        <div className="flex justify-start">
                                            {/* Simple text loading indicator for chat */}
                                            <div className="max-w-xs p-3 rounded-lg bg-gray-100 text-gray-900">
                                                <p className="text-sm italic">AI is thinking...</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Chat Input */}
                            <div className="p-4 border-t border-gray-200 flex-shrink-0">
                                <div className="flex items-center gap-2">
                                    <button
                                        onClick={handleMicClick}
                                        className={`p-3 rounded-full transition-colors ${listening ? 'bg-red-500 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`}
                                        title={listening ? "Stop Listening" : "Start Listening"}
                                    >
                                        <FaMicrophone className="h-5 w-5" />
                                    </button>
                                    <input
                                        type="text"
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        onKeyDown={handleKeyDown}
                                        placeholder={listening ? "Listening..." : translator("Type your message...", "अपना संदेश लिखें...")}
                                        className="flex-grow p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-shadow"
                                        disabled={listening && speechTranscript === ''} // Disable input while listening unless there's content
                                    />
                                    <button
                                        onClick={handleSendMessage}
                                        className="p-3 bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] text-white rounded-lg hover:opacity-90 transition-opacity disabled:opacity-50"
                                        disabled={!newMessage.trim() && !listening}
                                    >
                                        <IoSend className="h-5 w-5" />
                                    </button>
                                </div>
                                {browserSupportsSpeechRecognition && listening && (
                                    <p className="text-xs text-gray-500 mt-1">
                                        {translator("Say 'stop' or click mic to end.", "समाप्त करने के लिए 'स्टॉप' कहें या माइक पर क्लिक करें।")}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VideoSection;