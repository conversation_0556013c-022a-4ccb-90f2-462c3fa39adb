import React, { useState, useEffect } from "react";
import axios from "axios";
import Step1SubjectSelectionJS from "./Step1SubjectSelectionJS";
import Step2TimelineJS from "./Step2TimelineJS";
import Step3GeneratedPlanJS from "./Step3GeneratedPlanJS";

const StudyPlanJS = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [studyPlanData, setStudyPlanData] = useState({
    subjects: [],
    timelines: [],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const userData = JSON.parse(localStorage.getItem("user-data"));

  // Access the userId
  const userId = userData ? userData.userId : null;
  const handleStep1Submit = (subjects) => {
    setStudyPlanData((prev) => ({
      ...prev,
      subjects,
    }));
    setCurrentStep(2);
  };

  const handleStep2Submit = async (timelines) => {
    setStudyPlanData((prev) => ({
      ...prev,
      timelines,
    }));
    setLoading(true);
    setError("");
    setSuccess("");

    try {
      // Process each subject with its timeline
      const processedSubjects = await Promise.all(
        studyPlanData.subjects.map(async (subject, index) => {
          if (subject.pdfUrl) {
            try {
              console.log("Fetching AI response for:",{
                
                subject: subject.subject,
                pdfUrl: subject.pdfUrl,
                topic: subject.topic,
                numberOfDays: timelines[index].numberOfDays,
              });
              const response = await fetch(
                'https://schedule-suggestor-with-pdf-b8f6037-v1.app.beam.cloud' ,
                {
                  method: "POST",
                  Connection: "keep-alive",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization:
              "Bearer ALXP7mhHyKz1MQATKH7CIQXK9VQBpvoNNuxPvLONWyPCfgemj18cz2T74r4drBpvOkf-3orOQT_6r-63mHPZAA==",
          },
                  body: JSON.stringify({
                    pdf_url: subject.pdfUrl,
                    target: subject.topic,
                    days: timelines[index].numberOfDays,
                  }),
                }
              );
              const result = await response.json();
              console.log(result);
              return { ...subject, aiResponse: result };
            } catch (err) {
              console.error(
                `Failed to get AI response for ${subject.subject}:`,
                err
              );
              return subject;
            }
          }
          return subject;
        })
      );

      // Update the studyPlanData state with the processed subjects
      setStudyPlanData((prev) => ({
        ...prev,
        subjects: processedSubjects,
      }));

      // Create study plan with processed subjects and timelines
      const response = await fetch("https://api.aiclassroom.in/study-plan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          subjects: processedSubjects,
          startDate: timelines[0].startDate,
          endDate: timelines[0].endDate,
          totalDays: timelines[0].numberOfDays,
          dailyMinutes: timelines[0].dailyMinutes,
          holidays: timelines[0].holidays,
          userId: userId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create study plan");
      }

      const result = await response.json();
      setStudyPlanData((prev) => ({
        ...prev,
        planItems: result.planItems,
      }));
      setSuccess("Study plan created successfully!");
      setCurrentStep(3);
    } catch (err) {
      setError(err.message || "Failed to create study plan");
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    setCurrentStep((prev) => prev - 1);
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-3xl font-bold mb-8">Create Your Study Plan</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {currentStep === 1 && (
        <Step1SubjectSelectionJS
          onSubmit={handleStep1Submit}
          initialData={studyPlanData}
        />
      )}

      {currentStep === 2 && (
        <Step2TimelineJS
          onSubmit={handleStep2Submit}
          onBack={handleBack}
          initialData={studyPlanData}
          loading={loading}
        />
      )}

      {currentStep === 3 && (
        <Step3GeneratedPlanJS
          planItems={studyPlanData.planItems}
          onBack={handleBack}
          subjects={studyPlanData.subjects}
        />
      )}
    </div>
  );
};

export default StudyPlanJS;
