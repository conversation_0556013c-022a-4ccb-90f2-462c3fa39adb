import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { OpenAI } from "openai";

const openai = new OpenAI({ apiKey:"********************************************************************************************************************************************************************",dangerouslyAllowBrowser: true });



const InterviewerBot = ({ paragraph="Taj mahal is in Agra", intervieweeName="Alice" }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");
  const [stage, setStage] = useState("loading");
  const [questionIndex, setQuestionIndex] = useState(0);
  const [userData, setUserData] = useState([]);
  const [generalQuestions, setGeneralQuestions] = useState([]);
  const [quizQuestions, setQuizQuestions] = useState([]);

  useEffect(() => {
    const initialize = async () => {
      try {
        const greetingResponse = await openai.chat.completions.create({
          model: "gpt-4",
          messages: [{ role: "user", content: "Generate a warm, friendly greeting to begin an engaging and emotional interview." }],
        });

        const greeting = greetingResponse.choices[0].message.content;

        const generalResponse = await openai.chat.completions.create({
          model: "gpt-4",
          messages: [{ role: "user", content: "Generate 3 open-ended, emotionally engaging general interview questions to understand someone's personality and background better." }],
        });

        const parsedGeneral = generalResponse.choices[0].message.content.split("\n").filter(Boolean);

        const quizResponse = await openai.chat.completions.create({
          model: "gpt-4",
          messages: [
            {
              role: "user",
              content: `Generate 5 thoughtful interview-style questions and answers based on this paragraph:\n\n${paragraph}\n\nRespond in JSON array format like [{question: '...', answer: '...'}, ...]`,
            },
          ],
        });

        const parsedQuiz = JSON.parse(quizResponse.choices[0].message.content);

        setMessages([{ sender: "bot", text: `${greeting} I'm here to get to know you better, ${intervieweeName}. Let's begin!` }]);
        setGeneralQuestions(parsedGeneral);
        setQuizQuestions(parsedQuiz);
        setStage("general");
      } catch (err) {
        console.error("Failed to initialize interviewer bot:", err);
      }
    };
    initialize();
  }, [paragraph]);

  const sendMessage = (text, sender = "user") => {
    setMessages((prev) => [...prev, { sender, text }]);
  };

  const handleUserInput = async () => {
    if (!input.trim()) return;
    const userInput = input;
    sendMessage(userInput, "user");
    setUserData((prev) => [...prev, userInput]);
    setInput("");

    if (stage === "general") {
      try {
        const replyResponse = await openai.chat.completions.create({
          model: "gpt-4",
          messages: [
            { role: "system", content: "You are a warm and empathetic interviewer responding to an interviewee's personal answer with encouragement and genuine interest." },
            { role: "user", content: `Interviewee: ${userInput}\nRespond emotionally.` },
          ],
        });

        const reply = replyResponse.choices[0].message.content;
        sendMessage(reply, "bot");

        if (questionIndex < generalQuestions.length - 1) {
          const next = questionIndex + 1;
          setTimeout(() => {
            sendMessage(generalQuestions[next], "bot");
            setQuestionIndex(next);
          }, 1000);
        } else {
          setStage("data");
          setQuestionIndex(0);
          setTimeout(() => {
            sendMessage(`Thanks, ${intervieweeName}. Now let's dive into your knowledge.`, "bot");
            sendMessage(quizQuestions[0].question, "bot");
          }, 1500);
        }
      } catch (err) {
        console.error("General response error:", err);
        sendMessage("Thanks for sharing. Let's move on.", "bot");
      }
    } else if (stage === "data") {
      const currentQ = quizQuestions[questionIndex];
      try {
        const evaluation = await openai.chat.completions.create({
          model: "gpt-4",
          messages: [
            {
              role: "system",
              content:
                "You are an evaluator. Determine whether the user's answer is factually correct and equivalent in meaning to the expected answer, even if phrased more simply. Accept short, factual answers. Reply with only 'yes' or 'no'."
            },
            {
              role: "user",
              content: `Question: ${currentQ.question}\nUser answer: ${userInput}\nExpected answer: ${currentQ.answer}\nIs the user's answer correct?`
            }
          ]
        });

        const isCorrect = evaluation.choices[0].message.content.trim().toLowerCase().startsWith("yes");

        setTimeout(() => {
          sendMessage(isCorrect ? "That's correct! 😊" : `That's not quite right. The correct answer is: ${currentQ.answer}`, "bot");

          if (questionIndex < quizQuestions.length - 1) {
            const next = questionIndex + 1;
            setQuestionIndex(next);
            sendMessage(quizQuestions[next].question, "bot");
          } else {
            sendMessage("That’s all for now. Thanks for your time! 🌟", "bot");
            setStage("done");
          }
        }, 1000);
      } catch (err) {
        console.error("Evaluation error:", err);
        sendMessage("Sorry, I had trouble evaluating that answer.", "bot");
      }
    }
  };

  return (
    <div style={{ maxWidth: "700px", margin: "0 auto", padding: "1rem" }}>
      <div style={{ border: "1px solid #ccc", borderRadius: "10px", padding: "1rem", maxHeight: "500px", overflowY: "auto" }}>
        {messages.map((msg, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            style={{ textAlign: msg.sender === "bot" ? "left" : "right", marginBottom: "0.5rem" }}
          >
            <div
              style={{
                display: "inline-block",
                padding: "0.5rem 1rem",
                borderRadius: "15px",
                backgroundColor: msg.sender === "bot" ? "#e2e8f0" : "#bee3f8",
              }}
            >
              {msg.text}
            </div>
          </motion.div>
        ))}
      </div>
      {stage !== "done" && stage !== "loading" && (
        <div style={{ display: "flex", gap: "0.5rem", marginTop: "1rem" }}>
          <input
            style={{ flex: 1, padding: "0.5rem", borderRadius: "8px", border: "1px solid #ccc" }}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your answer..."
          />
          <button onClick={handleUserInput} style={{ padding: "0.5rem 1rem", borderRadius: "8px", background: "#3182ce", color: "white", border: "none" }}>
            Send
          </button>
        </div>
      )}
    </div>
  );
};

export default InterviewerBot;

// Usage example:
// <InterviewerBot 
//   paragraph="The water cycle describes how water moves through the Earth's atmosphere, land, and oceans..."
//   intervieweeName="Alice" 
// />


// Usage example:
// <InterviewerBot 
//   paragraph="The water cycle describes how water moves through the Earth's atmosphere, land, and oceans..."
//   intervieweeName="Alice" 
// />
