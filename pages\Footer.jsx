import { useTranslation } from "../hooks/useTranslation"

export default function Footer() {
  const { t } = useTranslation()

  return (
    <footer className="bg-white border-t border-gray-200 py-6">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap justify-center space-x-6 text-sm text-gray-600">
          <a href="#" className="hover:text-gray-900 mb-2">
            {t("footer.about")}
          </a>
          <a href="#" className="hover:text-gray-900 mb-2">
            {t("footer.helpCenter")}
          </a>
          <a href="#" className="hover:text-gray-900 mb-2">
            {t("footer.termsOfService")}
          </a>
          <a href="#" className="hover:text-gray-900 mb-2">
            {t("footer.privacyPolicy")}
          </a>
          <a href="#" className="hover:text-gray-900 mb-2">
            {t("footer.cookiePolicy")}
          </a>
          <a href="#" className="hover:text-gray-900 mb-2">
            {t("footer.careers")}
          </a>
          <a href="#" className="hover:text-gray-900 mb-2">
            @2025 AI Classroom. {t("footer.allRightsReserved")}
          </a>
        </div>
      </div>
    </footer>
  )
}