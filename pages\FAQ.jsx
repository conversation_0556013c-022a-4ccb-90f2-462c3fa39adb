"use client";
import {
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Facebook,
  UserCircle,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
const HeaderLogo = "/logo.png";

import img from "./navlogo.png";

// Custom Accordion Components
const CustomAccordion = ({ children }) => (
  <div className="space-y-4">{children}</div>
);

const CustomAccordionItem = ({ value, children, className = "" }) => (
  <div className={`border rounded-lg p-2 bg-gray-50 ${className}`}>
    {children}
  </div>
);

const CustomAccordionTrigger = ({ children, isOpen, onClick }) => (
  <button
    onClick={onClick}
    className="flex items-center gap-2 text-left w-full"
  >
    <div className="bg-purple-100 rounded-full p-1 w-6 h-6 flex items-center justify-center">
      <span className="text-purple-900 font-bold text-xs">?</span>
    </div>
    <span className="font-medium text-purple-900 flex-grow">{children}</span>
    {isOpen ? (
      <ChevronUp className="h-5 w-5 text-purple-900" />
    ) : (
      <ChevronDown className="h-5 w-5 text-purple-900" />
    )}
  </button>
);

const CustomAccordionContent = ({ children, isOpen }) => (
  <div
    className={`overflow-hidden transition-all duration-300 ${
      isOpen ? "max-h-[500px]" : "max-h-0"
    }`}
  >
    <div className="pl-8 pr-4 pt-2 pb-4 text-gray-600">{children}</div>
  </div>
);

export default function FAQ() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openItems, setOpenItems] = useState({});
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleItem = (value) => {
    setOpenItems((prev) => ({
      ...prev,
      [value]: !prev[value],
    }));
  };

  return (
    <>
      <div className="pt-[100px]">
        <h1 className="text-4xl font-bold text-purple-900 text-center mb-12">
          Popular Questions
        </h1>

        <div className="max-w-3xl mx-auto mb-12">
          <CustomAccordion>
            <CustomAccordionItem value="item-1">
              <CustomAccordionTrigger
                isOpen={openItems["item-1"]}
                onClick={() => toggleItem("item-1")}
              >
                What is AI Classroom ?
              </CustomAccordionTrigger>
              <CustomAccordionContent isOpen={openItems["item-1"]}>
                AI Classroom is an AI-powered website developed by RnPsoft that
                can convert text or PDF documents into animations. It also
                features exam mode and interview mode.
              </CustomAccordionContent>
            </CustomAccordionItem>

            <CustomAccordionItem value="item-2">
              <CustomAccordionTrigger
                isOpen={openItems["item-2"]}
                onClick={() => toggleItem("item-2")}
              >
                Does AI Classroom have an Exam Mode ?
              </CustomAccordionTrigger>
              <CustomAccordionContent isOpen={openItems["item-2"]}>
                Yes, it includes MCQs and answer-sheet-based exams.
              </CustomAccordionContent>
            </CustomAccordionItem>

            <CustomAccordionItem value="item-3">
              <CustomAccordionTrigger
                isOpen={openItems["item-3"]}
                onClick={() => toggleItem("item-3")}
              >
                Can AI Classroom Solve doubts instantly ?
              </CustomAccordionTrigger>
              <CustomAccordionContent isOpen={openItems["item-3"]}>
                Yes, through direct voice interaction.
              </CustomAccordionContent>
            </CustomAccordionItem>

            <CustomAccordionItem value="item-4">
              <CustomAccordionTrigger
                isOpen={openItems["item-4"]}
                onClick={() => toggleItem("item-4")}
              >
                What are the pricing plans for AI Classroom ?
              </CustomAccordionTrigger>
              <CustomAccordionContent isOpen={openItems["item-4"]}>
                AI Classroom requires credits to work, which can be purchased
                using UPI, credit, and debit cards. For detailed pricing
                information, please contact our sales team.
              </CustomAccordionContent>
            </CustomAccordionItem>

            <CustomAccordionItem value="item-5">
              <CustomAccordionTrigger
                isOpen={openItems["item-5"]}
                onClick={() => toggleItem("item-5")}
              >
                How does AI Classroom helps in studying ?
              </CustomAccordionTrigger>
              <CustomAccordionContent isOpen={openItems["item-5"]}>
                It provides better notes, real-time doubt solving, and an AI
                teacher.
              </CustomAccordionContent>
            </CustomAccordionItem>

            <CustomAccordionItem value="item-6">
              <CustomAccordionTrigger
                isOpen={openItems["item-6"]}
                onClick={() => toggleItem("item-6")}
              >
                What Languages AI Classroom Support ?
              </CustomAccordionTrigger>
              <CustomAccordionContent isOpen={openItems["item-6"]}>
                AI Classroom supports English, Hindi and Hinglish.
              </CustomAccordionContent>
            </CustomAccordionItem>
          </CustomAccordion>

          <div className="mt-8 p-4 border rounded-lg bg-gray-50">
            <p className="text-sm text-gray-600 mb-2">Still have questions?</p>
            <div className="text-xs text-gray-500 mb-4">
              Can't find the answer you're looking for? Please chat with our
              friendly team.
            </div>
            <button className="bg-purple-900 hover:bg-purple-800 text-white text-sm px-4 py-2 rounded" onClick={()=>window.location.href="https://aiclassroom.in/contact"}>
              Help desk
            </button>
          </div>
        </div>

        <footer className="border-t bg-gray-100 pt-8">
          <div className="flex flex-wrap justify-between mx-[10%] gap-8">
            <div className="w-full md:w-auto">
              <div className="flex items-center gap-2 mb-4">
                <div className="relative">
                  <img
                    src={img}
                    alt="AI Classroom Logo"
                    className="w-full h-[50px]"
                  />
                </div>
              </div>

              <div className="text-sm text-gray-600 mb-4 max-w-[450px]">
                AI Classroom is designed by RnPsoft Private Limited to cater all the needs of a classroom with the AI Effect.
              </div>
            </div>

            <div className="w-full sm:w-auto">
              <h3 className="font-medium mb-4">About Us</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li onClick={()=>{window.location.href="https://rnpsoft.com/our-team"}}>Our Team</li>
                <li onClick={()=>{window.location.href="https://rnpsoft.com/event"}}>Events</li>
                
              </ul>
            </div>

  <div className="w-full sm:w-auto">
              <h3 className="font-medium mb-4">Policies</h3>
              <li onClick={()=>{window.location.href="https://aiclassroom.in/terms"}}>Terms and Conditions</li>
                <li onClick={()=>{window.location.href="https://aiclassroom.in/privacy"}}>Privacy</li>
                                <li onClick={()=>{window.location.href="https://aiclassroom.in/refund"}}>Refund</li>

                
            </div>
          

            <div className="w-full sm:w-auto">
              <h3 className="font-medium mb-4">Follow Us</h3>
              <div className="mt-[15px] flex gap-2">
                <Instagram className="h-5 w-5" onClick={()=>{window.location.href="https://www.instagram.com/rnpsoft/"}} />
                <Facebook className="h-5 w-5" onClick={()=>{window.location.href="https://www.facebook.com/profile.php?id=61555927914160"}} />
                <Linkedin className="h-5 w-5" onClick={()=>{window.location.href="https://www.linkedin.com/company/rnpsoft/"}} />
                <Youtube className="h-5 w-5"onClick={()=>{window.location.href="https://www.youtube.com/@RnPsoft"}} />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap justify-between mx-[10%] mt-8">
            <div className="w-full sm:w-auto">
              <div className="text-sm text-gray-600 mb-2"><EMAIL></div>
              <div className="text-sm text-gray-600 mb-2">+91 9938512307</div>
            </div>

            <div className="w-full sm:w-auto text-center text-xs text-gray-500 mt-8 pt-4">
              © 2025 AI Classroom All rights reserved
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
