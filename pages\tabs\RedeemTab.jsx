"use client";

import { useEffect, useState } from "react";
import CoinBadge from "../CoinBadge";

import ShippingAddressModal from "./../modals/ShippingAddressModal";
import RedemptionSuccessModal from "./../modals/RedemptionSuccessModal";
import { set } from "date-fns";

const rewardItems = [
  {
    id: 1,
    title: "Digital Notebook",
    description: "Premium digital notebook with cloud sync",
    price: 1,
    image: "https://via.placeholder.com/300x200",
    canAfford: false,
    isPhysical: false,
    redemptionCode: "REWARD-07233WWR",
  },
  {
    id: 2,
    title: "AI Study Guide",
    description: "Premium digital notebook with cloud sync",
    price: 50,
    image: "https://via.placeholder.com/300x200",
    canAfford: false,
    isPhysical: false,
    redemptionCode: "REWARD-98765ABC",
  },
  {
    id: 3,
    title: "Premium Headphones",
    description: "Premium wireless headphones with noise cancellation",
    price: 50,
    image: "https://via.placeholder.com/300x200",
    canAfford: true,
    isPhysical: true,
    redemptionCode: "REWARD-45678XYZ",
  },
  {
    id: 4,
    title: "Laptop Discount",
    description: "Premium digital notebook with cloud sync",
    price: 50,
    image: "https://via.placeholder.com/300x200",
    canAfford: false,
    isPhysical: false,
    redemptionCode: "REWARD-12345DEF",
  },
  {
    id: 5,
    title: "Course Extension",
    description: "Premium digital notebook with cloud sync",
    price: 50,
    image: "https://via.placeholder.com/300x200",
    canAfford: false,
    isPhysical: false,
    redemptionCode: "REWARD-56789GHI",
  },
];

function RedeemTab() {
  const [showShippingModal, setShowShippingModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedReward, setSelectedReward] = useState(null);
  const [rewards, setRewards] = useState(0);
  const [totalRewards, setTotalRewards] = useState(0);
  useEffect(() => {
    const userData = localStorage.getItem("user-data");
    setRewards(userData ? JSON.parse(userData).coins : null);
    setTotalRewards(userData ? JSON.parse(userData).totalRewards : null);
  }, []);

  const handleRedeemClick = (reward) => {
    if (rewards < reward.price) return;

    setSelectedReward(reward);

    // Update user's coin balance
    const userData = JSON.parse(localStorage.getItem("user-data"));
    const updatedUserData = {
      ...userData,
      coins: userData.coins - reward.price,
    };
    localStorage.setItem("user-data", JSON.stringify(updatedUserData));
    setRewards(updatedUserData.coins);

    // Save redemption to backend
    const redemptionData = {
      email: userData.email,
      title: reward.title,
      price: reward.price,
      date: new Date().toISOString(),
    };

    fetch("https://api.aiclassroom.in/redeem-history", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(redemptionData),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          console.log("Redemption saved successfully!");
          if (reward.isPhysical) {
            setShowShippingModal(true);
          } else {
            setShowSuccessModal(true);
          }
        } else {
          console.error("Error saving redemption:", data.errors);
        }
      })
      .catch((error) => console.error("Fetch error:", error));
  };

  const handleConfirmShipping = (address) => {
    // Here you would typically send the shipping address to your backend
    console.log(`Shipping to: ${address}`);

    // Close shipping modal and show success modal
    setShowShippingModal(false);
    setShowSuccessModal(true);
  };

  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    setSelectedReward(null);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-blue-600">
          Upcoming Rewards
        </h2>
        <CoinBadge amount={rewards} variant="available" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {rewardItems.map((item) => {
          const canAfford = rewards >= item.price;
          return (
            <div
              key={item.id}
              className="bg-white rounded-lg overflow-hidden shadow-sm"
            >
              <div className="relative">
                <img
                  src={item.image || "/placeholder.svg"}
                  alt={`${item.title} reward image`}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 right-2">
                  <CoinBadge amount={item.price} />
                </div>
              </div>

              <div className="p-4">
                <h3 className="font-semibold text-purple-800">{item.title}</h3>
                <p className="text-sm text-gray-600">{item.description}</p>

                <button
                  className={`w-full mt-3 py-2 px-4 rounded-md text-white ${
                    !canAfford
                      ? "bg-gray-300 hover:bg-gray-300 text-gray-700 cursor-not-allowed"
                      : "bg-purple-800 hover:bg-purple-900"
                  }`}
                  disabled={!canAfford}
                  onClick={() => handleRedeemClick(item)}
                >
                  {canAfford ? "Redeem Now" : "Not Enough Coins"}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Shipping Address Modal */}
      {showShippingModal && selectedReward && (
        <ShippingAddressModal
          onClose={() => setShowShippingModal(false)}
          onConfirm={handleConfirmShipping}
          productName={selectedReward.title.toLowerCase()}
        />
      )}

      {/* Redemption Success Modal */}
      {showSuccessModal && selectedReward && (
        <RedemptionSuccessModal
          onClose={handleCloseSuccessModal}
          rewardDetails={{
            title: selectedReward.title,
            price: selectedReward.price,
            code: selectedReward.redemptionCode,
          }}
        />
      )}
    </div>
  );
}

export default RedeemTab;
