import React, { useState, useEffect } from "react";

const LoadingSequence = ({ steps }) => {
  const [progress, setProgress] = useState(Array(steps.length).fill(0));
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    if (currentStep < steps.length) {
      const speed = steps[currentStep].speed;
      const intervalTime = 50;
      const increment = (100 / (speed / intervalTime)).toFixed(2);

      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          const updated = [...prev];
          updated[currentStep] = Math.min(
            updated[currentStep] + parseFloat(increment),
            100
          );
          return updated;
        });
      }, intervalTime);

      const timeout = setTimeout(() => {
        clearInterval(progressInterval);
        setCurrentStep((prev) => prev + 1);
      }, speed);

      return () => {
        clearInterval(progressInterval);
        clearTimeout(timeout);
      };
    }
  }, [currentStep, steps]);

  return (
    <div className="flex flex-col items-center px-4 py-6 bg-white rounded-3xl shadow-xl transition-all duration-500">      <div className="flex justify-center gap-6 w-full max-w-full overflow-x-auto pb-4">
        {steps.map((step, index) => (
          <div
          key={index}
          className={`
            flex flex-col items-center space-y-3
            w-[100px] sm:w-[120px] md:w-[140px] lg:w-[160px] xl:w-[180px]
            p-3 sm:p-4 lg:p-5
            rounded-3xl transition-all duration-300
            bg-gray-800 shadow-lg hover:shadow-[0_0_20px_rgba(0,255,255,0.6)]
            ${
              currentStep === index
                ? "ring-2 ring-cyan-400 shadow-[0_0_25px_rgba(236,72,153,0.8)]"
                : ""
            }
          `}
        >
          <img
            src={step.gif}
            alt={`Step ${index + 1}`}
            className={`
              w-full h-auto object-contain rounded-xl
              transition-opacity duration-500
              ${currentStep >= index ? "opacity-100" : "opacity-40"}
              ${index === 2 ? "mt-4" : index === 1 ? "mt-1" : "mt-0"}
            `}
          />
        
            {step.speed !== 0 && (
              <div className="flex flex-col items-center w-full">
                <div className="w-full h-2 rounded-full overflow-hidden relative bg-gray-600 bg-opacity-70">
                  <div
                    className="h-full animate-gradient-x transition-all duration-200 ease-linear"
                    style={{
                      width: `${progress[index]}%`,
                      backgroundImage:
                        "linear-gradient(90deg, rgb(0,255,255), rgb(236,72,153), rgb(0,255,255))",
                      backgroundSize: "200% 100%",
                      backgroundPosition: "left center",
                      boxShadow: "0 0 10px rgba(0,255,255,0.8)",
                    }}
                  ></div>
                </div>
                <p className="mt-1 text-[10px] text-gray-300 text-center">
                  {progress[index] < 100
                    ? `${Math.floor(progress[index])}% loaded`
                    : "✅ Complete"}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {currentStep >= steps.length && (
        <h1 className="text-xl sm:text-2xl font-bold text-center mt-6 text-pink-500 animate-float drop-shadow-[0_0_10px_rgb(236,72,153)]">
          🎉 Generation Complete!
        </h1>
      )}

      {/* Neon Glow Animations */}
      <style>{`
        @keyframes gradient-x {
          0% { background-position: 0% center; }
          100% { background-position: 200% center; }
        }

        .animate-gradient-x {
          animation: gradient-x 2s linear infinite;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-5px); }
        }

        .animate-float {
          animation: float 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default LoadingSequence;
