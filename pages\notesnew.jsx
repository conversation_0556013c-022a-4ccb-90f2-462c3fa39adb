import { useState, useRef } from "react";
import { X, Eye, EyeOff } from "lucide-react";

export default function UnifiedView({ dataholder }) {
  const [showLeft, setShowLeft] = useState(false);
  const [isplaying, setIsPlaying] = useState(false);
  const [currenttime, setCurrenttime] = useState(0);
  const [values, setValues] = useState(0);
  const [max, setMax] = useState(dataholder.endingvalue || 100);
  const AudioRef = useRef(null);

  return (
    <div className="container mx-auto p-4 bg-[#f9f9f9] rounded-xl border-[10px] border-white shadow-[0_0_0_2px_#ccc,_inset_0_0_20px_#d9d9d9]">
      <div className="relative flex flex-col md:flex-row gap-6">
        {/* Toggle Button */}
        <button
          onClick={() => setShowLeft(!showLeft)}
          className="absolute top-4 left-4 z-10 p-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 focus:outline-none"
        >
          {showLeft ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
        </button>

        {/* Left Panel: Image or OCR */}
        {showLeft && (
          <div className="md:w-1/2 w-full bg-white rounded-xl p-4 shadow-lg border border-gray-100 relative">
            <button
              onClick={() => setShowLeft(false)}
              className="absolute top-2 right-2 text-gray-500 hover:text-red-500"
              aria-label="Close"
            >
              <X className="w-5 h-5" />
            </button>
            {dataholder.type ? (
              <img
                src={dataholder.mainImage}
                alt="Main Visual"
                className="w-full h-auto object-cover rounded-lg shadow-inner"
              />
            ) : (
              <div className="prose max-w-none text-gray-700 bg-gray-50 p-4 rounded-lg shadow-inner border border-gray-200">
                {dataholder?.ocr?.split(". ").map((part, index) => (
                  <div key={index} className="mb-2">
                    {markParagraphs(normalizeText(part), dataholder?.mappings)}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Right Panel: Title, TypingEffect, Image, Audio Controls */}
        <div className="flex-1 bg-white rounded-xl shadow-lg p-6 relative border border-gray-100">
          <div className="mb-6">
            <div className="rounded-lg overflow-hidden shadow-md border border-gray-200">
              <img
                src={dataholder.image}
                alt="Reference Visual"
                className="w-full h-auto object-cover"
              />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
            {dataholder.heading}
          </h2>
          <div className="text-gray-700 leading-relaxed mb-6">
            <TypingEffect
              content={dataholder.content}
              duration={dataholder.duration}
              color={"black"}
            />
          </div>

          {/* Audio Player Controls */}
          <div className="fixed inset-x-0 bottom-0 w-full bg-gradient-to-t from-gray-900/80 to-transparent p-4">
            <div className="flex items-center mb-2">
              <button
                onClick={() => {
                  if (AudioRef.current) {
                    if (AudioRef.current.paused) {
                      AudioRef.current.play();
                      setIsPlaying(true);
                    } else {
                      AudioRef.current.pause();
                      setIsPlaying(false);
                    }
                  }
                }}
                className="p-3 bg-white rounded-full shadow-md text-indigo-600 hover:bg-indigo-50 focus:outline-none mr-3"
              >
                {!isplaying ? (
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>

              <div className="flex-1">
                <input
                  type="range"
                  min="0"
                  max={max}
                  value={values}
                  onChange={(e) => {
                    const val = Number(e.target.value);
                    setValues(val);
                    AudioRef.current.currentTime = val - dataholder.startingvalue;
                    AudioRef.current.play();
                  }}
                  className="w-full h-2 bg-gray-300 rounded-lg cursor-pointer"
                />
                <div className="flex justify-between text-xs text-white mt-1">
                  <span>{formatTime(currenttime)}</span>
                  <span>{formatTime(dataholder.endingvalue)}</span>
                </div>
              </div>
            </div>
          </div>

          <audio
            src={dataholder.audio}
            autoPlay
            onTimeUpdate={() => {
              const current = AudioRef.current.currentTime + dataholder.startingvalue;
              setCurrenttime(current);
              setValues(current);
            }}
            onEnded={() => {
              if (dataholder.next) {
                if (dataholder.next.endingvalue > max) {
                  setMax(dataholder.next.endingvalue);
                }
                setdataholder(dataholder.next);
              }
            }}
            controls={false}
            ref={AudioRef}
            className="hidden"
          />
        </div>
      </div>
    </div>
  );
}
