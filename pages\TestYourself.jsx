"use client";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Facebook,
  UserCircle,
  Menu,
  X,
} from "lucide-react";

const HeaderLogo = "/logo.png";
const Bulb = "/bulb.png";
const DoubleCircle = "/doubleCircle.png";
const ReactLogo = "/react.png";
const Ruler = "/ruler.png";
const testExam = "/testExam.png";
const testInterview = "/testInterview.png";

export default function TestYourself() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  return (
    <div className="min-h-screen flex flex-col items-center px-4 md:px-16 py-10 relative overflow-hidden">
      <img
        src={Bulb}
        alt="Bulb"
        className="absolute top-[15%] right-[10%] md:right-[15%] w-[8vw] max-w-[70px] hidden sm:block"
      />
      <img
        src={DoubleCircle}
        alt="Double Circle"
        className="absolute top-[15%] left-[5%] md:left-[8%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={ReactLogo}
        alt="React Logo"
        className="absolute top-[55%] right-[5%] md:right-[8%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={Ruler}
        alt="Ruler"
        className="absolute top-[55%] left-[5%] md:left-[5%] w-[6vw] max-w-[50px] hidden sm:block"
      />

      {/* Main Content */}
      <main className="w-full mt-[35px] max-w-4xl">
        <div className="text-center mb-6">
          <h2 className="text-4xl font-bold text-purple-700 mb-4">
            Test Yourself
          </h2>
          <p className="text-gray-600 text-lg">
            Train, Learn, Master With All New Ai Powered Features
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Live Ai Interview Card */}
          <div className="bg-gradient-to-b from-[#C5BAFF] to-[#C4D9FF] rounded-2xl shadow-lg p-6 flex flex-col items-center text-center">
            <div className="relative flex align-center w-[200px] h-[200px]">
              <img
                src={testInterview}
                alt="Interview"
                className="object-contain"
              />
            </div>
            <h3 className="text-xl font-semibold text-purple-700 mb-4">
              Live Ai Interview
            </h3>
            <p className="text-gray-600 mb-6 text-sm">
              Participate in our live Ai-based interview to assess and enhance
              your knowledge and preparation.
            </p>
            <button className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors" onClick={()=>{if(!localStorage.getItem("user-data")){
      alert("Please login to continue");
      return;
    }window.location.href="/avatar"}}>
              Take Interview
            </button>
          </div>

          {/* Exam Mode Card */}
          <div className="bg-gradient-to-b from-[#C5BAFF] to-[#C4D9FF] rounded-2xl shadow-lg p-6 flex flex-col items-center text-center">
            <div className="relative flex align-center w-[200px] h-[200px]">
              <img src={testExam} alt="Exam" className="object-contain" />
            </div>
            <h3 className="text-xl font-semibold text-purple-700 mb-4">
              Exam Mode
            </h3>
            <p className="text-gray-600 mb-6 text-sm">
              Take our Answersheet Exam to evaluate your subjective knowledge
              and receive a detailed performance report.
            </p>
            <button className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors" onClick={()=>{if(!localStorage.getItem("user-data")){
      alert("Please login to continue");
      return;
    }window.location.href="/exam-toggle"}}>
              Attend Exam
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
