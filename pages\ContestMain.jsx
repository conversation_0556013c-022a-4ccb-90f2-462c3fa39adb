import React, { useState, useEffect } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
const Trophy = "../public/Trophy.png";

const ContestMain = () => {
  const [bannerContest, setBannerContest] = useState(null);
  const [userData, setUserData] = useState(null);
  const [upcomingContests, setUpcomingContests] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRegistered, setIsRegistered] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Load user data from localStorage
    const storedUserData = localStorage.getItem("user-data");
    if (storedUserData) {
      try {
        const parsedUserData = JSON.parse(storedUserData);
        setUserData(parsedUserData);
      } catch (err) {
        console.error("Error parsing user data from localStorage", err);
      }
    }

    // Fetch banner contest data
    const fetchBannerContest = async () => {
      try {
        const response = await axios.get("https://api.aiclassroom.in/contest");
        setBannerContest(response?.data?.data);
      } catch (err) {
        setError("Failed to load banner contest");
        console.error("Error fetching banner contest:", err);
      }
    };

    // Fetch upcoming contests
    const fetchUpcomingContests = async () => {
      try {
        const response = await axios.get("https://api.aiclassroom.in/contests");
        if (response.data.success) {
          setUpcomingContests(response.data.data);
        }
      } catch (err) {
        console.error("Error fetching upcoming contests:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBannerContest();
    fetchUpcomingContests();
  }, []);

  // Check if user is registered for the contest
  useEffect(() => {
    if (userData && bannerContest) {
      const registered = bannerContest.registered?.includes(userData.email);
      setIsRegistered(registered);
      const submitted = bannerContest.submission?.some(
        (entry) => entry.email === userData.email
      );
      setSubmitted(submitted);
    
    }
  }, [userData, bannerContest]);

  const handleRegister = async () => {
    if (!userData || !userData.userId) {
      alert("Please login to register for the contest.");
      return;
    }

    try {
      const response = await axios.post(
        `https://api.aiclassroom.in/contest/${bannerContest._id}/register`,
        { email: userData.email }
      );

      if (response.data.success) {
        alert("Successfully registered for the contest!");
        window.location.reload();
      } else {
        alert(response.data.message || "Failed to register for the contest.");
      }
    } catch (error) {
      console.error("Error registering for the contest:", error);
      alert("An error occurred while registering. Please try again.");
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const prizes = [
    { name: "Tablet Devices", icon: "📱", color: "bg-orange-300" },
    { name: "Science Encyclopedia", icon: "📚", color: "bg-white" },
    { name: "Digital Microscope", icon: "🔬", color: "bg-orange-500" },
  ];

  const winners = [
    {
      name: "Aryan Patel",
      place: "1st place",
      quote: "Winning boosted my confidence in science!",
      profileImg: "https://storage.googleapis.com/jwelleryrnpsoft/AryanPatel.png",
    },
    {
      name: "James Duke",
      place: "2nd place",
      quote: "I learn something new in every contest.",
      profileImg: "https://storage.googleapis.com/jwelleryrnpsoft/JamesDuke.png",
    },
    {
      name: "Mira Jhonson",
      place: "3rd place",
      quote: "The questions were challenging but fun!",
      profileImg: "https://storage.googleapis.com/jwelleryrnpsoft/MiraJohnson.png",
    },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-xl text-purple-600">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-xl text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Main Contest Card */}
      {bannerContest && (
        <div className="max-w-4xl mx-auto mt-20 p-6 bg-gradient-to-r from-blue-100/50 to-purple-100/50 rounded-xl relative">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>📅 {formatDate(bannerContest.date)}</span>
          </div>
          <h1 className="text-2xl font-bold mt-2">{bannerContest.title}</h1>

          <div className="flex gap-4 mt-4">
            <span className="bg-blue-200 px-3 py-1 text-blue-600 rounded-full text-sm">
              Class {bannerContest.class}
            </span>
            <span className="bg-purple-200 px-3 py-1 text-purple-600 rounded-full text-sm">
              {bannerContest.subject}
            </span>
            <span className="bg-green-200 px-3 py-1 text-green-600 rounded-full text-sm">
              {Math.floor(bannerContest.duration / 60000)} minutes
            </span>
          </div>

          <p className="mt-4 text-gray-600">{bannerContest.description}</p>

          {/* Important Exam Information */}
          <div className="mt-4 p-4 bg-yellow-200 rounded-lg">
            <h2 className="font-semibold text-lg text-yellow-700">
              Important Information for the Exam
            </h2>
            <ul className="list-disc pl-5 mt-2 text-gray-700">
              <li>Make sure you are ready with pen and paper to take notes or solve problems.</li>
              <li>Switching panels or tabs during the exam will automatically cancel your participation.</li>
              <li>You must take the exam on a laptop. Using a mobile device may result in disqualification.</li>
            </ul>
          </div>

          <div className="mt-4">
            {userData && userData.email ? (
              <>
                <button
                  onClick={handleRegister}
                  className={`bg-purple-600 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-purple-700 mr-4 ${
                    isRegistered ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  disabled={isRegistered}
                >
                  {isRegistered ? "Already Registered" : "Register Now"}
                </button>
                <button
                  onClick={() => navigate("/quiz-waiting-interface")}
                  className={`bg-green-500 text-white px-6 py-2 rounded-lg cursor-pointer hover:bg-green-600  ${
                    submitted ? "opacity-50 cursor-not-allowed" : ""
                  }` }
                  disabled={submitted}
                >
                  {submitted?"Already Submitted":"Give Contest"}
                </button>
              </>
            ) : (
              <>
              <button
                onClick={() => navigate("/")}
                className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700"
              >
                Login to register
              </button>
        
                </>
            )}
          </div>

          {/* Prizes Section 
          <div className="mt-6">
            <h2 className="text-xl font-bold text-purple-700">Prizes</h2>
            <div className="grid grid-cols-3 gap-6 mt-4">
              {prizes.map((prize, index) => (
                <div
                  key={index}
                  className={`p-4 ${prize.color} rounded-lg shadow-lg text-center`}
                >
                  <div className="text-3xl">{prize.icon}</div>
                  <div className="mt-2 text-lg font-semibold">{prize.name}</div>
                </div>
              ))}
            </div>
          </div>
          */}
        </div>
      )}

      {/* Upcoming Contests Button and Dropdown */}
      {upcomingContests.length > 0 && (
        <div className="max-w-4xl mx-auto mt-4">
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="w-full bg-white border border-purple-600 text-purple-600 px-6 py-2 rounded-lg hover:bg-purple-50 flex items-center justify-center gap-2"
            >
              View upcoming contests{" "}
              <svg
                className={`w-4 h-4 transition-transform ${
                  showDropdown ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {showDropdown && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg z-10 max-h-64 overflow-y-auto">
                {upcomingContests.map((contest, index) => (
                  <div
                    key={index}
                    className="p-3 hover:bg-purple-50 border-b last:border-b-0"
                  >
                    <div className="font-medium">{contest.title}</div>
                    <div className="text-sm text-gray-600">
                      {formatDate(contest.date)} • {contest.subject}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ContestMain;
