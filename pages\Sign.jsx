import { useState, useRef } from "react";
import "./sign.css";
import { useNavigate } from "react-router-dom";

const CreateAccount = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    mobile: "",
    password: "",
    confirmPassword: "",
    agreeTerms: false,
  });

  const [showOTP, setShowOTP] = useState(false);
  const [otp, setOtp] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const inputRefs = useRef([]);

  const handleChange = (e) => {
    if (!e.target) return;
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      alert("Passwords do not match");
      return;
    }

    if (!formData.agreeTerms) {
      alert("You must agree to the terms and conditions");
      return;
    }

    const otpValue = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(otpValue);
 const sendOtp = async () => {
  const response = await fetch("https://api.aiclassroom.in/sent-otp", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email: formData.email, otp: otpValue }),
  });

  if (!response.ok) {
    alert("❌ Failed to send OTP");
    return;
  }

  const result = await response.json();
  console.log("✅ OTP Sent:", result.message);
};
sendOtp()
    console.log("Generated OTP:", otpValue);
    setShowOTP(true);
  };

  const handleOtpChange = (index, value) => {
    if (/^\d?$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 3) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && otp[index] === "") {
      if (index > 0) inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOtp = otp.join("");
    if (enteredOtp === generatedOTP) {
      alert("OTP verified successfully!");
      navigate("/education-details", {
        state: {
          name: formData.fullName,
          email: formData.email,
          mobile: formData.mobile,
          password: formData.password,
        },
      });
    } else {
      alert("Incorrect OTP. Please try again.");
    }
  };

  return (
    <div className="container1">
      <div className="form-container">
        <div className="form-content">
          <h1 className="form-title">Create Your Account</h1>
          <p className="form-subtitle">Join AI Classroom to start your personalized learning journey.</p>

          {!showOTP ? (
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Full Name</label>
                <input
                  type="text"
                  name="fullName"
                  placeholder="Enter your full name"
                  value={formData.fullName}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="form-group">
                <label>Email Address</label>
                <input
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="form-group">
                <label>Mobile Number</label>
                <input
                  type="tel"
                  name="mobile"
                  placeholder="Enter your mobile number"
                  value={formData.mobile}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="form-group">
                <label>Create Password</label>
                <input
                  type="password"
                  name="password"
                  placeholder="Create a password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="form-group">
                <label>Confirm Password</label>
                <input
                  type="password"
                  name="confirmPassword"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="form-checkbox">
                <input
                  type="checkbox"
                  name="agreeTerms"
                  checked={formData.agreeTerms}
                  onChange={handleChange}
                  required
                />
                <span>
                  I agree to the{" "}
                  <a href="#" className="link">Terms of Service</a> and{" "}
                  <a href="#" className="link">Privacy Policy</a>
                </span>
              </div>

              <button type="submit" className="btn-primary">
                Create My Account
              </button>
            </form>
          ) : (
           <div className="otp-section">
  <h3>Enter the 4-digit OTP sent to your Email</h3>
  <div className="otp-inputs">
    {otp.map((digit, index) => (
      <input
        key={index}
        type="text"
        maxLength="1"
        value={digit}
        onChange={(e) => handleOtpChange(index, e.target.value)}
        onKeyDown={(e) => handleKeyDown(e, index)}
        ref={(el) => (inputRefs.current[index] = el)}
        className="otp-box"
        inputMode="numeric"
      />
    ))}
  </div>
  <button onClick={handleVerifyOTP} className="btn-primary mt-2">
    Verify OTP
  </button>
</div>

          )}
        </div>
      </div>

      <div className="info-container">
        <div className="logo-container">
          <div className="logo">
            <img src="/logo.png" alt="AI Classroom Logo" height={30} width={30} />
            <span>AI Classroom</span>
          </div>
        </div>

        <div className="illustration">
          <img src="/images/computer-illustration.png" alt="Computer with learning tools" />
        </div>

        <div className="welcome-text">
          <h2>Welcome to AI Classroom!</h2>
          <p>Let's set up your personalized AI learning experience. It only takes 2 minutes!</p>
        </div>
      </div>
    </div>
  );
};

export default CreateAccount;
