.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .welcome-section {
    background-color: white;
    border-radius: var(--radius-lg);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    box-shadow: var(--shadow-sm);
  }
  
  .welcome-section h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .welcome-section p {
    color: var(--text-secondary);
  }
  
  .dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
  }
  
  .goals-section,
  .streak-section {
    background-color: white;
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .section-header h3 {
    font-size: 18px;
    font-weight: 600;
  }
  
  .progress-circle {
    position: relative;
    width: 60px;
    height: 60px;
  }
  
  .progress-circle svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }
  
  .progress-circle-bg {
    fill: none;
    stroke: #f0f0f0;
    stroke-width: 2.8;
  }
  
  .progress-circle-fill {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 2.8;
    stroke-linecap: round;
  }
  
  .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    font-weight: 600;
  }
  
  .goals-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .goal-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid;
  }
  
  .goal-item:nth-child(1) {
    border-left-color: var(--primary-color);
  }
  
  .goal-item:nth-child(2) {
    border-left-color: var(--secondary-color);
  }
  
  .goal-item:nth-child(3) {
    border-left-color: var(--accent-color);
  }
  
  .goal-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
  }
  
  .quiz-icon {
    background-color: var(--primary-light);
    color: var(--primary-color);
  }
  
  .notes-icon {
    background-color: var(--secondary-light);
    color: var(--secondary-color);
  }
  
  .video-icon {
    background-color: var(--accent-light);
    color: var(--accent-color);
  }
  
  .goal-info {
    flex: 1;
  }
  
  .goal-info h4 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .goal-info p {
    color: var(--text-tertiary);
    font-size: 14px;
  }
  
  .streak-section {
    display: flex;
    flex-direction: column;
  }
  
  .streak-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  
  .streak-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .streak-icon {
    width: 60px;
    height: 60px;
    background-color: #fff3e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }
  
  .flame-icon {
    font-size: 28px;
  }
  
  .streak-days {
    font-size: 18px;
    font-weight: 600;
    color: var(--warning-color);
  }
  
  .streak-section p {
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 20px;
  }
  
  .streak-progress {
    margin-top: auto;
  }
  
  .streak-bar {
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  
  .streak-fill {
    height: 100%;
    background-color: var(--warning-color);
    border-radius: 4px;
  }
  
  .streak-progress p {
    font-size: 14px;
    color: var(--text-tertiary);
  }
  
  .tools-section {
    background-color: white;
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-sm);
  }
  
  .tools-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  
  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }
  
  .tool-card {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
  }
  
  .tool-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
  }
  
  .tool-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
  }
  
  .video-tool {
    background-color: var(--primary-light);
    color: var(--primary-color);
  }
  
  .idea-tool {
    background-color: var(--success-light);
    color: var(--success-color);
  }
  
  .animation-tool {
    background-color: var(--secondary-light);
    color: var(--secondary-color);
  }
  
  .interview-tool {
    background-color: var(--warning-light);
    color: var(--warning-color);
  }
  
  .lightbulb-icon {
    font-size: 24px;
  }
  
  .tool-card h4 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .tool-card p {
    color: var(--text-tertiary);
    font-size: 14px;
  }
  
  @media (max-width: 768px) {
    .dashboard-content {
      grid-template-columns: 1fr;
    }
  
    .tools-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
  }
  