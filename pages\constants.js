
export const API = {
  BREAKDOWN: "/api/breakdown",
  ANIMATION: "/api/animation",
  TEACHER: "/api/teacher",
  GET_KEYPOINTS: "/api/keypoints",
  GET_QUIZ: "/api/quiz",
  GET_DOUBT: "/api/doubt",
};

export const TRANSITIONS = {
  DURATION: 400, // in ms
  TYPEWRITER_SPEED: 50, // in ms per character
};

export const API_URLS = {
  PDF_UPLOAD_API: "https://api.aiclassroom.in/pdfUpload/upload",
  SUMMARY_CHUNKS_API: "https://-0d35868-v10.app.beam.cloud",
  TEACHER_VIDEO_API: "https://-3287ab4-v18.app.beam.cloud",
  IMAGE_API: "https://sdxl--eaa3a25-v6.app.beam.cloud",
  AUDIO_PROCESSING_API: "https://-processing-22f6d13-v3.app.beam.cloud",
  LENGTH_API: "https://api.example.com/lengths",
  
  // AWS S3 credentials - Note: In a production app, these would be secured in environment variables
  AWS_ACCESS_KEY: "********************",
  AWS_SECRET_KEY: "rDJKsXjRsPComZE1goGqYs29m7A1ckRUcwcfSi+9"
};

// API auth token
export const API_AUTH_TOKEN = "vpF1tVtHk4EzLfk2dDI-sIdhRcprdOdTirRj9z70V0_MHFy8CPX6pFPLMuV0tkqs_esUL-5Zabj6s2Fj9OPRqg==";

export const API_AUTH_TOKEN_AUDIO="Bearer bvbdV-lLGcQ_hJ4xqcpaPPPufrwFolpozy3tLTaoZXHMrkIj4rSWKte5qYkahQJ8Bdo6O1daoSZislHBRJ-CUA=="