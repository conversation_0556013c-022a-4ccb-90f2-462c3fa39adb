import { useState, useEffect, useRef, use } from "react";
import { useSearchParams } from "react-router-dom";

import { useLocation,useNavigate } from "react-router-dom";
import { useUploadPdf } from "./useUploadPdf";
import bgmusic from "./bg-music-1.mp3";
import OpenAI from "openai";
import dream from "./dream-painting-ai-192126.mp3";
import confidentinnovative from "./background-marketing-confident-innovative-explainer-electronic-music-20858.mp3";
import coniferousforest from "./coniferous-forest-142569.mp3";
import upbeats from "./for-her-chill-upbeat-summel-travel-vlog-and-ig-music-royalty-free-use-202298.mp3";
import infographicmusic from "./infographic-music-calm-tutorial-explainer-background-intro-theme-274859.mp3";
import inspirational from "./inspirational-motivational-music-311778.mp3";
import instrumental from "./instrumental-kizomba-verdades-x-guetto-zouk-instrumental-149699.mp3";
import junglebeat from "./jungle-ish-beat-for-video-games-314073.mp3";
import lightinstrumental from "./light-instrumental-melody-313329.mp3";
import lofibackgroundmusic from "./lofi-background-music-309034.mp3";
import midnightforest from "./midnight-forest-184304.mp3";
import moonlitshoreai from "./moonlit-shore-ai-193399.mp3";
import DisclaimerPage from "./DisclaimerPage";
import chill from "./morning-garden-acoustic-chill-15013.mp3";
import oldschool from "./old-school-124444.mp3";
import presentation from "./presentation-corporate-219493.mp3";
import smoke from "./smoke.mp4";
import rainveilai from "./rain-veil-ai-192972.mp3";
import ProfessionalEdTechLoader from "./ProfessionalEdTechLoader";
import TypingEffect from "./TypingEffect";
import Male from "./male.png";
import Female from "./female.png";
import EducationalPage from "./EducationalPage";
import playImage from "./play.png"
import prevImage from "./previous.png"
import Sider from "./sider.png"
import pauseImage from "./pause.png"
import capImage from "./Vector.png"
import solar_play from "./solar_play-bold.png"
import LoadingPage from "./Loader";
import { Mic, Star, X } from "lucide-react"; // Optional: Install Lucide or use SVGs
import DisclaimerModal from "./DisclaimerModal";

//music list below

const musiclist = [
  "dream",
  "confidentinnovative",
  "coniferousforest",
  "upbeats",
  "infographicmusic",
  "inspirational",
  "instrumental",
  "junglebeat",
  "lightinstrumental",
  "lofibackgroundmusic",
  "midnightforest",
  "moonlitshoreai",
  "chill",
  "oldschool",
  "presentation",
  "rainveilai",
];
const fakepost=()=>{
  fetch("https://api.aiclassroom.in/generate-image", {
  method: "POST",
  headers: {
    "Content-Type": "application/json"
  },
  body: JSON.stringify({ sentence:"Successfull Image"})
});
}
const UploadPdfComponent = () => {
  let { uploadPdf, uploading, fileUrl, error,setFileUrl } = useUploadPdf();
  const [disclaimer,setDisclaimer]=useState(true);
  const [selectedFile, setSelectedFile] = useState(null);
  const [summary, setSummary] = useState(null);
  const [showtitle,setshowtitle]=useState(true);
  const[caller,setCaller]=useState(false);
  const [showControls, setShowControls] = useState(false);
  const [showIcon, setshowIcon] = useState(false)
  const [chunks, setChunks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedChunkIndex, setSelectedChunkIndex] = useState(null);
  const [displayedSentences, setDisplayedSentences] = useState([]);
  const [showSidebar, setShowSidebar] = useState(true);
  const [autoplay, setAutoplay] = useState(true);
  const [lastaudiosrc,setlastaudiosrc]=useState("");
  const [lastaudiotime,setlastaudiotime]=useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [AudioUrl, setAudioUrl] = useState(null);
  const [isfullscreen, setIsFullscreen] = useState(false);
  const [finalVideos, setfinalVideos] = useState([]);
  const [chunkLoaded, setChunkLoaded] = useState(false);
  const [captionsOn, setCaptionsOn] = useState(false);
  const [markedsentence, setmarkedsentence] = useState([]);
  const [makechunk, setmakechunk] = useState(2); 
  const [isdoubtsubmitted, setIsDoubtsubmitted] = useState(false);
  const [isDoubtasked, setIsDoubtaske] = useState(false);
  const [isDoubtLoading,setIsDoubtLoading]=useState(false)
  const [doubtImages, setDoubtImages] = useState([]);
  const [doubtSolution, setDoubtSolution] = useState([]);
  const [doubtvalue,setDoubtask]=useState("");
  const [darkMode, setDarkMode] = useState(
    () => localStorage.getItem("darkMode") === "true"
  );
  const [visible, setVisible] = useState(true);
  const [translated,setTranslated]=useState([]);
  const [solution, setSolution] = useState(null);
  const [voiceURI, setVoiceURI] = useState(null);
  const [AudioUrls, setAudioUrls] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loop, setLoop] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentDoubtImageIndex, setCurrentDoubtImageIndex] = useState(0);
  const [chunkImageCache, setChunkImageCache] = useState({});
  const [audioCache, setAudioCache] = useState({});
  const [voices, setVoices] = useState([]);
  const [backgroundMusic, setBackgroundMusic] = useState(null);
  const [chunkMusic, setChunkMusic] = useState([]);
  const [doubtimagecache, setDoubtImageCache] = useState({});
  const [doubtchunk, setDoubtchunk] = useState(null);
  const [doubtloaded, setDoubtloaded] = useState(false);
  const [gender, setGender] = useState(["Male", "Female"]);
  const [genderURI, setgenderURI] = useState("");
  const [doubtperchase, setDoubtperchase] = useState({});
  const [recording, setrecording] = useState(false);
  const [doubtquery, setdoubtquery] = useState("");
  const synthRef = useRef(window.speechSynthesis);
  const [chunktitle, setchunktitle] = useState("");
  const audioRef = useRef(null);
  const [chunktitles, setchunktitles] = useState([]);
  const [titleimages, settitleimages] = useState([]);
  const [nextepisode, setnextepisode] = useState(false);
  const [Imageslist, setImageslist] = useState([]);
  const [selectedOption, setSelectedOption] = useState("all");

  const [images, setImages] = useState(null);
  const [selectedImages, setSelectedImages] = useState(null);
  const [fromPage, setFromPage] = useState("");
  const [toPage, setToPage] = useState("");
  const [rating, setRating] = useState(0);
  const [hovered, setHovered] = useState(0);
  const [comment, setComment] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [showDisclaimerModal, setShowDisclaimerModal] = useState(false);
  const [disclaimerShown, setDisclaimerShown] = useState(false);

  const nav=useNavigate()
useEffect(()=>{
  if(!disclaimer) return;
  if(selectedChunkIndex==0){
    setTimeout(()=>{
      setDisclaimer(false);
    },2000)
  }
},[selectedChunkIndex])
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < window.innerHeight) {
        setVisible(false);
      } else {
        setVisible(true);
      }
    };

    handleResize(); 
    window.addEventListener("resize", handleResize); 

    return () => window.removeEventListener("resize", handleResize); 
  }, []);


  const handleSubmit = () => {
    // Here you can send data to backend
    setSubmitted(true);
  };
 useEffect(() => {
  if(!showtitle)return;
  if(!disclaimer)
setTimeout(() => {
    setshowtitle(false);
  }, 2000);
  else
setTimeout(() => {
    setshowtitle(false);
  }, 4000);
}, [showtitle]);


  const toggleCaptions = () => {
    setCaptionsOn(!captionsOn);
  };

  const iconBtn = {
    background: "none",
    border: "none",
    color: "white",
    fontSize: "18px",
    cursor: "pointer",
  };
  const handleFileChange = (e) => setSelectedFile(e.target.files[0]);
  // const handleUpload = () => selectedFile && uploadPdf(selectedFile);
  const AudioRef = useRef(null);
  const deleteImage = (indexToDelete) => {
    setSelectedImages((prev) => prev.filter((_, idx) => idx !== indexToDelete));
  };

  const toggleImageSelection = (img) => {
    setSelectedImages((prev) =>
      prev.includes(img) ? prev.filter((i) => i !== img) : [...prev, img]
    );
  };
  const [searchParams] = useSearchParams();
  const id = searchParams.get("id");  
  function generateRandomCode(length = 5) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
  useEffect(() => {
    if (!fileUrl) return;
    const imgdata=async()=>{ 
      const response = await fetch(
        "https://py.aiclassroom.in/process/",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({file_url:fileUrl,file_uid:generateRandomCode()}),
        }
      )
      const data = await response.json();
      console.log(data.Pdf_Pages_Data);
      setImages(data.Pdf_Pages_Data);
      setSelectedImages(data.Pdf_Pages_Data);
    }
    imgdata();
  }, [fileUrl]);
 useEffect(() => {
  const letsbegin = async () => {
    if (voiceURI === "English") return;

    let translated = [];

    for (let i = 0; i < chunks.length; i++) {
      const sentences = chunks[i].split(".").filter(Boolean); // avoid empty strings
      const audioUrl3 = [];

      const promises = sentences.map(async (sentence) => {
        try {
          const openai = new OpenAI({
            apiKey:             "********************************************************************************************************************************************************************",
 // Secure this key!
            dangerouslyAllowBrowser: true,
          });

          const res = await openai.chat.completions.create({
            model: "gpt-3.5-turbo",
            messages: [
              {
                role: "system",
                content: `
Translate this to: "${voiceURI}". 
Do NOT use English. No comments, no explanations. Just meaningful translation.
                `,
              },
              { role: "user", content: sentence + "." },
            ],
            max_tokens: 1000,
          });

          const translatedText = res.choices[0].message.content.trim();

          const voiceLang = voiceuriMS(voiceURI, genderURI).split("IN")[0];
          const voiceName = voiceuriMS(voiceURI, genderURI);

          const ssml = `
<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='${voiceLang}IN'>
  <voice xml:lang='${voiceLang}IN' xml:gender='${genderURI}' name='${voiceName}'>
    <prosody rate='-10.00%'>
      ${translatedText}
    </prosody>
  </voice>
</speak>`;

          const ttsRes = await fetch(
            "https://eastus.tts.speech.microsoft.com/cognitiveservices/v1",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-32kbitrate-mono-mp3",
                "Ocp-Apim-Subscription-Key":                   "5cgtafcR3A0Cl6szTwjHr48KtVTXTHJLfaK2z8Rhh5KAD10yxjpMJQQJ99AKACYeBjFXJ3w3AAAYACOGXOKF",
 // Secure this key too!
              },
              body: ssml,
            }
          );

          const arrayBuffer = await ttsRes.arrayBuffer();
          const audioBlob = new Blob([arrayBuffer], { type: "audio/mpeg" });
          const audioUrl2 = URL.createObjectURL(audioBlob);
                  return { audioUrl2, translatedText };
        } catch (error) {
          console.error("Error in translation:", error);
          return null;
        }
      });

      const translatedSentences = await Promise.all(promises);
const audioUrlsForChunk = translatedSentences.map((r) => r.audioUrl2);
    const translatedForChunk = translatedSentences.map((r) => r.translatedText);
      setAudioUrls((prev) => {
        const newAudioUrls = [...prev];
        newAudioUrls[i] = audioUrlsForChunk;
        return newAudioUrls;
      });

      setTranslated((prev) => {
        const newTranslated = [...prev];
        newTranslated[i] = translatedForChunk;
        return newTranslated;
      });
    }
  };

  if (chunks.length > 0 && voiceURI) {
    letsbegin();
  }
}, [chunks]);
  useEffect(() => {
    if (id) {
      alert(id)
      const timer = setTimeout(async() => {
        const result=await fetch("https://api.aiclassroom.in/validate",{
          method:"POST",
          headers:{
            "Content-Type":"application/json"
          },
          body:JSON.stringify({
            id
          })
        })
        if(result.ok){
          alert("okay")
          const response=await result.json();
          console.log(response)
          setFileUrl(response.link)
          const deleted =await fetch("https://api.aiclassroom.in/sharecode",{
            method:"DELETE",
            headers:{
              "Content-Type":"application/json"
            },
            body:JSON.stringify({
              id
            })
          });
          if(deleted.ok){
            console.log("deleted successfully")
          }
        }
      }, 1000); // Delay of 1 second

      return () => clearTimeout(timer);
    }
  },[]);
  const annotaions=null;
useEffect(()=>{
  if(recording){
    recordDoubt() 
   }else{
    return;
  }
},[recording])
  const formatTime = (seconds) => {
    const min = Math.floor(seconds / 60) || 0;
    const sec = Math.floor(seconds % 60) || 0;
    return `${min}:${sec < 10 ? "0" : ""}${sec}`;
  };

  useEffect(() => {
    setVoices(
      [
        "Hindi",
        "English",
        "Bengali",
        "Oriya",
        "Punjabi",
        "Assamese",
        "Kannada",
        "Mallayalam",
        "Marathi",
        "Telugu",
        "Tamil",
        "Urdu",
      ].map((lang) => ({
        name: lang,
        voiceURI: lang,
      }))
    );
  }, []);

  const musicsrc = (x) => {
    switch (x) {
      case "dream":
        return dream;
      case "confidentinnovative":
        return confidentinnovative;
      case "coniferousforest":
        return coniferousforest;
      case "upbeats":
        return upbeats;
      case "infographicmusic":
        return infographicmusic;
      case "inspirational":
        return inspirational;
      case "instrumental":
        return instrumental;
      case "junglebeat":
        return junglebeat;
      case "lightinstrumental":
        return lightinstrumental;
      case "lofibackgroundmusic":
        return lofibackgroundmusic;
      case "midnightforest":
        return midnightforest;
      case "moonlitshoreai":
        return moonlitshoreai;
      case "chill":
        return chill;
      case "oldschool":
        return oldschool;
      case "presentation":
        return presentation;
      case "rainveilai":
        return rainveilai;
      default:
        return bgmusic;
    }
  };

  const voiceuriMS = (voiceURI, gender) => {
    if (gender === "Female") {
      switch (voiceURI) {
        case "Hindi":
          return "hi-IN-SwaraNeural";
        case "English":
          return "en-IN-NeerjaNeural";
        case "Bengali":
          return "bn-IN-TanishaaNeural";
        case "Oriya":
          return "or-IN-SubhasiniNeural";
        case "Punjabi":
          return "pa-IN-OjasNeural";
        case "Assamese":
          return "as-IN-YashicaNeural";
        case "Kannada":
          return "kn-IN-SapnaNeural";
        case "Mallayalam":
          return "ml-IN-SobhanaNeural";
        case "Marathi":
          return "mr-IN-AarohiNeural";
        case "Telugu":
          return "te-IN-ShrutiNeural";
        case "Tamil":
          return "ta-IN-PallaviNeural";
        case "Urdu":
          return "ur-IN-GulNeural";
        default:
          return "";
      }
    } else {
      switch (voiceURI) {
        case "Hindi":
          return "hi-IN-ArjunNeural";
        case "English":
          return "en-IN-PrabhatNeural";
        case "Bengali":
          return "bn-IN-BashkarNeural";
        case "Oriya":
          return "or-IN-SukantNeural";
        case "Punjabi":
          return "pa-IN-OjasNeural";
        case "Assamese":
          return "as-IN-PriyomNeural";
        case "Kannada":
          return "kn-IN-GaganNeural";
        case "Mallayalam":
          return "ml-IN-MidhunNeural";
        case "Marathi":
          return "mr-IN-ManoharNeural";
        case "Telugu":
          return "te-IN-MohanNeural";
        case "Tamil":
          return "ta-IN-ValluvarNeural";
        case "Urdu":
          return "ur-IN-SalmanNeural";
        default:
          return "";
      }
    }
  };
  const summarizeContent = async () => {
    if (!fileUrl) return;
    try {
      fakepost();

      setLoading(true);
      const response = await fetch(
        "https://py.aiclassroom.in/summarize/",
        {
          method: "POST",
          headers: {
            Connection: "keep-alive",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ uid:"112",desired_language:voiceURI,img_list:selectedImages }),
        }
      );
      if (!response.ok) {
        throw new Error("Failed to fetch data from the server");
      }
      const data = await response.json();
      if(data.is_math){
        alert("Math content detected, please upload a different file")
        window.location.href="/";
        return;
      }
      if(data.is_NSFW){
        alert("NSFW content detected, please upload a different file")
        window.location.href="/";
        return;
      }
      console.log(JSON.parse(data.topic_music_json));
      //const data={summary:"help me in my area",chunks:["helps","my area","your area"]}
      setSummary(data.topic_concatenated_chunks);

      const openai1 = new OpenAI({
        apiKey:
          "********************************************************************************************************************************************************************",
        dangerouslyAllowBrowser: true,
      });
      console.log(data.topic_concatenated_chunks);
      setChunks(data.topic_concatenated_chunks);
      console.log(chunks);
      await new Promise((resolve) => setTimeout(resolve, 5000));
      let chunk = data.topic_concatenated_chunks;
      for(let i=0;i<(2<chunk.length?2:chunk.length);i++){
        const sentences = chunk[i].split(".") || [];
        setmarkedsentence((prev) => [...prev, sentences]);
        sentences.pop(); // Remove the last empty element if it exists
        const promises = sentences.map(async (sentence) => {
          try {
            const res = await fetch(
              "https://api.aiclassroom.in/generate-image",
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json"
                },
                body: JSON.stringify({sentence}),
              }
            );
            const imgData = await res.json();
            return new Promise((resolve, reject) => {
              const img = new Image();
              img.src = imgData.data.image;
              img.onload = () => resolve(imgData.data.image);
              img.onerror = () => reject("Image failed to load");
            });
          } catch (err) {
            console.error("Error generating image:", err);
            return null;
          }
        });
        const loadedImages = await Promise.all(promises);
        setImageslist((prev) => {
          const newImagesList = [...prev];
          newImagesList[i] = loadedImages;
          return newImagesList;
        });
      }
      if(voiceURI!=="English"){
      const res1 = await openai1.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `Translate into ${voiceURI} language`,
          },
          { role: "user", content: `${JSON.parse(data.topic_music_json).topic_title}` },
        ],
      });
      const chunkSummary = res1.choices[0].message.content.trim();
      console.log(chunkSummary);
      setchunktitle(chunkSummary);
    }else{
      const chunkSummary = JSON.parse(data.topic_music_json).topic_title;
      setchunktitle(chunkSummary);
    }
      setchunktitles(JSON.parse(data.topic_music_json).titles);
      let p = JSON.parse(data.topic_music_json).titles;

      const imagePromises = p.map(async (prompt) => {
        try {
          const res = await fetch(
            "https://api.aiclassroom.in/generate-image",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({sentence:prompt}),
            }
          );
          
    
          const imgData = await res.json();
          return new Promise((resolve, reject) => {
            const img = new Image();
            img.src = imgData.data.image;
            img.onload = () => resolve(imgData.data.image);
            img.onerror = () => reject("Image failed to load");
          });
      
        } catch (error) {
          console.error(`Error fetching image for prompt "${prompt}":`, error);
          return null;
        }
      });
      let title1 = (await Promise.all(imagePromises)).filter(Boolean);      
      if(voiceURI!=="English"){
        let titles=JSON.parse(data.topic_music_json).titles;
        let finaltitles=[];
        for(let i=0;i<titles.length;i++){

      const res4 = await openai1.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `convert into ${voiceURI}, i will do JSON.parse on your output`,
          },
          { role: "user", content: `${titles[i]}` },
        ],
      });
      const chunkMusic2 = res4.choices[0].message.content.trim();
      finaltitles.push(chunkMusic2);
    }
      setchunktitles(finaltitles);
    }
alert("Generation Completed");   
 settitleimages(title1);
      setChunkMusic(JSON.parse(data.topic_music_json).musics);
      setSelectedChunkIndex(0);
    } catch (error) {
      console.error("Summarization error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleNextChunk = () => {
    if (chunks.length === 0) return;
    setSelectedChunkIndex((prev) => {
      const next = prev === null || prev === chunks.length - 1 ? 0 : prev + 1;
      return loop && next === 0 && prev === chunks.length - 1 ? 0 : next;
    });
  };
  
  const recordDoubt = async () => {
    try {
      const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
      recognition.lang = voiceURI === "English" ? "en-US" : voiceURI.toLowerCase();
      recognition.interimResults = false;
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log("Voice recognition started. Speak into the microphone.");
      };

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        console.log("Voice recognized:", transcript);
        setdoubtquery(transcript);
      };
      recognition.onerror = (event) => {
        console.error("Voice recognition error:", event.error);
      };
      recognition.onend = () => {
        console.log("Voice recognition ended.");
      };
      recognition.start();
    } catch (error) {
      console.error("Speech recognition is not supported in this browser.", error);
      alert("Speech recognition is not supported in this browser.");
    }
  };
    
useEffect(() => {
  if(makechunk > chunks.length){
return;  }
if(Imageslist[makechunk]){
  return;
}

try{
const sentence=chunks[makechunk].split(".") || [];
const tohandle=async()=>{
const promises = sentence.map(async (sentence) => {
  let chunkUrl=[]
  try {
    const res = await fetch(
      "https://api.aiclassroom.in/generate-image",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({sentence}),
      }
    );

    const imgData = await res.json();
    console.log(imgData)
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = imgData.data.image;
      img.onload = () => resolve(imgData.data.image);
      img.onerror = () => reject("Image failed to load");
    });

  } catch (err) {
    console.error("Error generating image:", err);
    return null;
  }

});
const loadedImages = await Promise.all(promises)
setImageslist((prev) => {
  const newImagesList = [...prev];
  newImagesList[makechunk] = loadedImages;
  return newImagesList;
});
}

tohandle()
setmakechunk(makechunk+1)
}catch(err){
  console.log(err);
}
},[selectedChunkIndex])

  useEffect(() => {
    if (selectedChunkIndex === null || !chunks[selectedChunkIndex]) return;
    const text = chunks[selectedChunkIndex];
    const sentences = text.split(".") || [];
    sentences.pop(); // Remove the last empty element if it exists
    console.log("Selected Chunk sentences: ", selectedChunkIndex, sentences);
    const preloadContent = async () => {
      const audioFiles = [];
      const images = [];
      let p = 0;
      if(!Imageslist[selectedChunkIndex]){
try{
        for(let i=0;i<=sentences.length;i++){
          const sentence=sentences[i];
          const imgData=await fetch(
            "https://api.aiclassroom.in/generate-image",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({sentence}),
            }
          );
          const imgData1 = await imgData.json();
          images.push(imgData1.data.image);
        }
        setImageslist((prev) => {
          const newImagesList = [...prev];
          newImagesList[selectedChunkIndex] = images;
          return newImagesList;
        });
      }    
      catch(error){
        alert("Please try again",error);
        return;
      }} 
           for (const sentence of sentences) {
        let sentence_dup = sentence;
        if (voiceURI !== "English") {
         if(translated[selectedChunkIndex]){
          sentence_dup = translated[selectedChunkIndex][p];
          }else{
          try {
            const openai = new OpenAI({
              apiKey:
                "********************************************************************************************************************************************************************",
              dangerouslyAllowBrowser: true,
            });
            const res = await openai.chat.completions.create({
              model: "gpt-3.5-turbo",
              messages: [
                {
                  role: "system",
                  content:`
                      completely Translate this to language : "${voiceURI}".
    Don't use English!, Keep it Meaningfully intact.
    - Do NOT add any extra text, comments, explanations, or notes.
                  `
                },
                { role: "user", content: sentence+"." },
              ],
              max_tokens: 1000,
            });
            sentence_dup = res.choices[0].message.content.trim();
            console.log("sentence_dup", sentence_dup);
            console.log("sentence", sentence);
          } catch (e) {
            console.log(e);
          }
        }
      } 
      if(!AudioUrls[selectedChunkIndex]){
        try {
          const ssml = `
<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='${
            voiceuriMS(voiceURI, genderURI).split("IN")[0]
          }IN'>
  <voice xml:lang='${
    voiceuriMS(voiceURI, genderURI).split("IN")[0]
  }IN' xml:gender='${genderURI}' name='${voiceuriMS(voiceURI, genderURI)}'>    
        <prosody rate="-10.00%">
    ${sentence_dup}
    </prosody>
  </voice>
</speak>`;
          const ttsRes = await fetch(
            "https://eastus.tts.speech.microsoft.com/cognitiveservices/v1",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-32kbitrate-mono-mp3",
                "Ocp-Apim-Subscription-Key":
                  "5cgtafcR3A0Cl6szTwjHr48KtVTXTHJLfaK2z8Rhh5KAD10yxjpMJQQJ99AKACYeBjFXJ3w3AAAYACOGXOKF",
              },
              body: ssml,
            }
          );

          const arrayBuffer = await ttsRes.arrayBuffer();
          const audioBlob = new Blob([arrayBuffer], { type: "audio/mpeg" });
          const audioUrl = URL.createObjectURL(audioBlob);

          audioFiles.push(audioUrl);
        
          sentences[p] = sentence_dup;
          console.log("Viewing Images",Imageslist);
          const imgData = Imageslist[selectedChunkIndex][p];
          p++;
          images.push(imgData);
        } catch (err) {
          console.error("Error fetching content:", err);
          audioFiles.push(null);
          images.push(null);
        }
        }else{
          audioFiles.push(AudioUrls[selectedChunkIndex][p]);
          sentences[p] = translated[selectedChunkIndex][p];
          const imgData = Imageslist[selectedChunkIndex][p];
          p++;
          images.push(imgData);
        }
      }

      const updatedCache = {
        ...audioCache,
        [selectedChunkIndex]: audioFiles,
      };
      localStorage.setItem("audioCache", JSON.stringify(updatedCache));
      setChunkImageCache((prev) => ({
        ...prev,
        [selectedChunkIndex]: images,
      }));
      setCurrentImageIndex(0);
      setAutoplay(true);
      playContent();
    };

    const playContent = () => {

      audioRef.current.src = musicsrc(chunkMusic[selectedChunkIndex]);
      audioRef.current.volume = 0.1;
      audioRef.current.play();
      const audioCache1 = JSON.parse(localStorage.getItem("audioCache")) || {};
      const audioFiles = audioCache1[selectedChunkIndex] || [];
      const images = chunkImageCache[selectedChunkIndex] || [];
      let i = 0;
      console.log("audioFiles", audioFiles);
      console.log("images", images);

      const playNext = () => {
        if (i == 0) {
          setnextepisode(false);
        }
        if (i == audioFiles.length - 1) {
          if (selectedChunkIndex < chunks.length - 1){
            setnextepisode(true);
          }
        }
        if (i >= audioFiles.length) return;
        
        const sentence = sentences[i];
        setDisplayedSentences([sentence]);
        setCurrentImageIndex(i);

        if (audioFiles[i]) {
                          AudioRef.current.src=audioFiles[i];

          if(i==0){
            if(selectedChunkIndex==0){
              setTimeout(() => {
                AudioRef.current.play();
              }, 4000);
            }else{
              setTimeout(() => {
                AudioRef.current.play();
              }, 2000);
            }}else{
          AudioRef.current.play();
            }
          AudioRef.current.onended = () => {
            if (i == audioFiles.length - 1) {
              const current = selectedChunkIndex;
              if (autoplay) {
                if (selectedChunkIndex == current) {
                  setSelectedChunkIndex(selectedChunkIndex + 1);
                  setCurrentImageIndex(0);
                  setnextepisode(false);
                  setshowtitle(true);
                  setDoubtchunk(null);
                  setnextepisode(false);
                }
              }
            }
            i++;
            if (i < audioFiles.length) {
              setTimeout(playNext, 500);
            }
          };
        }
      };

      playNext();
    };

    preloadContent();

    return () => {
      setAudioCache({});
      setChunkImageCache({});
      localStorage.removeItem("audioCache");
      setDisplayedSentences([]);
      setCurrentImageIndex(0);
    };
  }, [selectedChunkIndex, voiceURI, genderURI]);
  useEffect(() => {
    doubtplayer(solution);
  },[solution]);

  const doubtplayer = (solution) => {
    if (doubtchunk == null || !doubtperchase[doubtchunk]) {
      console.log("returning");
      return;
    }
    const text = doubtperchase[doubtchunk][0].solution;
    const sentences = solution.match(/[^\.!\?]+[\.!\?]+/g) || [];
    const preloadContent = async () => {
      const audioFiles = [];
      const images = [];
      for (const sentence of sentences) {
        let sentence_dup = sentence;
        if (voiceURI !== "English") {
          try {
            const openai = new OpenAI({
              apiKey:
                "********************************************************************************************************************************************************************",
              dangerouslyAllowBrowser: true,
            });
            const res = await openai.chat.completions.create({
              model: "gpt-3.5-turbo",
              messages: [
                {
                  role: "system",
                  content:`
                   completely Translate this to language : "${voiceURI}".
    Don't use English!, Keep it Meaningfully intact, always end with closing symbol like in Hindi with |.
    - Do NOT add any extra text, comments, explanations, or notes.
                  `
                },
                { role: "user", content: sentence+"." },
              ],
              max_tokens: 1000,
            });
            sentence_dup = res.choices[0].message.content.trim();
            console.log("sentence_dup", sentence_dup);
            console.log("sentence", sentence);
          } catch (e) {
            console.log(e);
          }
        }
        try {
          const ssml = `
<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='${
            voiceuriMS(voiceURI, genderURI).split("IN")[0]
          }IN'>
  <voice xml:lang='${
    voiceuriMS(voiceURI, genderURI).split("IN")[0]
  }IN' xml:gender='${genderURI}' name='${voiceuriMS(voiceURI, genderURI)}'>    
        <prosody rate="-10.00%">
    ${sentence_dup}
    </prosody>
  </voice>
</speak>`;
          const ttsRes = await fetch(
            "https://eastus.tts.speech.microsoft.com/cognitiveservices/v1",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-32kbitrate-mono-mp3",
                "Ocp-Apim-Subscription-Key":
                  "5cgtafcR3A0Cl6szTwjHr48KtVTXTHJLfaK2z8Rhh5KAD10yxjpMJQQJ99AKACYeBjFXJ3w3AAAYACOGXOKF",
              },
              body: ssml,
            }
          );
          const arrayBuffer = await ttsRes.arrayBuffer();
          const audioBlob = new Blob([arrayBuffer], { type: "audio/mpeg" });
          const audioUrl = URL.createObjectURL(audioBlob);
          audioFiles.push(audioUrl);
          const res = await fetch(
            "https://api.aiclassroom.in/generate-image",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({sentence}),
            }
          );
          const imgData = await res.json();
          console.log(imgData)

          images.push(imgData.data.image);
        } catch (err) {
          console.error("Error fetching content:", err);
          audioFiles.push(null);
          images.push(null);
        }
      }
      const updatedCache = {
        ...audioCache,
        [doubtchunk]: audioFiles,
      };
      localStorage.setItem("doubtaudioCache", JSON.stringify(updatedCache));
      setDoubtImageCache((prev) => ({
        ...prev,
        [doubtchunk]: images,
      }));
      setCurrentDoubtImageIndex(0);
      playContent();
    };
    const playContent = () => {
      const audioCache1 = JSON.parse(localStorage.getItem("doubtaudioCache")) || {};
      const audioFiles = audioCache1[doubtchunk] || [];
      const images = doubtimagecache[doubtchunk] || [];
      let i = 0;
      console.log("audioFiles", audioFiles);
      console.log("images", images);
      
      const playNext = () => {
        if (i >= audioFiles.length){
          return;}

      
        const sentence = sentences[i];
        setDisplayedSentences([sentence]);
        console.log("sentence", sentence);
        setCurrentDoubtImageIndex(i);
        console.log("DoubtImageIndex", currentDoubtImageIndex);

        if (audioFiles[i]) {
          AudioRef.current.src = audioFiles[i];
          AudioRef.current.play();
          if(i==0){setIsDoubtsubmitted(false)  ; 
            if(isfullscreen){
              setTimeout(()=>{
            document.getElementById("container").requestFullscreen();},1000)}
               setrecording(false);
               setDoubtloaded(true)
            }
          AudioRef.current.onended = () => {
            if (i == audioFiles.length - 1) {
              if(AudioRef.current.isPlaying){
                AudioRef.current.pause();
              }
              setDoubtchunk(null);
              setDoubtloaded(false)
              AudioRef.current.src = lastaudiosrc;
              AudioRef.current.currentTime=lastaudiotime;
              AudioRef.current.play();
              setDoubtImageCache({});
              setCurrentDoubtImageIndex(0);
              setDoubtperchase({});
              setCaller(true)
            }
            i++;
            if (i < audioFiles.length) {
              setTimeout(playNext, 500);
            }
        }
      };
    }
      playNext();
    };
    preloadContent();
  };
  useEffect(() => {
    localStorage.setItem("darkMode", darkMode);
  }, [darkMode]);

  const downloadSummary = () => {
    const blob = new Blob([chunks.join("\n\n")], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "summary.txt";
    link.click();
    URL.revokeObjectURL(url);
  };
  const containerStyle = {
    padding: "2rem",
    backgroundColor: darkMode ? "#111827" : "#f3f4f6",
    color: darkMode ? "#fff" : "#000",
    minHeight: "100vh",
    fontFamily: "sans-serif",
  };

  useEffect(() => {
    // Show disclaimer modal before first chunk plays
    if (selectedChunkIndex === 0 && !disclaimerShown && chunks.length > 0) {
      setShowDisclaimerModal(true);
    }
  }, [selectedChunkIndex, disclaimerShown, chunks.length]);

  // Prevent video from playing until disclaimer is dismissed
  useEffect(() => {
    if (showDisclaimerModal) {
      // Pause audio if playing
      if (AudioRef.current) {
        AudioRef.current.pause();
      }
      if (audioRef.current) {
        audioRef.current.pause();
      }
    } else if (disclaimerShown && selectedChunkIndex === 0 && chunks.length > 0) {
      // Play audio after disclaimer is dismissed
      setTimeout(() => {
        if (AudioRef.current) {
          AudioRef.current.play();
        }
        if (audioRef.current) {
          audioRef.current.play();
        }
      }, 100); // slight delay to ensure modal is gone
    }
  }, [showDisclaimerModal, disclaimerShown, selectedChunkIndex, chunks.length]);

  return (
    <div style={containerStyle}>
      {showDisclaimerModal && (
        <DisclaimerModal
          onClose={() => {
            setShowDisclaimerModal(false);
            setDisclaimerShown(true);
          }}
        />
      )}
      <audio ref={AudioRef} style={{ display: "none" }}  onEnded={()=>{
        if(caller){
          setCaller(false)
          if(selectedChunkIndex<chunks.length){
            setSelectedChunkIndex(selectedChunkIndex+1);
            setCurrentImageIndex(0);
            setDoubtchunk(null);
          }
        }
      }}/>
      <audio
        src={backgroundMusic}
        ref={audioRef}
        loop
        style={{ display: "none" }}
      />
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          backgroundColor: darkMode ? "#1f2937" : "#fff",
          padding: "2rem",
          borderRadius: "10px",
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        }}
      >
        <div
          className={`flex justify-between items-center p-6 mt-5 ${
            darkMode ? "bg-gray-900" : "bg-white"
          } transition-colors duration-300 shadow-sm border-b ${
            darkMode ? "border-gray-700" : "border-gray-200"
          }`}
        >
          <div className="flex items-center text-center">
            <h1
              className={`text-2xl font-semibold ${
                darkMode ? "text-white" : "text-gray-800"
              } flex items-center`}
            >
              <span className="mr-3 text-indigo-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-6 h-6"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 14.5v-9l6 4.5-6 4.5z" />
                </svg>
              </span>
              {images && chunks.length === 0
                ? "Preview Your PDF":
              "Watch Your Animated Video"
              }
            </h1>
          </div>
{/*}
          <button
            onClick={() => setDarkMode(!darkMode)}
            className={`relative inline-flex items-center h-8 rounded-full w-14 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              darkMode ? "bg-indigo-600" : "bg-gray-200"
            }`}
            aria-label="Toggle dark mode"
          >
            <span
              className={`inline-block w-6 h-6 transform transition-transform duration-200 rounded-full bg-white shadow-md ${
                darkMode ? "translate-x-7" : "translate-x-1"
              }`}
            />
            <span className="sr-only">Toggle dark mode</span>
            <span
              className={`absolute left-1 text-xs ${
                darkMode ? "opacity-100" : "opacity-0"
              } transition-opacity`}
            >
              ☾
            </span>
            <span
              className={`absolute right-1 text-xs ${
                darkMode ? "opacity-0" : "opacity-100"
              } transition-opacity`}
            >
              ☀
            </span>
          </button>
          */}
        </div>
        {chunkImageCache[selectedChunkIndex] && (
          <></>)}
        {        (!fileUrl || !images) &&
 (
  <>
  {visible && (
            <div className="flex flex-col items-center justify-center h-60 w-full bg-white text-gray-800 rounded-xl shadow-lg">
            <div className="loader mb-4"></div>
            <p className="text-sm text-gray-500 animate-pulse">Loading...</p>
          
            <style>{`
              .loader {
                border: 4px solid rgba(0, 0, 0, 0.1);
                border-top: 4px solid #6366f1; /* Indigo-500 */
                border-radius: 50%;
                width: 48px;
                height: 48px;
                animation: spin 0.8s ease-in-out infinite;
                box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
              }
          
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}</style>
          </div>
          
  )}
  {!visible && (
    <div className="text-center text-sm text-red-600 font-medium p-4">
    🔁 Please rotate your device to <span className="font-semibold">landscape mode</span> for a better preview experience.
  </div>   )}
          </>
          )}
        {images && chunks.length === 0 && (
  <div className="flex flex-col  sm:flex-row lg:flex-row gap-6 p-4 max-h-screen overflow-y-auto">
    
    {/* Left: Image Preview */}
    <div className="flex-1 bg-gray-50 rounded-xl p-4 shadow-md max-h-[80vh]  overflow-y-auto">
      <div className="h-[300px] sm:h-[100px] md:h-[500px] lg:h-[600px]  overflow-y-scroll border rounded-lg bg-white p-3 space-y-6 shadow-inner">
        {selectedImages.map((img, index) => (
          <div key={index} className="relative">
            <img
              src={img}
              alt={`Page ${index + 1}`}
              className="w-full rounded-lg object-contain"
            />
            <button
              onClick={() => deleteImage(index)}
              className="absolute top-2 right-2 bg-black bg-opacity-60 text-white rounded-full w-7 h-7 flex items-center justify-center hover:bg-opacity-80 transition"
              title="Delete Page"
            >
              ×
            </button>
          </div>
        ))}
      </div>
    </div>

    {/* Right: Settings */}
    <div className="flex-1 bg-gray-100 rounded-xl p-6 shadow-md flex flex-col gap-6 max-h-[80vh] overflow-y-auto">
      
      {/* Pages Selection */}
      <div className="flex flex-col gap-2">
        <label className="font-bold">Pages</label>
        <div className="flex flex-col gap-2">
          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="pages"
              value="all"
              checked={selectedOption === "all"}
              onChange={() => {
                setSelectedOption("all");
                setSelectedImages(images);
              }}
            />
            All Pages
          </label>

          <label className="flex items-center gap-2">
            <input
              type="radio"
              name="pages"
              value="range"
              checked={selectedOption === "range"}
              onChange={() => setSelectedOption("range")}
            />
            Enter Page Number
          </label>

          {selectedOption === "range" && (
            <div className="flex flex-col md:flex-row md:items-center gap-2 pl-6">
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={fromPage}
                  onChange={(e) => {
                    const newFromPage = parseInt(e.target.value, 10);
                    setFromPage(newFromPage);
                    setSelectedImages(images.slice(newFromPage - 1, toPage));
                  }}
                  placeholder="From"
                  className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                />
                <span className="text-gray-500">to</span>
              </div>
              <input
                type="number"
                value={toPage}
                onChange={(e) => {
                  const newToPage = parseInt(e.target.value, 10);
                  setToPage(newToPage);
                  setSelectedImages(images.slice(fromPage - 1, newToPage));
                }}
                placeholder="To"
                className="w-24 px-2 py-1 border border-gray-300 rounded-md"
              />
            </div>
          )}
        </div>
      </div>

      {/* Language, Voice, and Generate Button in one row */}
      <div className="flex flex-row flex-wrap items-center justify-between gap-4 w-full">

        {/* Language Selector */}
        <div className="flex flex-col w-full sm:w-1/3">
          <label className="font-bold">Language</label>
          <select
            className="border p-2 rounded-lg"
            onChange={(e) => setVoiceURI(e.target.value)}
          >
            <option value="">Select your language</option>
            {voices.map((voice, idx) => (
              <option key={idx} value={voice.voiceURI}>
                {voice.name}
              </option>
            ))}
          </select>
        </div>

        {/* Voice Selector */}
        <div className="flex flex-col w-full sm:w-1/3">
          <label className="font-bold">Voice</label>
          <div className="flex justify-around gap-2">
            <div
              onClick={() => setgenderURI("Male")}
              className={`flex flex-col items-center cursor-pointer p-2 border rounded-full ${
                genderURI === "Male" ? "border-blue-600" : "border-gray-300"
              } transition`}
            >
              <img
                src={Male}
                alt="Male"
                className="w-12 h-12 object-cover rounded-full"
              />
              <span className="text-sm">Male</span>
            </div>
            <div
              onClick={() => setgenderURI("Female")}
              className={`flex flex-col items-center cursor-pointer p-2 border rounded-full ${
                genderURI === "Female" ? "border-blue-600" : "border-gray-300"
              } transition`}
            >
              <img
                src={Female}
                alt="Female"
                className="w-12 h-12 object-cover rounded-full"
              />
              <span className="text-sm">Female</span>
            </div>
          </div>
        </div>
  
        {/* Generate Button */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">

  {annotaions?.map((x, i) => (
    <div
      key={i}
      className="transition-all duration-300 ease-in-out hover:col-span-2 hover:row-span-2 hover:z-10"
    >
      <div className="bg-white border border-emerald-300 shadow-md rounded-xl p-4 h-full w-full hover:scale-[1.03] transition-all duration-300 ease-in-out">
        {/* Title */}
        <div className="text-sm font-semibold text-emerald-700 truncate hover:whitespace-normal">
          {x.title || "Untitled"}
        </div>

        {/* URL */}
        <a
          href={x.url.replace("?utm_source=openai","")}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 text-xs truncate block hover:whitespace-normal hover:overflow-visible hover:underline mt-1"
        >
          {x.url.replace("?utm_source=openai","")}
        </a>

        {/* Index */}
        <div className="text-xs text-gray-500 mt-1">
          Index: <span className="font-medium">{x.start_index}-{x.end_index}</span>
        </div>
      </div>
    </div>
  ))}
</div>
        <div className="flex flex-col w-full sm:w-1/3 mt-4 sm:mt-6">
        
        {!loading && (
          <button
            onClick={summarizeContent}
            className="bg-emerald-500 hover:bg-emerald-600 text-white font-bold py-2 rounded-lg transition w-full"
          >
            Generate
          </button>
          
        )}
    


          {loading &&<div className="flex justify-center items-center bg-white shadow-md rounded-full p-4 text-purple-600 font-semibold text-lg">
        <svg
          className="animate-spin h-5 w-5 mr-2 text-purple-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8v4l3-3-3-3v4a8 8 0 00-8 8h4z"
          ></path>
        </svg>
        Loading...
      </div>}
        </div>
  


      </div>
    </div>
  </div>
)}

        {chunks.length > 0 && (
          <>
            {/* <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                margin: "1rem 0",
              }}
            >
              <div style={{ display: "flex", gap: "1rem" }}>
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{ padding: "6px", borderRadius: "6px" }}
                />
                <button
                  onClick={downloadSummary}
                  style={{
                    padding: "6px 12px",
                    backgroundColor: "#4b5563",
                    color: "#fff",
                    borderRadius: "4px",
                  }}
                >
                  Download
                </button>
                <button
                  onClick={() => {
                    const doubts = "Doubt in chunk " + (selectedChunkIndex + 1);
                    if (doubts) {
                      setDoubtperchase((prev) => ({
                        ...prev,
                        [selectedChunkIndex]: [
                          {
                            title: doubts
                              .split(",")
                              .map((doubt) => doubt.trim()),
                            doubt: "",
                            solution: "",
                          },
                        ],
                      }));
                    }
                  }}
                  style={{
                    padding: "6px 12px",
                    backgroundColor: "#4b5563",
                    color: "#fff",
                    borderRadius: "4px",
                  }}
                >
                  Ask Doubt
                </button>
              </div>
              <label>
                <input
                  type="checkbox"
                  checked={autoplay}
                  onChange={() => setAutoplay(!autoplay)}
                />{" "}
                Autoplay
                <input
                  type="checkbox"
                  checked={loop}
                  onChange={() => setLoop(!loop)}
                  style={{ marginLeft: "1rem" }}
                />{" "}
                Loop
              </label>
            </div> */}
   <div style={{ display: "flex", gap: "2rem" }}>
              <div style={{ flex: 1 }}>
                {doubtchunk != null && doubtloaded && (
                  <div
                    style={{
                      border: "1px solid #ccc",
                      padding: "1rem",
                      borderRadius: "8px",
                      backgroundColor: darkMode ? "#374151" : "#f9fafb",
                    }}
                  >
                    
                    {doubtimagecache[doubtchunk]?.length > 0 && (
                      <div
                        style={{
                          position: "relative",
                          overflow: "hidden",
                          height: "600px",
                          marginTop: "1rem",
                          borderRadius: "8px",
                        }}
                        id="container"
                      >
                        <h3 style={{ position: "absolute", top: 2 }}>
                          📌 Chunk {doubtchunk + 1}
                        </h3>
                        {doubtimagecache[doubtchunk].map((img, index) => (
                          <>
                          <img
                            key={index}
                            src={img}
                            alt={`Scene ${index}`}
                            onMouseEnter={() => {
                              setShowControls(true);
                              setTimeout(() => { setShowControls(false) }, 5000)
                            }}

                            onClick={() => {
                              setshowIcon(true);
                              setTimeout(() => {setshowIcon(false) }, 1000)
                              if (
                                !AudioRef.current.paused &&
                                !AudioRef.current.ended &&
                                AudioRef.current.currentTime > 0
                              ) {
                                AudioRef.current.pause();
                                fakepost()
                              } else {
                                AudioRef.current.play();
                              }
                              if (
                                !audioRef.current.paused &&
                                !audioRef.current.ended &&
                                audioRef.current.currentTime > 0
                              ) {
                                audioRef.current.pause();
                              } else {
                                audioRef.current.play();
                              }
                            }}
                            style={{
                              position: "absolute",
                              top: 0,
                              left: 0,
                              width: "100%",
                              objectFit: "cover",
                              opacity: currentDoubtImageIndex === index ? 1 : 0,
                              transition: "opacity 1s ease-in-out",
                            }}
                          />
                            {(showControls ||AudioRef.current?.paused)  && (
                          <div
                            style={{
                              position: "absolute",
                              bottom: 0,
                              left: 0,
                              width: "100%",
                              background: "linear-gradient(to top, rgba(0,0,0,0.8), transparent)",
                              padding: "10px 16px",
                              zIndex: 10,
                              transition: "opacity 0.3s ease",
                              display: "flex",
                              flexDirection: "column",
                              gap: "8px",
                            }}
                          >
                            {/* Seek Bar */}
                            <input
                              type="range"
                              min="0"
                              max={AudioRef.current?.duration || 0}
                              value={AudioRef.current?.currentTime || 0}
                              onChange={(e) => (AudioRef.current.currentTime = e.target.value)}
                              style={{
                                appearance: "none",
                                width: "100%",
                                height: "4px",
                                borderRadius: "2px",
                                background: "#ccc",
                                backgroundImage: `linear-gradient(to right, red ${(AudioRef.current?.currentTime / AudioRef.current?.duration) * 100 || 0
                                  }%, #ccc 0%)`,
                                outline: "none",
                                cursor: "pointer",
                              }}
                            />

                            {/* Buttons */}
                            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                              {/* Left: Transport + Volume */}
                              <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
                                <button onClick={() => AudioRef.current.currentTime -= 10} style={iconBtn}><img src={prevImage} /></button>
                                <button
                                   onClick={() => {
                                    if (AudioRef.current.paused) {
                                      AudioRef.current.play();
                                      audioRef.current.play();
                                    } else {
                                      AudioRef.current.pause();
                                      audioRef.current.pause();
                                    }
                                  }}
                                  style={iconBtn}
                                >
                                  {AudioRef.current?.paused ? <img src={playImage} /> : <img src={pauseImage} style={{
                                    width: 30, height: 30, filter: "brightness(0) invert(1)",
                                  }} />}
                                </button>
                                <input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.01"
                                  value={AudioRef.current?.volume || 1}
                                  onChange={(e) => (AudioRef.current.volume = e.target.value)}
                                  style={{
                                    accentColor: "red",
                                    width: "100px",
                                    cursor: "pointer",
                                  }}
                                />
                              </div>

                              {/* Center: Time */}
                              <div style={{ fontSize: "14px", color: "#fff" }}>
                                <span style={{ color: "red" }}>●</span> LIVE · {formatTime(AudioRef.current?.currentTime)}
                              </div>

                              {/* Right: Captions & Fullscreen */}
                              <div style={{ display: "flex", gap: "12px" }}>
                                <button onClick={toggleCaptions} style={iconBtn}><img src={capImage} /></button>
                                <button
                                  onClick={() => {
                                    const container =
                                      document.getElementById("container");
                                    if (!document.fullscreenElement) {
                                      container.requestFullscreen();
                                    } else {
                                      document.exitFullscreen();
                                    }
                                  }}
                                  style={iconBtn}
                                >
                                  ⌞ ⌝
                                </button>
                              </div>
                            </div>
                          </div>
                        )}

                          </>
                        ))}


                        {/* <button
                          onClick={() => {
                            const container =
                              document.getElementById("container");
                            if (!document.fullscreenElement) {
                              container.requestFullscreen();
                            } else {
                              document.exitFullscreen();
                            }
                          }}
                          style={{
                            position: "absolute",
                            top: "10px",
                            right: "10px",
                            padding: "6px 12px",
                            backgroundColor: "#4b5563",
                            color: "#fff",
                            borderRadius: "4px",
                            zIndex: 3,
                          }}
                        >
                          Full Screen
                        </button> */}
                             {!showSidebar && (
                          <div
                            style={{
                              position: "absolute",
                              top: 2,
                              left: 5,
                              zIndex: 5,
                            }}
                          >
                            <button
                              onClick={() => setShowSidebar(!showSidebar)}
                              style={{
                                padding: "6px 12px",
                                border: "1px solid #ccc",
                                borderRadius: "4px",
                              }}
                            >
                              <img src={Sider} />
                            </button>
                          </div>
                        )}

                        {showSidebar && (
                          <div
                          style={{
                            width: "250px",
                            zIndex: 2,
                            position: "absolute",
                            top: "50%",
                            left: 5,
                            transform: "translateY(-50%)",
                            backgroundColor: darkMode ? "#1f2937" : "#ffffff",
                            padding: "1rem",
                            borderRadius: "8px",
                            boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                            maxHeight: "80vh", // set max height relative to viewport
                            overflowY: "auto",  // enables scrolling if content exceeds height
                          }}
                        >
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginBottom: "1rem",
                                overflowY:"auto"
                              }}
                            >
                              <h2 style={{ fontWeight: "bold", fontSize: "2rem", margin: 0 }}>Topics</h2>
                              <button
                                onClick={() => setShowSidebar(false)}
                                style={{
                                  padding: "4px 10px",
                                  border: "1px solid #ccc",
                                  borderRadius: "4px",
                                  cursor: "pointer",
                                  fontWeight: "bold",
                                }}
                              >
                                X
                              </button>
                            </div>

                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                gap: "0.5rem",
                              }}
                            >
                              {chunks.map((_, index) => (
                                <div
                                  key={index}
                                  style={{ marginBottom: "1rem" }}
                                >
                                  <button
                                    onClick={() => {
                                      if(!AudioRef.current?.paused){AudioRef.current.pause();audioRef.current.pause()}
                                      if(Imageslist[index]){
                                        setDoubtchunk(null);
                                        setDoubtloaded(false);
                                        setDoubtImageCache({});
                                        setCurrentDoubtImageIndex(0);
                                        setDoubtperchase({});
                                        setCaller(true)
                                      setSelectedChunkIndex(index);
                                       
                                      }
                                    }}
                                    style={{
                                      textAlign: "left",
                                      padding: "10px",
                                      borderRadius: "4px",
                                      border: "1px solid #ccc",
                                      height: "auto",
                                      fontWeight:
                                        selectedChunkIndex === index
                                          ? "bold"
                                          : "normal",
                                      cursor: "pointer",
                                      width: "100%", // Optional for full-width buttons
                                      backgroundImage: `url(${titleimages[index]})`,
                                      color: "white",
                                      backgroundSize: "cover",
                                      backgroundPosition: "center"
                                      
                                    }}
                                  >
                                    {chunktitles[index]?.replaceAll('"', "")}
                                  </button>

                                  {doubtperchase[index] && (
                                    <div
                                      style={{
                                        marginTop: "0.5rem",
                                        padding: "0.5rem",
                                        backgroundColor: "#f9fafb",
                                        borderRadius: "4px",
                                      }}
                                    >
                                      <strong>Doubts:</strong>
                                      {doubtperchase[index].map(
                                        (doubt, idx) => (
                                          <button
                                            onClick={() => {
                                              setDoubtchunk(index);
                                              setSelectedChunkIndex(null);
                                            }}
                                            key={idx}
                                            style={{ margin: "0.25rem 0" }}
                                          >
                                            {doubt.title}
                                          </button>
                                        )
                                      )}
                                    </div>
                                  )}
                                </div>
                                
                              ))}
                               
                            </div>
                          </div>
                        )}
                 
                      </div>
                    )}
                  </div>
                )}
                <div>
{selectedChunkIndex !== null && doubtchunk == null  && !visible  && (

                      <div
                        style={{
                          position: "relative",
                          overflow: "hidden",
                          height: "600px",
                          
                          marginTop: "1rem",
                          borderRadius: "8px",
                          zIndex: 3,
                        }}
                        >
                          <div className="text-center text-sm text-red-600 font-medium p-4">
    🔁 Please rotate your device to <span className="font-semibold">landscape mode</span> for a better preview experience.
  </div>
                          </div>
                    )}
                {selectedChunkIndex !== null && !doubtloaded ? (
                  <>
                  {visible && (
                  <div
                  id="container"
                  style={{
                    height: "600px",
                    width: "100%",
                    position: "relative", // key for inner absolute children
                    display: "flex",       // ensure it stretches
                    flexDirection: "column",
                  boxShadow:isDoubtasked?"0 4px 12px rgba(0,0,0,0.1)":"none",

                  }}>
                   {selectedChunkIndex<chunks.length ? (
                    <>
                    {disclaimer && (
                      <DisclaimerPage />
                    )}

                    {(!chunkImageCache[selectedChunkIndex] ||
                      chunkImageCache[selectedChunkIndex].length === 0 || showtitle) && !disclaimer  && (
                        <div
                          style={{
                            position: "relative",
                            overflow: "hidden",
                            height: "100vh",
                            marginTop: "1rem",
                            borderRadius: "8px",
                            zIndex: 3,
                          }}
                        >
     

                          <img
                            src={titleimages[selectedChunkIndex]}
                            style={{
                              position: "absolute",
                              top: 0,
                              left: 0,
                              width: "100%",
                              height: "100%",
                              objectFit: "cover",
                              opacity: 1,
                              transition: "opacity 1s ease-in-out",
                            }}
                            autoPlay
                            loop
                            muted
                            playsInline
                          />

                          <div
                            style={{
                              position: "absolute",
                              top: "50%",
                              left: "50%",
                              transform: "translate(-50%, -50%)",
                              color: "white",
                              fontSize: "2rem",
                              fontWeight: "bold",
                              textAlign: "center",
                              textShadow: "2px 2px 10px rgba(0,0,0,0.7)",
                              fontFamily: "Google Sans, Roboto, sans-serif",
                              zIndex: 4,
                            }}
                          >
                            Topic {selectedChunkIndex + 1}:<br />
                            <span
                              style={{ fontSize: "1.6rem", fontWeight: "500" }}
                            >
                              <TypingEffect
                                content={chunktitles[selectedChunkIndex]}
                                duration={2}
                              />
                            </span>
                          </div>
                          <div
                            style={{
                              position: "absolute",
                              bottom: "16px",
                              right: "16px",
                              width: "40px",
                              height: "40px",
                              border: "4px solid rgba(255, 255, 255, 0.2)",
                              borderTop: "4px solid white",
                              borderRadius: "50%",
                              animation: "spin 1s linear infinite",
                              zIndex: 5,
                            }}
                          ></div>
                          <style>
                            {`
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
`}
                          </style>
                        </div>
                        
                      )}
                       
                    {chunkImageCache[selectedChunkIndex]?.length > 0 && !disclaimer && !showtitle && (

                      <div
                        style={{
                          position: "relative",
                          overflow: "hidden",
                          height: "100vh",
                          marginTop: "1rem",
                          borderRadius: "8px",
                          zIndex: 3,
                        }}
                      >
                        <div
                          style={{
                            position: "absolute",
                            top: "50%",
                            left: "50%",
                            color: "white",
                            fontSize: "2rem",
                            fontWeight: "bold",
                            textAlign: "center",
                            textShadow: "2px 2px 10px rgba(0,0,0,0.7)",
                            fontFamily: "Google Sans, Roboto, sans-serif",
                            zIndex: 10,
                            pointerEvents: "none", // Prevents blocking clicks
                            transition: "opacity 0.3s ease, transform 0.3s ease",
                            opacity: showIcon ? 1 : 0,
                            transform: showIcon
                              ? "translate(-50%, -50%) scale(1)"
                              : "translate(-50%, -50%) scale(0.8)",
                          }}
                        >
                          {AudioRef.current?.paused ? <img src={solar_play} alt="Play" style={{ width: 50, height: 50 }} /> : <img src={pauseImage} alt="Pause" style={{ width: 50, height: 50, filter: "brightness(0) invert(1)", }} />}

                        </div>
                        <h3
                          style={{
                            position: "absolute",
                            top: 10,
                            left: "50%",
                            transform: "translateX(-50%)",
                            zIndex: 5,
                            backgroundColor: "rgba(255, 255, 255, 0.85)",
                            padding: "6px 16px",
                            borderRadius: "12px",
                            fontSize: "20px",
                            fontWeight: "600",
                            color: "#202124",
                            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                            fontFamily: "Google Sans, Roboto, sans-serif",
                            whiteSpace: "nowrap",
                            maxWidth: "90%",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                        >
                          {(showControls || AudioRef.current?.paused) && 
  chunktitle?.replaceAll('"', "")
}

                        </h3>
                        {!showSidebar && (
                          <div
                            style={{
                              position: "absolute",
                              top: 2,
                              left: 5,
                              zIndex: 5,
                            }}
                          >
                            <button
                              onClick={() => setShowSidebar(!showSidebar)}
                              style={{
                                padding: "6px 12px",
                                border: "1px solid #ccc",
                                borderRadius: "4px",
                              }}
                            >
                              <img src={Sider} />
                            </button>
                          </div>
                        )}
                        <div>
                          {displayedSentences.map((sentence, idx) => (
                            <p
                              key={idx}
                              style={{
                                position: "absolute",
                                bottom: showControls ? 50 : 5,
                                width: "100%",
                                zIndex: 5,
                                boxShadow: "0 4px 12px rgba(0, 0, 0, 0.25)",
                                padding: "8px 12px",
                              }}
                            >
                              {captionsOn && (
                                <div style={{
                                  backgroundColor: "black",
                                  opacity:0.7,
                                  color: "white",
                                  padding: "0.2rem 0.4rem",
                                  borderRadius: "4px",
                                  display: "inline-block",
                                }}>
                                  <TypingEffect content={sentence} duration={5} />
                                </div>
                              )}
                            </p>

                          ))}
                        </div>
                        {nextepisode && (
                          <div
                            style={{
                              position: "absolute",
                              bottom: 4,
                              right: 5,
                              zIndex: 5,
                              padding: "12px 16px",
                              borderRadius: "12px",
                              color: "white",
                              cursor: "pointer",
                              boxShadow: "0 4px 12px rgba(0, 0, 0, 0.25)",
                              fontFamily: "Google Sans, Roboto, sans-serif",
                              fontSize: "16px",
                              fontWeight: "600",
                              display: "flex",
                              alignItems: "center",
                              gap: "12px",
                              transition: "all 0.3s ease-in-out",
                              backgroundImage: `url(${titleimages[selectedChunkIndex + 1]
                                })`,
                              backgroundSize: "cover",
                              backgroundPosition: "center",
                              backdropFilter: "blur(4px)",
                              border: "1px solid rgba(255,255,255,0.1)",
                            }}
                            onMouseOver={(e) => {
                              e.currentTarget.style.transform = "scale(1.03)";
                              e.currentTarget.style.boxShadow =
                                "0 6px 14px rgba(0, 0, 0, 0.4)";
                            }}
                            onMouseOut={(e) => {
                              e.currentTarget.style.transform = "scale(1)";
                              e.currentTarget.style.boxShadow =
                                "0 4px 12px rgba(0, 0, 0, 0.25)";
                            }}
                            onClick={() => {
                              setAutoplay(false);
                            }}
                          >
                            <div
                              style={{
                                backgroundColor: "rgba(0,0,0,0.5)",
                                padding: "8px 12px",
                                borderRadius: "10px",
                              }}
                            >
                              <div>{chunktitles[selectedChunkIndex + 1]}</div>
                              <div
                                style={{
                                  fontSize: "13px",
                                  color: "#e3e3e3",
                                  marginTop: "4px",
                                }}
                              >
                                Auto-playing in{" "}
                                <span id="timer" style={{ fontWeight: "bold" }}>
                                  5
                                </span>{" "}
                                seconds...
                              </div>
                            </div>
                          </div>
                        )}
                        {chunkImageCache[selectedChunkIndex].map(
                          (img, index) => (
                            <img
                              key={index}
                              src={img}
                              onMouseEnter={() => {
                                setShowControls(true);
                                setTimeout(() => { setShowControls(false) }, 5000)
                              }}

                              onClick={() => {
                                setshowIcon(true);
                                setTimeout(() => {setshowIcon(false) }, 1000)
                                if (
                                  !AudioRef.current.paused &&
                                  !AudioRef.current.ended &&
                                  AudioRef.current.currentTime > 0
                                ) {
                                  AudioRef.current.pause();
                                  fakepost()
                                } else {
                                  AudioRef.current.play();
                                }
                                if (
                                  !audioRef.current.paused &&
                                  !audioRef.current.ended &&
                                  audioRef.current.currentTime > 0
                                ) {
                                  audioRef.current.pause();
                                } else {
                                  audioRef.current.play();
                                }
                              }}
                              alt={`Scene ${index}`}
                              style={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                boxShadow:recording?"0 4px 12px rgba(0,0,0,0.1)":"none",
                                objectFit: "contain",
                                opacity: currentImageIndex === index ? 1 : 0,
                                transition: "opacity 1s ease-in-out",
                              }}
                            />

                          )
                        )}
                        {(showControls||AudioRef?.current?.paused) &&(<>
                        <img src="/doubt.png" style={{
  position: "absolute",
  top: 80,
  right:5,
  zIndex: 5,
  width: 80,
  height: 60,
  cursor: "pointer",
  filter: "brightness(0) invert(1)",
  transition: "transform 0.3s ease",
}} onClick={()=>{audioRef.current.pause();AudioRef.current.pause();setrecording(true);fakepost()}}></img>
                        <img src="/notes.png" style={{
                          position: "absolute",
                          top: 5,
                          right:15,
                          zIndex: 5,
                          width: 55,
                          height: 65,
                          cursor: "pointer",
                          filter: "brightness(0) invert(1)",
                          transition: "transform 0.3s ease",
                          
                        }} onClick={()=>{nav("/notes",{state:{title:chunktitle,data:[chunktitles.map((x,index)=>{
                          return {
                            "title":x,
                            "original":chunks[index],
                            "titleimage":titleimages[index],
                            "chunkimage":chunkImageCache[index]
                          }
                        })]}})}}></img></> )}

                        {(showControls ||AudioRef.current?.paused)  && (
                          <div
                            style={{
                              position: "absolute",
                              bottom: 0,
                              left: 0,
                              width: "100%",
                              background: "linear-gradient(to top, rgba(0,0,0,0.8), transparent)",
                              padding: "10px 16px",
                              zIndex: 10,
                              transition: "opacity 0.3s ease",
                              display: "flex",
                              flexDirection: "column",
                              gap: "8px",
                            }}
                          >
                            {/* Seek Bar */}
                            <input
                              type="range"
                              min="0"
                              max={AudioRef.current?.duration || 0}
                              value={AudioRef.current?.currentTime || 0}
                              onChange={(e) => (AudioRef.current.currentTime = e.target.value)}
                              style={{
                                appearance: "none",
                                width: "100%",
                                height: "4px",
                                borderRadius: "2px",
                                background: "#ccc",
                                backgroundImage: `linear-gradient(to right, red ${(AudioRef.current?.currentTime / AudioRef.current?.duration) * 100 || 0
                                  }%, #ccc 0%)`,
                                outline: "none",
                                cursor: "pointer",
                              }}
                            />

                            {/* Buttons */}
                            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                              {/* Left: Transport + Volume */}
                              <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
                                <button onClick={() => AudioRef.current.currentTime -= 10} style={iconBtn}><img src={prevImage} /></button>
                                <button
                                   onClick={() => {
                                    if (AudioRef.current.paused) {
                                      AudioRef.current.play();
                                      audioRef.current.play();
                                    } else {
                                      AudioRef.current.pause();
                                      audioRef.current.pause();
                                    }
                                  }}
                                  style={iconBtn}
                                >
                                  {AudioRef.current?.paused ? <img src={playImage} /> : <img src={pauseImage} style={{
                                    width: 30, height: 30, filter: "brightness(0) invert(1)",
                                  }} />}
                                </button>
                                <input
                                  type="range"
                                  min="0"
                                  max="1"
                                  step="0.01"
                                  value={AudioRef.current?.volume || 1}
                                  onChange={(e) => (AudioRef.current.volume = e.target.value)}
                                  style={{
                                    accentColor: "red",
                                    width: "100px",
                                    cursor: "pointer",
                                  }}
                                />
                              </div>

                              {/* Center: Time */}
                              <div style={{ fontSize: "14px", color: "#fff" }}>
                                <span style={{ color: "red" }}>●</span> LIVE · {formatTime(AudioRef.current?.currentTime)}
                              </div>

                              {/* Right: Captions & Fullscreen */}
                              <div style={{ display: "flex", gap: "12px" }}>
                                <button onClick={toggleCaptions} style={iconBtn}><img src={capImage} /></button>
                                <button
                                  onClick={() => {
                                    const container =
                                      document.getElementById("container");
                                    if (!document.fullscreenElement) {
                                      container.requestFullscreen();
                                    } else {
                                      document.exitFullscreen();
                                    }
                                  }}
                                  style={iconBtn}
                                >
                                  ⌞ ⌝
                                </button>
                              </div>
                            </div>
                          </div>
                        )}

                        {recording && (
                          <div style={{ position: "absolute", top: "50%", left: "50%",       transform: "translate(-50%, -50%)",
                            width: "230px",height:isdoubtsubmitted?"60px":"230px"   , borderRadius: "16px",  // ✅ Add this line
                            backgroundColor: "white", zIndex: 20 }}>
                                                            {!isdoubtsubmitted?(
                                                              <>
                              <div style={{position:"absolute",top:5,right:2}}><X onClick={()=>{setrecording(false)}}/></div>
                            <div
  style={{
    position: "absolute",
    left: "50%",
    top: "10%",
    width: "80px",
    height: "80px",
    borderRadius: "50%",
    backgroundColor: "rgba(26, 115, 232, 0.2)",
    animation: "pulse 1.5s infinite",
    zIndex: 0,
  }}
>
  <img
    src="https://static.vecteezy.com/system/resources/thumbnails/018/754/486/small/microphone-icon-in-black-colors-podcast-signs-illustration-png.png"
    style={{
      position: "absolute",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
      height: "50px",
      width: "50px",
    }}
    alt="microphone"
  />
</div>

                            {/* Input & Submit UI */}

                            <div
                              style={{
                                position: "absolute",
                                top:"50%",
                                left: "50%",
                                transform: "translateX(-50%)",
                                gap: 10,
                                zIndex: 1,
                              }}
                            >
                              <input
                                type="text"
                                value={doubtquery}
                                placeholder="Listening..."
                                onChange={(e) => setdoubtquery(e.target.value)}
                                style={{
                                  padding: "12px 16px",
                                  borderRadius: "25px",
                                  border: "1px solid #ccc",
                                  outline: "none",
                                  fontSize: "16px",
                                  width: "200px",
                                  boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                                }}
                              />
                              <button
                                onClick={() => {
                                  setlastaudiosrc(AudioRef.current.src);
                                  setlastaudiotime(AudioRef.current.currentTime);
                                  if (AudioRef.current) {
                                    if (AudioRef.current.isPlaying) {
                                      AudioRef.current.pause();
                                      audioRef.current.pause();
                                    }
                                  }
                                  const openai = new OpenAI({
                                    apiKey:
                                      "********************************************************************************************************************************************************************",
                                    dangerouslyAllowBrowser: true,
                                  });
                                  const doubts = doubtquery;
                                  openai.chat.completions
                                    .create({
                                      model: "gpt-3.5-turbo",
                                      messages: [
                                        {
                                          role: "system",
                                          content: `You are a helpful assistant. Answer the user's question based on the context provided. Give the solution in ${voiceURI}`,
                                        },
                                        { role: "user", content: doubtquery },
                                      ],
                                      max_tokens: 500,
                                    })
                                    .then((res) => {
                                    let solute=res.choices[0].message.content.trim();
                                     if (document.fullscreenElement === container) {
                                      setIsFullscreen(true)
                                     }
                                      setDoubtperchase((prev) => ({
                                        ...prev,
                                        [selectedChunkIndex]: [
                                          {
                                            title: doubts
                                              .split(",")
                                              .map((d) => d.trim()),
                                            doubt: doubtquery,
                                            solution: solute,
                                          },
                                        ],
                                      }));
                                      setSolution(solute);
                                      setDoubtchunk(selectedChunkIndex);
                                      console.log("solution", solution);
                                      console.log(doubtchunk);
                                      setIsDoubtsubmitted(true)
                                      setTimeout(() => {
                                        console.log(doubtperchase[doubtchunk]);
                                      }, 5000);
                                    });
                                }}
                                style={{
                                  padding: "12px 20px",
                                  borderRadius: "25px",
                                  backgroundColor: "#1A73E8",
                                  color: "#fff",
                                  fontWeight: "bold",
                                  border: "none",
                                  left:"50%",
                                  transform: "translateX(50%)",
marginTop: 10,
                                  cursor: "pointer",
                                  boxShadow:
                                    "0 4px 14px rgba(26, 115, 232, 0.3)",
                                }}
                              >
                                Submit
                              </button>
                              
                            </div>
                            </>
                            ):(
                                                              <div className="flex justify-center items-center bg-white shadow-md rounded-full p-4 text-purple-600 font-semibold text-lg">
        <svg
          className="animate-spin h-5 w-5 mr-2 text-purple-600"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8v4l3-3-3-3v4a8 8 0 00-8 8h4z"
          ></path>
        </svg>
        Loading...
      </div>
                                                            )}
                                                            
                          </div>
                        )}

                        {/* Animation keyframes */}
                        <style>
                          {`
    @keyframes pulse {
      0% {
        transform: translateX(-50%) scale(1);
        opacity: 0.7;
      }
      50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 1;
      }
      100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.7;
      }
    }
  `}
                        </style>


                        {showSidebar && (
                          <div
                          style={{
                            width: "250px",
                            zIndex: 2,
                            position: "absolute",
                            top: "50%",
                            left: 5,
                            transform: "translateY(-50%)",
                            backgroundColor: darkMode ? "#1f2937" : "#ffffff",
                            padding: "1rem",
                            borderRadius: "8px",
                            boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                            maxHeight: "80vh", // set max height relative to viewport
                            overflowY: "auto",  // enables scrolling if content exceeds height
                          }}
                        >
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginBottom: "1rem",
                                overflowY:"auto"
                              }}
                            >
                              <h2 style={{ fontWeight: "bold", fontSize: "2rem", margin: 0 }}>Topics</h2>
                              <button
                                onClick={() => setShowSidebar(false)}
                                style={{
                                  padding: "4px 10px",
                                  border: "1px solid #ccc",
                                  borderRadius: "4px",
                                  cursor: "pointer",
                                  fontWeight: "bold",
                                }}
                              >
                                X
                              </button>
                            </div>

                            <div
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                gap: "0.5rem",
                              }}
                            >

                              {chunks.map((_, index) => (
                                <div
                                  key={index}
                                  style={{ marginBottom: "1rem" }}
                                >
                                  <button
                                    onClick={() => {
                                      if(!AudioRef.current?.paused){AudioRef.current.pause();audioRef.current.pause()}
                                      if(Imageslist[index]){
                                      setSelectedChunkIndex(index);
                                      setshowtitle(true);
                                      setDoubtchunk(null);
                                      }
                                    }}
                                    style={{
                                      textAlign: "left",
                                      padding: "10px",
                                      borderRadius: "4px",
                                      border: "1px solid #ccc",
                                      height: "auto",
                                      fontWeight:
                                        selectedChunkIndex === index
                                          ? "bold"
                                          : "normal",
                                      cursor: "pointer",
                                      width: "100%", // Optional for full-width buttons
                                      backgroundImage: `url(${titleimages[index]})`,
                                      color: "white",
                                      backgroundSize: "cover",
                                      backgroundPosition: "center",
                                        boxShadow: Imageslist[index] ? "none" : "0 4px 12px rgba(0, 0, 0, 0.5)",
                                        pointerEvents: Imageslist[index] ? "auto" : "none",
                                    }}
                                  >
                                    {chunktitles[index]?.replaceAll('"', "")}
                                  </button>

                                  {doubtperchase[index] && (
                                    <div
                                      style={{
                                        marginTop: "0.5rem",
                                        padding: "0.5rem",
                                        backgroundColor: "#f9fafb",
                                        borderRadius: "4px",
                                      }}
                                    >
                                      <strong>Doubts:</strong>
                                      {doubtperchase[index].map(
                                        (doubt, idx) => (
                                          <button
                                            onClick={() => {
                                              setDoubtchunk(index);
                                              setSelectedChunkIndex(null);
                                            }}
                                            key={idx}
                                            style={{ margin: "0.25rem 0" }}
                                          >
                                            {doubt.title}
                                          </button>
                                        )
                                      )}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    </>
                  ) : (<>
                  <div className="bg-black text-white min-h-screen flex items-center justify-center">
      <div className="max-w-md w-full p-6 rounded-xl bg-gray-900 shadow-xl">
        {submitted ? (
          
          <div className="text-center space-y-6">
          <div className="text-green-400 text-xl font-semibold">
            Feedback submitted successfully 
          </div>
          <button
            onClick={()=>{setSelectedChunkIndex(0)}}
            className="px-6 py-2 bg-white text-black font-semibold rounded-full hover:bg-gray-200 transition"
          >
            Watch Again
          </button>
        </div>
        ) : (
          <>
            <h2 className="text-2xl font-bold mb-4 text-center">Leave Feedback</h2>

            {/* Star Rating */}
            <div className="flex justify-center mb-4">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`w-8 h-8 cursor-pointer transition ${
                    (hovered || rating) >= star ? "text-yellow-400" : "text-gray-600"
                  }`}
                  onMouseEnter={() => setHovered(star)}
                  onMouseLeave={() => setHovered(0)}
                  onClick={() => setRating(star)}
                  fill={(hovered || rating) >= star ? "currentColor" : "none"}
                />
              ))}
            </div>

            {/* Comment Box */}
            <textarea
              className="w-full p-3 rounded-lg bg-gray-800 text-white resize-none focus:outline-none focus:ring-2 focus:ring-yellow-400 mb-4"
              rows="4"
              placeholder="Write your comments..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            ></textarea>

            {/* Submit Button */}
            <button
              onClick={handleSubmit}
              disabled={rating === 0}
              className={`w-full py-2 rounded-lg font-semibold transition ${
                rating === 0
                  ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                  : "bg-yellow-400 text-black hover:bg-yellow-300"
              }`}
            >
              Submit Feedback
            </button>
          </>
        )}
      </div>
    </div>
              
              </>)}
                  </div>)}</>
                ) : (
                  doubtchunk == null &&          <>
                  {visible &&(
                  <LoadingPage speed={1000} content1={"Summarizing"} content2={"Generating"}/>)}
                  {!visible && (
                    <div className="text-center text-sm text-red-600 font-medium p-4">
                    🔁 Please rotate your device to <span className="font-semibold">landscape mode</span> for a better preview experience.
                  </div>
                  )}
                  </>

              
                )}        </div>


                {/* <button
                  onClick={handleNextChunk}
                  style={{
                    padding: "10px 20px",
                    backgroundColor: "#7c3aed",
                    color: "#fff",
                    borderRadius: "6px",
                    marginTop: "2rem",
                  }}
                >
                  {chunks.length > 1 ? "Next Chunk" : "Restart"}
                </button> */}
              </div>
            </div>
          </>
        )}

        {error && <p style={{ color: "red", marginTop: "1rem" }}>❌ {error}</p>}
      </div>
    </div>
  );
};

export default UploadPdfComponent;