import React, { useEffect, useState } from "react";

const LoadingPage = ({ speed, content1, content2 }) => {
  const [summarizingProgress, setSummarizingProgress] = useState(0);
  const [creatingProgress, setCreatingProgress] = useState(0);

  useEffect(() => {
    let interval;

    if (summarizingProgress < 100) {
      interval = setInterval(() => {
        setSummarizingProgress((prev) => Math.min(prev + 1, 100));
      }, speed);
    } else if (creatingProgress < 100) {
      interval = setInterval(() => {
        setCreatingProgress((prev) => Math.min(prev + 1, 100));
      }, speed);
    }

    return () => clearInterval(interval);
  }, [summarizingProgress, creatingProgress]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white mt-[-20px]">
      <div className="flex items-center space-x-2 mb-8">
        <div className="w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-lg font-medium text-gray-700">Loading, please wait...</span>
      </div>

      <div className="w-full max-w-3xl px-4 flex space-x-6">
        {/* Summarizing */}
        <div className="flex-1">
          <div className="flex justify-between mb-2 font-semibold text-gray-800">
            <span>{content1}</span>
            <span>{summarizingProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 h-4 rounded-full overflow-hidden">
            <div
              className="h-4 rounded-full transition-all duration-300"
              style={{
                width: `${summarizingProgress}%`,
                background: "linear-gradient(90deg, #ff7eb3, #ff758c, #ff6a5e)",
              }}
            ></div>
          </div>
        </div>

        {/* Creating */}
        <div className="flex-1">
          <div className="flex justify-between mb-2 font-semibold text-gray-800">
            <span>{content2}</span>
            <span>{creatingProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 h-4 rounded-full overflow-hidden">
            <div
              className="h-4 rounded-full transition-all duration-300"
              style={{
                width: `${creatingProgress}%`,
                background: "linear-gradient(90deg, #6a11cb, #2575fc)",
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingPage;
