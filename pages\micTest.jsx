// App.jsx
import React from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

function App() {
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  if (!browserSupportsSpeechRecognition) {
    return (
      <div style={{ padding: 20, fontFamily: 'Arial' }}>
        <h2>Speech Recognition Not Supported</h2>
        <p>Your browser does not support speech recognition. Please try using Chrome or Edge.</p>
      </div>
    );
  }

  return (
    <div style={{
      fontFamily: 'Arial',
      padding: 20,
      maxWidth: 600,
      margin: 'auto',
      textAlign: 'center'
    }}>
      <h2>🎤 Speech Recognition Demo</h2>

      <div style={{
        border: '2px solid #ccc',
        padding: '10px',
        borderRadius: 8,
        marginBottom: 20,
        backgroundColor: listening ? '#f0f8ff' : '#fff'
      }}>
        <p><strong>Status:</strong> {listening ? 'Listening...' : 'Stopped'}</p>
        <p><strong>Transcript:</strong></p>
        <div style={{
          background: '#f9f9f9',
          padding: 10,
          borderRadius: 4,
          minHeight: 100,
          whiteSpace: 'pre-wrap',
          border: '1px solid #ddd'
        }}>
          {transcript || 'Start speaking...'}
        </div>
      </div>

      <button
        onClick={() => SpeechRecognition.startListening({ continuous: true })}
        style={buttonStyle('green')}
      >
        Start
      </button>
      <button
        onClick={SpeechRecognition.stopListening}
        style={buttonStyle('red')}
      >
        Stop
      </button>
      <button
        onClick={resetTranscript}
        style={buttonStyle('gray')}
      >
        Reset
      </button>
    </div>
  );
}

const buttonStyle = (color) => ({
  backgroundColor: color,
  color: 'white',
  border: 'none',
  padding: '10px 20px',
  margin: '0 10px',
  borderRadius: 5,
  cursor: 'pointer',
  fontSize: 16
});

export default App;
