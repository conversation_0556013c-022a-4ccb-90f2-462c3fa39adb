import { Play, BookOpen, Video, MessageSquare } from "lucide-react"
import "./Dash.css"
import React, { useState, useEffect } from "react"

const Dashboard = () => {
  const [username, setUsername] = useState("")
  const [studyPlan, setStudyPlan] = useState([])
  const [planItems, setPlanItems] = useState([])
  const [earliestDate, setEarliestDate] = useState("")
  const [lasttDate, setLastDate] = useState("")

  useEffect(() => {
    const userData = JSON.parse(localStorage.getItem("user-data"))
    if (!userData) {
      window.location.href = "/login"
    }
    setUsername(userData.firstName)

    const fetchData = async () => {
      try {
        const response = await fetch(
          `https://api.aiclassroom.in/study-plan?userId=${userData.userId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        )

        if (response.status !== 200) {
          throw new Error("Failed to fetch data")
        }

        const data = await response.json()
        setStudyPlan(data)

        // Get earliest and latest dates
        let earliestDate = new Date(data[0].startDate)
        let lastDate = new Date(data[0].endDate)

        data.forEach((item) => {
          const startDate = new Date(item.startDate)
          const endDate = new Date(item.endDate)
          if (startDate < earliestDate) earliestDate = startDate
          if (endDate > lastDate) lastDate = endDate
        })

        setEarliestDate(earliestDate.toLocaleDateString())
        setLastDate(lastDate.toLocaleDateString())

        // Find today's plan
        const today = new Date()
        const todaysPlan = data.find((plan) => {
          const start = new Date(plan.startDate)
          const end = new Date(plan.endDate)
          return start <= today && today <= end
        })

        if (todaysPlan) {
          setPlanItems(todaysPlan.planItems || [])
        } else {
          setPlanItems([])
        }

      } catch (error) {
        console.error("Error fetching data:", error)
      }
    }

    fetchData()
  }, [])

  const calculatecurrentweek = (startDate) => {
    if (startDate === "") return 0
    const currentDate = new Date()
    const start = new Date(startDate)
    const timeDiff = currentDate - start
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    const weeksDiff = Math.floor(daysDiff / 7)
    return weeksDiff + 1
  }

  const parseDate = (dateStr) => {
    const [day, month, year] = dateStr.split('/').map(Number)
    return new Date(year, month - 1, day)
  }

  const calculateTotalWeeks = (startDate, endDate) => {
    if (!startDate || !endDate) return 0
    const start = parseDate(startDate)
    const end = parseDate(endDate)
    if (isNaN(start) || isNaN(end)) {
      console.error("Invalid date(s):", start, end)
      return 0
    }
    if (start > end) return 0
    const timeDiff = end - start
    const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
    const weeksDiff = Math.ceil(daysDiff / 7)
    return weeksDiff
  }

  return (
    <div className="dashboard-container">
      <div className="welcome-section">
        <div>
          <h2>Welcome back, {username}</h2>
          <p>You're on week {calculatecurrentweek(earliestDate)} of your {calculateTotalWeeks(earliestDate, lasttDate)}-week learning plan</p>
        </div>
        <button className="primary-button">Continue Today's Goals</button>
      </div>

      <div className="dashboard-content">
        <div className="goals-section">
          <div className="section-header">
            <h3>Today's Learning Goals</h3>
            <div className="progress-circle">
              <svg viewBox="0 0 36 36">
                <path
                  className="progress-circle-bg"
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className="progress-circle-fill"
                  strokeDasharray="67, 100"
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <span className="progress-text">
                {Math.floor((calculatecurrentweek(earliestDate) / calculateTotalWeeks(earliestDate, lasttDate)) * 100)}%
              </span>
            </div>
          </div>

          <div className="goals-list">
            {planItems.length > 0 ? (
              planItems.map((item, index) => (
                <div className="goal-item" key={index}>
                  <div className="goal-icon quiz-icon">
                    <Play size={16} />
                  </div>
                  <div className="goal-info">
                    <h4>{item.title || "Untitled Task"}</h4>
                    <p>{item.description || "No description"}</p>
                  </div>
                  <button className="secondary-button">Start</button>
                </div>
              ))
            ) : (
              <p>No learning goals for today.</p>
            )}
          </div>
        </div>

        <div className="streak-section">
          <h3>Your Learning Streak</h3>
          <div className="streak-display">
            <div className="streak-icon">
              <div className="flame-icon">🔥</div>
            </div>
            <div className="streak-days">5 days</div>
          </div>
          <p>Keep going! You're on your longest streak yet.</p>
          <div className="streak-progress">
            <div className="streak-bar">
              <div className="streak-fill" style={{ width: "71%" }}></div>
            </div>
            <p>Next milestone: 7-day streak (+50 coins)</p>
          </div>
        </div>
      </div>

      <div className="tools-section">
        <h3>AI Tools</h3>
        <div className="tools-grid">
          <div className="tool-card">
            <div className="tool-icon video-tool">
              <Video size={24} />
            </div>
            <h4>Generate Notes to Video</h4>
            <p>Convert your notes into video</p>
          </div>

          <div className="tool-card">
            <div className="tool-icon idea-tool">
              <div className="lightbulb-icon">💡</div>
            </div>
            <h4>Topic Based AI Video</h4>
            <p>Generate Videos On Any Topic</p>
          </div>

          <div className="tool-card">
            <div className="tool-icon animation-tool">
              <Play size={24} />
            </div>
            <h4>PDF to Animations</h4>
            <p>Get AI explanations</p>
          </div>

          <div className="tool-card">
            <div className="tool-icon interview-tool">
              <MessageSquare size={24} />
            </div>
            <h4>AI Interview</h4>
            <p>1 on 1 AI Guidance</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
