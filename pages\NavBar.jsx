// "use client";
// import { useState } from "react";
// import { Link, useNavigate } from "react-router-dom";
// // import { useLanguage, languages } from "./LanguageContext"
// import { useTranslation } from "./useTranslation";
// import {
//   Search,
//   Upload,
//   DivideCircle,
//   UserCircle,
//   Menu,
//   X,
// } from "lucide-react";
// import Login from "./Login";
// import SignUp from "./Signup";
// const Book = "/book.png";
// const Bulb = "/bulb.png";
// const DoubleCircle = "/doubleCircle.png";
// const Laptop = "/laptop.png";
// const PlayButton = "/playButton.png";
// const ReactLogo = "/react.png";
// const Ruler = "/ruler.png";
// const WhiteBoard = "/whiteBoard.png";
// const Scale90 = "/Scale90.png";

// const HeaderLogo = "/logo.png";

// export default function NavBar() {
//   //const { language, toggleLanguage } = useLanguage();
//   //const { t } = useTranslation()

//   const [isMenuOpen, setIsMenuOpen] = useState(false);
//   const [model, setModel] = useState(false);
//   const [logged, setLogged] = useState(false);
//   const [user, setUser] = useState({});
//   const [FILE, setFILE] = useState(null);
//   //const fileInputRef = useRef(null);
//   //const fileInputRef2 = useRef(null);
//   const [isOpen, setIsOpen] = useState(false);
//   const [selectedLanguage, setSelectedLanguage] = useState("English");

//   const languages = [
//     "Hindi",
//     "English",
//     "Bengali",
//     "Oriya",
//     "Punjabi",
//     "Assamese",
//     "Kannada",
//     "Mallayalam",
//     "Marathi",
//     "Telugu",
//     "Tamil",
//     "Urdu",
//   ];
//   useState(() => {
//     localStorage.setItem("lang", selectedLanguage);
//   }, [selectedLanguage]);
//   // const handleIconClick = () => {
//   //   fileInputRef.current.click(); // Trigger hidden file input
//   // };
//   const navigate = useNavigate();
//   // const handleIconClick2 = () => {
//   //   fileInputRef2.current.click(); // Trigger hidden file input
//   // };

//   // const handleFileChange = (event) => {
//   //   const file = event.target.files[0];
//   //   if (file && file.type === "application/pdf") {
//   //     console.log("Selected PDF:", file);
//   //     setFILE(file);
//   //     // You can pass the file to a handler if needed
//   //     navigate("/upload-page", { state: { selectedFile: file } });
//   //   } else {
//   //     alert("Please select a PDF file.");
//   //   }
//   // };

//   // const handleFileChange2 = (event) => {
//   //   const file = event.target.files[0];
//   //   if (file && file.type === "application/pdf") {
//   //     console.log("Selected PDF:", file);
//   //     setFILE(file);
//   //     // You can pass the file to a handler if needed
//   //     navigate("/ask-doubt", { state: { selectedFile: file } });
//   //   } else {
//   //     alert("Please select a PDF file.");
//   //   }
//   // };
//   useState(() => {
//     const token = localStorage.getItem("auth-token");
//     const userData = localStorage.getItem("user-data");
//     if (token) {
//       setLogged(true);
//       setUser(JSON.parse(userData));
//     } else {
//       setLogged(false);
//     }
//   }, []);
//   console.log(user);
//   const toggleMenu = () => {
//     setIsMenuOpen(!isMenuOpen);
//   };
//   const [isSignUp, setIsSignUp] = useState(true);
//   const nav = useNavigate();

//   return (
//     <header className="w-full py-4 px-4 md:px-6 flex items-center justify-between fixed top-0 left-0 right-0 bg-white">
//       {/* Logo */}
//       <button onClick={() => nav("/")} className="flex items-center">
//         <img
//           src={HeaderLogo}
//           alt="Logo"
//           width={100}
//           height={50}
//           className="w-[80px] md:w-[100px]"
//         />
//       </button>
//       {/* Mobile Menu Button */}
//       <button onClick={toggleMenu} className="md:hidden z-50">
//         {isMenuOpen ? (
//           <X className="h-7 w-7 text-purple-900" />
//         ) : (
//           <Menu className="h-7 w-7 text-purple-900" />
//         )}
//       </button>

//       {/* Desktop Menu */}
//       <nav className="hidden md:flex items-center gap-6">
//         <button
//           onClick={() => nav("/study-plan")}
//           className="text-sm font-medium hover:text-purple-700 cursor-pointer"
//         >
//           Study Planner
//         </button>
//         <button
//           onClick={() => nav("/rewards")}
//           className="text-sm font-medium hover:text-purple-700 cursor-pointer"
//         >
//           Redeem
//         </button>
//         <button
//           onClick={() => {
//             window.scrollTo({
//               top: 3300,
//               behavior: "smooth",
//             });
//           }}
//           className="text-sm cursor-pointer font-medium hover:text-purple-700 transition-colors duration-200"
//         >
//           Pricing
//         </button>
//         <button
//           onClick={() => nav("/contact")}
//           className="text-sm font-medium hover:text-purple-700 cursor-pointer"
//         >
//           Contact Us
//         </button>
//         <button
//           onClick={() => nav("/contest")}
//           className="text-sm font-medium hover:text-purple-700 cursor-pointer"
//         >
//           Contest
//         </button>

//         {/* Language Toggle */}
//         <div className="relative inline-block">
//           <button
//             onClick={() => setIsOpen(!isOpen)}
//             className="flex text-md items-center justify-between rounded-full bg-purple-100 border border-purple-300 text-purple-900 p-1 pl-6 h-auto leading-tight focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
//           >
//             {selectedLanguage}
//             <svg
//               className="fill-current h-4 w-4 ml-2"
//               xmlns="http://www.w3.org/2000/svg"
//               viewBox="0 0 20 20"
//             >
//               <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
//             </svg>
//           </button>

//           {isOpen && (
//             <div className="absolute z-10 mt-1 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
//               <div className="py-1">
//                 {languages.map((language) => (
//                   <button
//                     key={language}
//                     onClick={() => {
//                       setSelectedLanguage(language);
//                       setIsOpen(false);
//                     }}
//                     className={`block w-full text-left px-4 py-2 text-md ${
//                       selectedLanguage === language
//                         ? "bg-purple-100 text-purple-900"
//                         : "text-gray-700 hover:bg-gray-100"
//                     }`}
//                   >
//                     {language}
//                   </button>
//                 ))}
//               </div>
//             </div>
//           )}
//         </div>

//         {/* Notification & User Icon */}
//         {/* Inside your desktop menu (replace the current login/signup buttons section) */}
//         <div className="flex items-center gap-2">
//           {logged ? (
//             <div className="flex items-center gap-4">
//               <div className="flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full">
//                 <span className="text-sm font-medium text-purple-900">
//                   {user.coins} coins
//                 </span>
//               </div>
//               <div className="flex items-center gap-2">
//                 <img
//                   src={user.avatar}
//                   alt={user.firstName}
//                   className="w-8 h-8 rounded-full object-cover"
//                 />
//                 <span className="text-sm font-medium">{user.firstName}</span>
//               </div>
//             </div>
//           ) : (
//             <div>
//               <div className="flex justify-center space-x-4">
//                 <button
//                   className={`py-1 px-4 rounded-md text-sm font-medium cursor-pointer ${
//                     isSignUp
//                       ? "bg-purple-600 text-white"
//                       : "bg-gray-200 text-gray-700"
//                   }`}
//                   onClick={() => {
//                     setIsSignUp(true);
//                     setModel(true);
//                   }}
//                 >
//                   Sign Up
//                 </button>
//                 <button
//                   className={`py-1 px-4 rounded-md text-sm font-medium cursor-pointer ${
//                     !isSignUp
//                       ? "bg-purple-600 text-white"
//                       : "bg-gray-200 text-gray-700"
//                   }`}
//                   onClick={() => {
//                     setIsSignUp(false);
//                     setModel(true);
//                   }}
//                 >
//                   Login
//                 </button>
//               </div>
//               {model && (
//                 <>
//                   {isSignUp ? (
//                     <SignUp setModel={setModel} />
//                   ) : (
//                     <Login setModel={setModel} />
//                   )}
//                 </>
//               )}
//             </div>
//           )}
//         </div>
//       </nav>

//       {/* Mobile Menu Overlay */}
//       <div
//         className={`fixed inset-0 bg-black/50 transition-opacity duration-300 ${
//           isMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
//         }`}
//         onClick={toggleMenu}
//       ></div>

//       <div
//         className={`fixed top-0 right-0 w-2/3 max-w-xs h-full bg-white shadow-lg transform transition-transform duration-300 ${
//           isMenuOpen ? "translate-x-0" : "translate-x-full"
//         } md:hidden flex flex-col p-6 z-50`}
//       >
//         {/* Close Button */}
//         <button className="absolute top-4 right-4" onClick={toggleMenu}>
//           <X className="h-6 w-6 text-purple-900" />
//         </button>

//         {/* Menu Items */}
//         <nav className="mt-12 space-y-6">
//           <button
//             onClick={() => nav("/study-plan")}
//             className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
//           >
//             Study Planner
//           </button>
//           <button
//             onClick={() => nav("/rewards")}
//             className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
//           >
//             Redeem
//           </button>
//           <button
//             onClick={() => {
//               toggleMenu();
//               window.scrollTo({
//                 top: 4300,
//                 behavior: "smooth",
//               });
//             }}
//             className="block text-lg font-medium text-gray-900 hover:text-purple-700"
//           >
//             Pricing
//           </button>
//           <button
//             onClick={() => {
//               nav("/contact");
//               toggleMenu();
//             }}
//             className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
//           >
//             Contact Us
//           </button>

//           {/* Language Toggle */}
//           <div className="pt-4 border-t">
//             <div className="flex items-center justify-center rounded-full bg-purple-100 p-1 w-fit mx-auto">
//               <button
//                 variant="ghost"
//                 className="rounded-full px-4 py-1 h-auto bg-purple-900 text-white text-xs"
//               >
//                 English
//               </button>
//               <button
//                 variant="ghost"
//                 className="rounded-full px-4 py-1 h-auto text-xs"
//               >
//                 Hindi
//               </button>
//             </div>
//           </div>

//           {/* Notification & User */}
//           {/* Inside your mobile menu */}
//           <div className="flex justify-between items-center mt-6">
//             {logged ? (
//               <>
//                 <div className="flex items-center gap-1 bg-purple-100 text-purple-900 rounded-full px-3 py-1">
//                   <span className="text-sm font-medium">
//                     {user.coins} coins
//                   </span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <img
//                     src={user.avatar}
//                     alt={user.firstName}
//                     className="w-8 h-8 rounded-full object-cover"
//                   />
//                   <span className="text-sm font-medium">{user.firstName}</span>
//                 </div>
//               </>
//             ) : (
//               <div className="flex gap-2 w-full justify-center">
//                 <button
//                   className="py-1 px-4 rounded-md text-sm font-medium bg-purple-600 text-white"
//                   onClick={() => {
//                     setIsSignUp(true);
//                     setModel(true);
//                     toggleMenu();
//                   }}
//                 >
//                   Sign Up
//                 </button>
//                 <button
//                   className="py-1 px-4 rounded-md text-sm font-medium bg-gray-200 text-gray-700"
//                   onClick={() => {
//                     setIsSignUp(false);
//                     setModel(true);
//                     toggleMenu();
//                   }}
//                 >
//                   Login
//                 </button>
//               </div>
//             )}
//           </div>
//         </nav>
//       </div>
//     </header>
//   );
// }
import React from "react";

function NavBar() {
  return <div></div>;
}

export default NavBar;
