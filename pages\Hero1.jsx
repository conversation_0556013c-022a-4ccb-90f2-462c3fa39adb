return (
    <div className="min-h-screen bg-white flex flex-col items-center px-4 md:px-16 relative overflow-hidden ">
      {/* Decorative Images - Responsive positioning */}
      <img
        src={Book}
        alt="Book"
        className="absolute top-[50%] left-[10%] md:left-[15%] w-[8vw] max-w-[70px] hidden sm:block"
      />
      <img
        src={Bulb}
        alt="Bulb"
        className="absolute top-[15%] right-[10%] md:right-[15%] w-[8vw] max-w-[70px] hidden sm:block"
      />
      <img
        src={DoubleCircle}
        alt="Double Circle"
        className="absolute top-[15%] left-[5%] md:left-[8%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={Laptop}
        alt="Laptop"
        className="absolute bottom-[5%] right-[2%] w-[25vw] max-w-[300px] hidden md:block"
      />
      <img
        src={PlayButton}
        alt="Play Button"
        className="absolute top-[45%] left-[8%] md:left-[12%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={ReactLogo}
        alt="React Logo"
        className="absolute top-[25%] right-[5%] md:right-[8%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={Ruler}
        alt="Ruler"
        className="absolute top-[22%] left-[20%] md:left-[20%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={Scale90}
        alt="Scale90"
        className="absolute bottom-[5%] left-[50%] md:left-[55%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={WhiteBoard}
        alt="White Board"
        className="absolute bottom-[10%] left-[10%] md:left-[12%] w-[8vw] max-w-[70px] hidden sm:block"
      />

      {/* Header - Desktop and Mobile */}
      <header className="w-full py-4 px-4 md:px-6 flex items-center justify-between fixed top-0 left-0 right-0 bg-white">
        {/* Logo */}
        <button onClick={() => nav("/")} className="flex items-center">
          <img
            src={HeaderLogo}
            alt="Logo"
            width={100}
            height={50}
            className="w-[80px] md:w-[100px]"
          />
        </button>
        {/* Mobile Menu Button */}
        <button onClick={toggleMenu} className="md:hidden z-2">
          {isMenuOpen ? (
            <X className="h-7 w-7 text-purple-900" />
          ) : (
            <Menu className="h-7 w-7 text-purple-900" />
          )}
        </button>

        {/* Desktop Menu */}
        <nav className="hidden md:flex items-center gap-6">
          <button
            onClick={() => nav("/study-plan")}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Study Planner
          </button>
          <button
            onClick={() => nav("/rewards")}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Redeem
          </button>
          <button
            onClick={() => {
              window.scrollTo({
                top: 3300,
                behavior: "smooth",
              });
            }}
            className="text-sm cursor-pointer font-medium hover:text-purple-700 transition-colors duration-200"
          >
            Pricing
          </button>
          <button
            onClick={() => nav("/contact")}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Contact Us
          </button>
          <button
            onClick={() => nav("/contest")}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Contest
          </button>
          <button
            onClick={() => window.location.href = "https://commercial.aiclassroom.in/"}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer">
            Commercial
          </button>

          {/* Language Toggle */}
          <div className="relative inline-block">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="flex text-md items-center justify-between rounded-full bg-purple-100 border border-purple-300 text-purple-900 p-1 pl-6 h-auto leading-tight focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              {selectedLanguage}
              <svg
                className="fill-current h-4 w-4 ml-2"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
              >
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </button>

            {isOpen && (
              <div className="absolute z-2 mt-1 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div className="py-1">
                  {languages.map((language) => (
                    <button
                      key={language}
                      onClick={() => {
                        setSelectedLanguage(language);
                        setIsOpen(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-md ${selectedLanguage === language
                          ? "bg-purple-100 text-purple-900"
                          : "text-gray-700 hover:bg-gray-100"
                        }`}
                    >
                      {language}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Notification & User Icon */}
          {/* Inside your desktop menu (replace the current login/signup buttons section) */}
          <div className="flex items-center gap-2">
            {logged ? (
              <div
                className="flex relative cursor-pointer items-center gap-4"
                onClick={() => setShowDropdown(!showDropdown)}
              >
                <div className="flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full">
                  <span className="text-sm font-medium text-purple-900">
                    {user.coins} coins
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <img
                    src={user.avatar}
                    alt={user.firstName}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <span className="text-sm font-medium">{user.firstName}</span>
                </div>
                {showDropdown && (
                  <div className="absolute right-0 mt-32 bg-gray-200 shadow-md rounded-md py-2 z-2">

                    <button
                      onClick={handleLogout}
                      className="w-full text-left font-bold px-4 py-2 text-sm hover:bg-gray-100"
                    >
                      Logout
                    </button>
                    <button
                      onClick={() => navigate("/quiz-results")}
                      className="w-full text-left font-bold px-4 py-2 text-sm hover:bg-gray-100"
                    >
                      See Quiz Results
                    </button>
                    {/* Array Options */}
                    {/* {arrayOptions.map((item, index) => (
                          <button
                            key={index}
                            onClick={() => console.log(item)}
                            className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                          >
                            {item}
                          </button>
                        ))} */}
                  </div>
                )}
              </div>
            ) : (
              <div>
                <div className="flex justify-center space-x-4">
                  <button
                    className={`py-1 px-4 rounded-md text-sm font-medium cursor-pointer ${isSignUp
                        ? "bg-purple-600 text-white"
                        : "bg-gray-200 text-gray-700"
                      }`}
                    onClick={() => {
                      setIsSignUp(true);
                      setModel(true);
                    }}
                  >
                    Sign Up
                  </button>
                  <button
                    className={`py-1 px-4 rounded-md text-sm font-medium cursor-pointer ${!isSignUp
                        ? "bg-purple-600 text-white"
                        : "bg-gray-200 text-gray-700"
                      }`}
                    onClick={() => {
                      setIsSignUp(false);
                      setModel(true);
                    }}
                  >
                    Login
                  </button>
                </div>
                {model && (
      <>
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          onClick={() => setModel(false)}
        ></div>
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {isSignUp ? <SignUp setModel={setModel} /> : <Login setModel={setModel} />}
        </div>
      </>
    )}

              </div>
            )}
          </div>
        </nav>

        {/* Mobile Menu Overlay */}
        <div
          className={`fixed inset-0 bg-black/50 transition-opacity duration-300 ${isMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
            }`}
          onClick={toggleMenu}
        ></div>

        <div
          className={`fixed top-0 right-0 w-2/3 max-w-xs h-full bg-white shadow-lg transform transition-transform duration-300 ${isMenuOpen ? "translate-x-0" : "translate-x-full"
            } md:hidden flex flex-col p-6 z-2`}
        >
          {/* Close Button */}
          <button className="absolute top-4 right-4" onClick={toggleMenu}>
            <X className="h-6 w-6 text-purple-900" />
          </button>

          {/* Menu Items */}
          <nav className="mt-12 space-y-6">
            <button
              onClick={() => nav("/study-plan")}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Study Planner
            </button>
            <button
              onClick={() => nav("/rewards")}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Redeem
            </button>
            <button
              onClick={() => {
                toggleMenu();
                window.scrollTo({
                  top: 4300,
                  behavior: "smooth",
                });
              }}
              className="block text-lg font-medium text-gray-900 hover:text-purple-700"
            >
              Pricing
            </button>
            <button
              onClick={() => {
                nav("/contact");
                toggleMenu();
              }}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Contact Us
            </button>
            <button
              onClick={() => nav("/contest")}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Contest
            </button>

            {/* Language Toggle */}
            <div className="pt-4 border-t">
              <div className="flex items-center justify-center rounded-full bg-purple-100 p-1 w-fit mx-auto">
                <button
                  variant="ghost"
                  className="rounded-full px-4 py-1 h-auto bg-purple-900 text-white text-xs"
                >
                  English
                </button>
                <button
                  variant="ghost"
                  className="rounded-full px-4 py-1 h-auto text-xs"
                >
                  Hindi
                </button>
              </div>
            </div>

            {/* Notification & User */}
            {/* Inside your mobile menu */}
            <div className="flex justify-between mt-6">
              {logged ? (
                <>
                  <div className=" bg-purple-100 text-purple-900 rounded-full px-3 py-1">
                    <span className="text-sm font-medium">
                      {user.coins} coins
                    </span>
                  </div>

                  <div
                    className="relative w-fit cursor-pointer"
                    onClick={() => setShowDropdown(!showDropdown)}
                  >
                    <div className="flex items-center gap-2">
                      <img
                        src={user.avatar}
                        alt={user.firstName}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <span className="text-sm font-medium">
                        {user.firstName}
                      </span>
                    </div>
                    {showDropdown && (
                      <div className="absolute right-0 mt-2 w-[150px] bg-gray-200 shadow-md rounded-md py-2 z-2">
                        <div
                          onClick={() => navigate("/quiz-results")}
                          className="w-full text-left font-bold px-4 text-sm hover:bg-gray-100 cursor-pointer"
                        >
                          See Quiz Results
                        </div>
                        <div
                          onClick={handleLogout}
                          className="w-full text-left font-bold px-4 mt-2 text-sm cursor-pointer hover:bg-gray-100"
                        >
                          Logout
                        </div>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <div className="flex gap-2 w-full justify-center">
                  <button
                    className="py-1 px-4 rounded-md text-sm font-medium bg-purple-600 text-white"
                    onClick={() => {
                      setIsSignUp(true);
                      setModel(true);
                      toggleMenu();
                    }}
                  >
                    Sign Up
                  </button>
                  <button
                    className="py-1 px-4 rounded-md text-sm font-medium bg-gray-200 text-gray-700"
                    onClick={() => {
                      setIsSignUp(false);
                      setModel(true);
                      toggleMenu();
                    }}
                  >
                    Login
                  </button>
                </div>
              )}
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="text-center mt-[100px] max-w-2xl px-4">
        <h2 className="text-2xl md:text-4xl font-bold text-purple-700">
          Welcome To AI Classroom
        </h2>
        <p className="text-lg md:text-[21px] text-gray-700 font-semibold mt-2">
          Classroom in your study table
        </p>
        <p className="text-gray-600 mt-4 text-sm md:text-base">
          Transform Your Textbooks Into Engaging Animated Videos With Our
          AI-Driven Platform.
          <br className="hidden md:block" />
          Enjoy Real-Time Video Summaries.
        </p>
      </main>

      <div className="mt-8 w-full max-w-2xl px-4 relative z-0" >
        <div className="flex items-center bg-white shadow-md rounded-full p-2 z-0" style={{ zIndex: 0 }}>
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Search for topics, chapters, or keywords..."
            className="w-full px-4 py-2 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-600 z-0" style={{ zIndex: 0 }}
          />
          <button className="bg-purple-600 text-white rounded-full p-2 ml-2" onClick={async () => {
            await fetch(`https://api.aiclassroom.in/convertupload`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({ query: input })
            }).then((res) => res.json()).then((data) => {
              navigate("/upload-page", { state: { selectedFile: data.firleUrl, fileUrl: true } });
            }).catch((err) => {
              console.log(err);
            })
          }}>
            <Search size={20} />
          </button>
        </div>

        {showSuggestions && suggestions.length > 0 && (
          <ul className="absolute top-full mt-2 w-full bg-white shadow-lg rounded-lg z-2 max-h-60 overflow-y-auto">
            {suggestions.map((s, index) => (
              <li
                key={index}
                onClick={() => handleSelect(s)}
                className="px-4 py-2 hover:bg-purple-100 cursor-pointer"
              >
                {s}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Action Cards */}
      <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 w-full max-w-xl px-4">
        <div className="bg-white shadow-md rounded-lg p-4 md:p-6 flex flex-col items-center text-center">
          {/* Hidden input */}
          <input
            type="file"
            accept="application/pdf"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: "none" }}
          />

          {/* Upload Icon */}
          <Upload
            size={30}
            className="text-red-500 mb-3 cursor-pointer"
          // onClick={handleIconClick}
          />

          <h3 className="text-base md:text-lg font-semibold mb-3">
            Upload Your Files for Animation
          </h3>

          {/* Upload Button */}
          <button
            // onClick={handleUploadClick}
            onClick={handleIconClick}
            className="bg-purple-600 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-md hover:bg-purple-700 text-sm"
          >
            Upload
          </button>
        </div>

        <div className="bg-white shadow-md rounded-lg p-4 md:p-6 flex flex-col items-center text-center">
          {/* Hidden input */}
          <input
            type="file"
            accept="application/pdf"
            ref={fileInputRef2}
            onChange={handleFileChange2}
            style={{ display: "none" }}
          />
          <DivideCircle size={30} className="text-purple-500 mb-3" />
          <h3 className="text-base md:text-lg font-semibold mb-3">
            Convert Your Notes to Video
          </h3>
          <button
            onClick={handleIconClick2}
            className="bg-purple-600 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-md hover:bg-purple-700 text-sm"
          >
            Ask
          </button>
        </div>
      </div>

      {/* Background Decorative Elements */}
      <div className="absolute top-0 left-0 opacity-20">
        <svg width="150" height="150" className="text-purple-200">
          <circle cx="50" cy="50" r="40" fill="currentColor" />
        </svg>
      </div>
      <div className="absolute bottom-0 right-0 opacity-20">
        <svg width="150" height="150" className="text-blue-200">
          <circle cx="150" cy="150" r="40" fill="currentColor" />
        </svg>
      </div>
    </div>
  );
}
without changing anything , just correct the z-index of everything and model also , make sure u dont remove my code and give me full corrected code