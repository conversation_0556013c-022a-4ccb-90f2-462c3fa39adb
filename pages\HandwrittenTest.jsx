import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '/src/assets/fonts.css';
import axios from 'axios';

const HandwrittenTest = () => {
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const [isMobile, setIsMobile] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [testData, setTestData] = useState({
        userName: '',
        subject: '',
        totalQuestions: 0,
        totalMarks: 0,
        questions: [],
        timeLeft: '00:00:00'
    });
    const navigate = useNavigate();

    const translator = (word1, word2) =>
        localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
            ? word1
            : localStorage.getItem("lang")
                ? word2
                : word1;

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        handleResize(); // Initial check
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useEffect(() => {
        fetchTestData();
    }, []);

    const fetchTestData = async () => {
        try {
            setLoading(true);
            // Replace with your actual API endpoint
            const response = await axios.get('https://api.example.com/test-data');
            setTestData(response.data);
            setLoading(false);
        } catch (err) {
            console.error('Error fetching test data:', err);
            //setError('Failed to load test data. Please try again later.');
            setLoading(false);

            // Fallback to dummy data for development
            setTestData({
                userName: 'Arindam',
                subject: 'HISTORY',
                totalQuestions: 30,
                totalMarks: 30,
                timeLeft: '00:09:47',
                questions: [
                    {
                        id: 1,
                        text: 'What were the main causes of the Revolt of 1857 in India?'
                    },
                    {
                        id: 2,
                        text: 'Describe the socio-economic and political conditions in India before the advent of British rule and how these factors contributed to the establishment of British dominance in the subcontinent. Discuss the role of key historical events such as the Battle of Plassey and the Battle of Buxar in consolidating British power.'
                    },
                    {
                        id: 3,
                        text: 'What was the significance of the Regulating Act of 1773 in the administration of British India?'
                    },
                    {
                        id: 4,
                        text: 'What was the significance of the Regulating Act of 1773 in the administration of British India?'
                    }
                ]
            });
        }
    };

    const handleFileUpload = (e) => {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            // Convert files to image URLs for preview
            const newUploadedFiles = files.map(file => ({
                id: Math.random().toString(36).substring(2),
                url: URL.createObjectURL(file),
                file
            }));
            setUploadedFiles(prev => [...prev, ...newUploadedFiles]);
        }
    };

    const handleRemoveFile = (fileId) => {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    };

    const handleUploadClick = () => {
        document.getElementById('file-upload').click();
    };

    const handleSubmit = async () => {
        try {
            // Prepare form data to send files
            const formData = new FormData();

            // Add all uploaded files to form data
            uploadedFiles.forEach((fileObj, index) => {
                formData.append(`answer_file_${index}`, fileObj.file);
            });

            // Add test ID or other metadata if needed
            // formData.append('testId', testData.id);

            // Send data to server
            const response = await axios.post('https://api.example.com/submit-test', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            alert('Answers submitted successfully!');
            // navigate('/results'); // Navigate to results page
        } catch (err) {
            console.error('Error submitting test:', err);
            alert('Failed to submit answers. Please try again.');
        }
    };

    if (loading) {
        return (
            <div className="w-full min-h-screen bg-white flex justify-center items-center">
                <p className="text-xl font-medium text-[#3A1078]">Loading test data...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="w-full min-h-screen bg-white flex justify-center items-center">
                <p className="text-xl font-medium text-red-500">{error}</p>
            </div>
        );
    }

    return (
        <div className="w-full min-h-screen bg-white">
            {/* Main Content */}
            <div className="w-full px-4 sm:px-6 md:px-[9%] py-4 md:py-6 bg-[#F9FDFF]">
                {/* Header Information */}
                <div className="flex justify-between items-center">
                    <div className="text-center">
                        <h1 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-[#3A1078] tracking-tighter font-poppins">All The Best!</h1>
                    </div>

                    <div className="flex justify-end mt-2 md:mt-4">
                        <div className="text-right">
                            <p className="text-lg md:text-xl lg:text-2xl font-medium text-[#3A1078] font-poppins">{translator("Name : ", "नाम : ")} {testData.userName}</p>
                            <p className="text-lg md:text-xl lg:text-2xl font-medium text-[#3A1078] font-poppins">{translator("Subject : ", "विषय : ")} {testData.subject}</p>
                        </div>
                    </div>
                </div>
                <div className="flex justify-center mt-2 md:mt-4">
                    <p className="text-xl md:text-2xl lg:text-3xl font-medium text-[#3A1078] tracking-tighter mt-2 md:mt-3 font-poppins">
                        {translator("Answer all questions with proper numbering, and submit your answers.", "सभी प्रश्नों को सही संख्या सहित उत्तर दें, और अपने उत्तर जमा करें।")}
                    </p>
                </div>
                {/* Test Container */}
                <div className="mt-4 md:mt-8 relative">
                    {/* Main Container with Shadow */}
                    <div className={`bg-white/80 rounded-[20px] md:rounded-[38px] border border-black/80 shadow-lg relative ${isMobile ? 'flex flex-col' : 'h-[75vh]'}`}>
                        {/* Questions Section */}
                        <div className={`px-4 py-4 md:pl-[5%] md:py-6 md:pr-[8%] ${isMobile ? 'w-full' : 'w-2/3'} ${isMobile ? 'h-[50vh]' : 'h-[calc(75vh-87px)]'} overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent`}>
                            {testData.questions.map((question) => (
                                <div key={question.id} className="mb-6 md:mb-8">
                                    <p className="text-lg md:text-xl lg:text-2xl font-medium font-advent-pro">
                                        Question {question.id} :<br />
                                        {question.text}
                                    </p>
                                </div>
                            ))}
                        </div>

                        {/* Bottom Bar */}
                        <div className={`bg-[#4C60A5] h-[60px] md:h-[87px] ${isMobile ? 'w-full rounded-b-[20px]' : 'w-[65%] rounded-bl-[38px]'} px-4 md:px-[8%] flex justify-between items-center ${isMobile ? '' : 'absolute bottom-0'}`}>
                            <p className="text-white text-sm md:text-lg lg:text-2xl font-normal uppercase font-jost">{translator("total questions: ", "कुल प्रश्न: ")} {testData.totalQuestions}</p>
                            <p className="text-white text-sm md:text-lg lg:text-2xl font-normal uppercase font-jost">{translator("total marks: ", "कुल अंक: ")} {testData.totalMarks}</p>
                        </div>
                    </div>

                    {/* Right Side Components */}
                    <div className={`${isMobile ? 'mt-4' : 'absolute right-0 top-[4rem]'} flex ${isMobile ? 'flex-row justify-between flex-wrap' : 'flex-col'} gap-3 md:gap-6 ${isMobile ? 'w-full' : 'w-1/3'}`}>
                        <div className={`${isMobile ? 'w-full' : ''}`}>
                            <p className="text-black font-bold text-base md:text-xl font-jost ml-4">{translator("Time Left", "समय बाकी")}</p>
                            <p className="text-[#08A064] font-bold text-2xl md:text-3xl font-jost ml-4">{testData.timeLeft}</p>
                        </div>

                        <div className="flex flex-col gap-4 md:gap-6 w-full">

                            {/* Uploaded Files Section */}
                            <div className="border border-dashed border-black/50 rounded-lg p-2 md:p-3 w-full mx-auto max-w-[90%]">
                                <p className="text-[#8B8B8B] text-xs md:text-sm text-center mb-2 md:mb-4 font-average-sans">{translator("Uploaded Files", "अपलोड किए गए फाइल")}</p>
                                <div className={`max-h-[120px] md:max-h-[150px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent ${uploadedFiles.length === 0 ? 'hidden' : ''}`}>
                                    <div className="grid grid-cols-5 gap-1 md:gap-2">
                                        {uploadedFiles.map((file, index) => (
                                            <div key={file.id} className="w-[30px] h-[30px] md:w-[39px] md:h-[39px] border border-[#959595] rounded-lg overflow-hidden mb-1 relative group">
                                                <img src={file.url} alt={`Upload ${index}`} className="w-full h-full object-cover" />
                                                <button
                                                    className="absolute top-0 right-0 bg-red-500 text-white w-[14px] h-[14px] md:w-[18px] md:h-[18px] flex items-center justify-center rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleRemoveFile(file.id);
                                                    }}
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 md:h-4 md:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                {uploadedFiles.length === 0 ? (
                                    <p className="text-[#8B8B8B] text-xs text-center py-4">{translator("No files uploaded", "कोई फाइल अपलोड नहीं की गई")}</p>
                                ) : (
                                    <p className="text-[#8B8B8B] text-xs text-center mt-1">
                                        {uploadedFiles.length} {translator("file", "फाइल")} {uploadedFiles.length !== 1 ? translator("s", "हैं") : ''} {translator("uploaded", "अपलोड की गई")}
                                    </p>
                                )}
                            </div>


                            {/* Upload Button */}
                            <div
                                className={`bg-[#342499] rounded-[12px] md:rounded-[20px] p-2 md:p-4 shadow-md flex flex-col items-center cursor-pointer hover:bg-[#2b1e7e] transition-colors ${isMobile ? 'flex-1' : 'w-[85%] mx-auto'}`}
                                onClick={handleUploadClick}
                            >
                                <div className="w-8 h-8 md:w-12 md:h-12 flex flex-col items-center justify-center">
                                    <img src="/images/upload-vector1.svg" alt="Upload" className="h-4 md:h-6" />
                                    <img src="/images/upload-vector2.svg" alt="Upload" className="w-6 md:w-10" />
                                </div>
                                <p className="text-[#EBB12B] font-bold text-xs md:text-sm mt-1 uppercase font-jost">{translator("upload files", "फाइल अपलोड करें")}</p>
                            </div>

                            {/* Submit Button */}
                            <button
                                className={`bg-[#005FD0] text-white rounded-[8px] md:rounded-[12px] h-[50px] md:h-[68px] shadow-md font-medium text-base md:text-xl font-afacad hover:bg-[#0050b1] transition-colors ${isMobile ? 'flex-1' : 'w-[85%] mx-auto'}`}
                                onClick={handleSubmit}
                            >
                                {translator("Confirm and Submit", "सत्यापित और सबमिट करें")}
                            </button>
                        </div>

                    </div>

                    {/* Hidden File Input */}
                    <input
                        type="file"
                        multiple
                        onChange={handleFileUpload}
                        className="hidden"
                        id="file-upload"
                        accept="image/*"
                    />
                </div>
            </div>
        </div>
    );
};

export default HandwrittenTest;
