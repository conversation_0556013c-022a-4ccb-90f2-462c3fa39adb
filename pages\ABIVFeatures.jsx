"use client";
import {
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Facebook,
  UserCircle,
  Menu,
  X,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const HeaderLogo = "/logo.png";
const Bulb = "/bulb.png";
const DoubleCircle = "/doubleCircle.png";
const ReactLogo = "/react.png";
const Ruler = "/ruler.png";
const Scale90 = "/Scale90.png";

export default function AbivFeatures() {
  const features = [
    {
      title: "Instant Generation",
      description: "Creating content in real time",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
    },
    {
      title: "Adaptive Questioning",
      description: "Personalized for your unique learning journey.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
          />
        </svg>
      ),
    },
    {
      title: "Multilingual Support",
      description: "Generating content in Hindi and English",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
          />
        </svg>
      ),
    },
    {
      title: "Interview Mode",
      description: "Continuously engaging you with questions from your PDF.",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
          />
        </svg>
      ),
    },
    {
      title: "Comprehensive Notes",
      description: "Offering easily downloadable, well-structured notes",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
    },
    {
      title: "Flexible Learning Mode",
      description: "Designed for Teachers, Students and All",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12 text-purple-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
      ),
    },
  ];
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <div className="min-h-screen flex flex-col items-center px-4 md:px-16 py-10 relative overflow-hidden">
      <img
        src={Bulb}
        alt="Bulb"
        className="absolute top-[13%] right-[5%] md:right-[5%] w-[8vw] max-w-[70px] hidden sm:block"
      />
      <img
        src={DoubleCircle}
        alt="Double Circle"
        className="absolute top-[15%] left-[5%] md:left-[8%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={ReactLogo}
        alt="React Logo"
        className="absolute top-[65%] right-[3%] md:right-[3%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={Ruler}
        alt="Ruler"
        className="absolute top-[60%] left-[5%] md:left-[5%] w-[6vw] max-w-[50px] hidden sm:block"
      />
      <img
        src={Scale90}
        alt="Scale90"
        className="absolute bottom-[2%] left-[50%] md:left-[55%] w-[6vw] max-w-[50px] hidden sm:block"
      />

      {/* Main Content */}
      <main className="w-full mt-[50px] max-w-6xl">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-purple-700 mb-4">
            AI Classroom IS AVAILABLE FOR ALL
          </h2>
          <p className="text-gray-600 text-lg max-w-3xl mx-auto">
            Enjoy Real-Time Video Summaries, Multi-Language Support, And
            Interactive Learning Anytime, Anywhere.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl shadow-lg p-4 flex flex-col items-center text-center hover:scale-105 transition-transform duration-300"
            >
              <div className="mb-2">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-purple-700 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
}
