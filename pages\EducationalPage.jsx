"use client"

import { useState, useEffect } from "react"
import { FiArrowLeft, FiUser } from "react-icons/fi"
import RotatingBox from "./RotatingBox"
import "./EducationalPage.css"

function EducationalPage({ userName, userClass, courseTitle }) {
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading progress
    const interval = setInterval(() => {
      setLoadingProgress((prev) => {
        const newProgress = prev + 1
        if (newProgress >= 100) {
          clearInterval(interval)
          setTimeout(() => setIsLoading(false), 500) // Keep showing 100% for a moment
          return 100
        }
        return newProgress
      })
    }, 50)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="container">
      {/* Header */}
      <header className="header">
        <div className="header-left">
          <button className="back-button">
            <FiArrowLeft size={24} />
          </button>
          <div className="user-info">
            <p className="greeting">
              Hello, <span className="user-name">{userName}</span>
            </p>
            <p className="user-class">{userClass}</p>
          </div>
        </div>
        <div className="profile-icon">
          <FiUser size={20} />
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        <h1 className="title">{courseTitle}</h1>

        {isLoading && (
          <div className="loading-container">
            <div className="rotating-box-container">
              <RotatingBox />
            </div>

            <div className="loading-progress">
              <p className="loading-text">Loading</p>
              <div className="progress-bar-container">
                <div className="progress-bar" style={{ width: `${loadingProgress}%` }}></div>
              </div>
            </div>
          </div>
        )}

        {!isLoading && (
          <div className="content">
            <p>Content will appear here after loading is complete.</p>
            {/* Add your actual content here */}
          </div>
        )}
      </main>
    </div>
  )
}

export default EducationalPage
