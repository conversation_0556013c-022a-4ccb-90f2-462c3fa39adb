"use client";
import {
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Facebook,
  UserCircle,
  Menu,
  X,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const HeaderLogo = "/logo.png";
const why1 = "whyTexttoVideo.png";
const why2 = "whyMCQ.png";
const why3 = "whyDoubts.png";
const why4 = "whyInterview.png";
const Bulb = "/bulb.png";
const DoubleCircle = "/doubleCircle.png";

export default function WhyAbiv() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <div className="relative overflow-hidden">
      {/* Decorative elements */}
      <img
        src={Bulb}
        alt="Bulb"
        className="absolute top-[13%] right-[5%] md:right-[5%] w-[8vw] max-w-[70px] hidden sm:block"
      />
      <img
        src={DoubleCircle}
        alt="Double Circle"
        className="absolute top-[15%] left-[5%] md:left-[8%] w-[6vw] max-w-[50px] hidden sm:block"
      />

      <div className="container mx-auto mt-[70px] px-4 py-12">
        <h1 className="text-4xl md:text-5xl font-bold text-purple-900 text-center mb-12">
          Why AI Classroom?
        </h1>

        <div className="max-w-4xl mx-auto space-y-6">
          <div className="bg-white rounded-lg p-6 border border-gray-100 shadow-sm flex items-center justify-between">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-purple-900">
                Text To Video
              </h2>
              <p className="text-gray-600">
                Converts any text to video with enriching user experience and
                study notes.
              </p>
            </div>
            <div className="relative flex align-center w-32 h-32">
              <img
                src={why1}
                alt="Book illustration"
                className="object-contain"
              />
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-100 shadow-sm flex items-center justify-between">
            <div className="relative flex align-center w-32 h-32">
              <img
                src={why2}
                alt="MCQ illustration"
                className="object-contain"
              />
            </div>
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-purple-900">MCQ's</h2>
              <p className="text-gray-600">
                Get curated MCQs available for you while you watch the animation
                and make yourself prepared.
              </p>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-100 shadow-sm flex items-center justify-between">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-purple-900">Doubts</h2>
              <p className="text-gray-600">
                Get Live Doubt Assistance while you study to make your topic
                enriching.
              </p>
            </div>
            <div className="relative flex align-center w-32 h-32">
              <img
                src={why3}
                alt="Question mark illustration"
                className="object-contain"
              />
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-100 shadow-sm flex items-center justify-between">
            <div className="relative flex align-center w-32 h-32">
              <img
                src={why4}
                alt="Interview illustration"
                className="object-contain"
              />
            </div>
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-purple-900">Interview</h2>
              <p className="text-gray-600">
                Upcoming Feature to help you cater yourself for Exam
                Preparation!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
