import axios from "axios";
import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const QuizResults = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState("All");
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [userData, setUserData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentUserSubmission, setCurrentUserSubmission] = useState(null);
  const [contest, setContest] = useState(null);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [userRank, setUserRank] = useState(null);

  useEffect(() => {
    // Check for user data in localStorage
    const storedUserData = localStorage.getItem("user-data");
    if (storedUserData) {
      try {
        const parsedUserData = JSON.parse(storedUserData);
        setUserData(parsedUserData);
      } catch (err) {
        console.error("Error parsing user data from localStorage", err);
      }
    }

    // Fetch contest data
    const fetchContestData = async () => {
      try {
        const response = await axios.get("https://api.aiclassroom.in/contest");
        const contestData = response?.data?.data;
        setContest(contestData);
        setTotalQuestions(contestData?.questions?.length || 0);

        // Process leaderboard data from contest submissions
        if (contestData?.submission) {
          const submissions = contestData.submission.map((item) => ({
            email: item.email,
            submission: item.submission,
          }));

          // Sort submissions by score (high to low) and time (low to high)
          const sortedSubmissions = [...submissions].sort(
            (a, b) =>
              b.submission.score - a.submission.score ||
              a.submission.timeTaken - b.submission.timeTaken
          );

          setLeaderboardData(sortedSubmissions);

          // Find current user's submission and rank
          if (userData?.email) {
            const userIndex = sortedSubmissions.findIndex(
              (entry) => entry.email === userData.email
            );

            if (userIndex !== -1) {
              setCurrentUserSubmission(sortedSubmissions[userIndex].submission);
              setUserRank(userIndex + 1);
            }
          }
        }

        setLoading(false);
      } catch (err) {
        setError("Failed to load contest data");
        console.error("Error fetching contest data:", err);
        setLoading(false);
      }
    };

    fetchContestData();
  }, [userData?.email]);
  console.log("contest leadr", leaderboardData);
  const [showLeaderboardModal, setShowLeaderboardModal] = useState(false);

  const handleViewLeaderboard = () => {
    setShowLeaderboardModal(true);
  };

  const handleCloseLeaderboard = () => {
    setShowLeaderboardModal(false);
  };

  useEffect(()=>{
    console.log(contest);
  }, [contest])

  const formatTime = (seconds) => {
    if (!seconds) return "00:00:00";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const filterQuestions = () => {
    if (!contest?.questions || !currentUserSubmission?.answers) {
      return [];
    }

    const questions = contest.questions.map((question, index) => {
      // Convert object format answers to array format
      const answersArray =
        currentUserSubmission.answers instanceof Object
          ? Object.entries(currentUserSubmission.answers).reduce(
              (acc, [qIndex, answer]) => {
                acc[parseInt(qIndex)] = answer;
                return acc;
              },
              []
            )
          : currentUserSubmission.answers;

      const userAnswerIndex = answersArray[index];
      const isAnswered =
        userAnswerIndex !== undefined && userAnswerIndex !== null;
      const isCorrect = userAnswerIndex === question.correct;

      return {
        number: index + 1,
        question: question.title,
        status: isCorrect ? "correct" : isAnswered ? "incorrect" : "unanswered",
        userAnswer: isAnswered ? question.options[userAnswerIndex] : null,
        correctAnswer: question.options[question.correct],
        isAnswered,
      };
    });

    switch (activeFilter) {
      case "Answered":
        return questions.filter((q) => q.isAnswered);
      case "Unanswered":
        return questions.filter((q) => !q.isAnswered);
      case "All":
      default:
        return questions;
    }
  };

  if (loading)
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );

  if (error)
    return (
      <div className="min-h-screen bg-gray-50 p-6 text-red-600">{error}</div>
    );
  if (!currentUserSubmission)
    return (
      <div className="min-h-screen bg-gray-50 p-6 text-center">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-bold mb-4">No submission found</h2>
          <p className="text-gray-600 mb-6">
            You haven't attempted quizes yet.
          </p>
          {contest?._id && (
            <Link
              to={`/contest/${contest._id}`}
              className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700"
            >
              Take Quiz
            </Link>
          )}
        </div>
      </div>
    );

  const filteredQuestions = filterQuestions();

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold">
                {contest?.title || "Quiz Results"}
              </h1>
              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                <span>{leaderboardData.length} Participants</span>
                <span>•</span>
                <span>{totalQuestions} Questions</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {userRank && (
                <span className="text-yellow-600 flex items-center gap-2">
                  🏆 Rank #{userRank} of {leaderboardData.length}
                </span>
              )}
              <button
                onClick={handleViewLeaderboard}
                className="bg-indigo-600 cursor-pointer text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                View Leaderboard
              </button>

              {/* Leaderboard Modal */}
              {showLeaderboardModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold">Leaderboard</h2>
                      <button
                        onClick={handleCloseLeaderboard}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        ✕
                      </button>
                    </div>

                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {leaderboardData.map((user, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg ${
                            user.email === userData?.email
                              ? "bg-indigo-50 border border-indigo-100"
                              : "bg-gray-50"
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-3">
                              <span className="font-medium text-gray-700">
                                {index + 1}.
                              </span>
                              <span
                                className={`${
                                  user.email === userData?.email
                                    ? "font-semibold text-indigo-600"
                                    : "text-gray-700"
                                }`}
                              >
                                {user.email}
                              </span>
                            </div>
                            <span className="font-medium">
                              Score: {user.submission.score}
                            </span>
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            Time: {formatTime(user.submission.timeTaken)}
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4 flex justify-end cursor-pointer">
                      <button
                        onClick={handleCloseLeaderboard}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Score Circle */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-6 text-center">
          <div className="relative inline-flex">
            <div className="w-32 h-32 rounded-full bg-blue-100 flex items-center justify-center">
              <div className="text-4xl font-bold text-blue-600">
                {totalQuestions > 0
                  ? Math.round(
                      (currentUserSubmission.score / totalQuestions) * 100
                    )
                  : 0}
                %
              </div>
            </div>
          </div>
          <h2 className="text-xl font-semibold mt-4">Quiz Submitted</h2>
          <p className="text-gray-600">
            You scored {currentUserSubmission.score} out of {totalQuestions}{" "}
            questions correctly
          </p>
        </div>

        {/* Quiz Summary */}
        <div className="bg-gray-100 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Quiz Summary</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Questions:</span>
              <span className="font-medium">{totalQuestions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Correct Answers:</span>
              <span className="font-medium text-green-600">
                {currentUserSubmission.score}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Time Taken:</span>
              <span className="font-medium">
                {formatTime(currentUserSubmission.timeTaken)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Rank:</span>
              <span className="font-medium text-yellow-600">
                #{userRank} of {leaderboardData.length}
              </span>
            </div>
          </div>
        </div>

        {/* Review Answers */}
        {contest?.questions?.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Review Your Answers</h3>
              <div className="flex gap-2">
                <button
                  onClick={() => setActiveFilter("All")}
                  className={`px-4 py-1 rounded-full text-sm ${
                    activeFilter === "All"
                      ? "bg-indigo-100 text-indigo-700"
                      : "text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  All ({contest.questions.length})
                </button>
                <button
                  onClick={() => setActiveFilter("Answered")}
                  className={`px-4 py-1 rounded-full text-sm ${
                    activeFilter === "Answered"
                      ? "bg-indigo-100 text-indigo-700"
                      : "text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  Answered (
                  {filterQuestions().filter((q) => q.isAnswered).length})
                </button>
                <button
                  onClick={() => setActiveFilter("Unanswered")}
                  className={`px-4 py-1 rounded-full text-sm ${
                    activeFilter === "Unanswered"
                      ? "bg-indigo-100 text-indigo-700"
                      : "text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  Unanswered (
                  {filterQuestions().filter((q) => !q.isAnswered).length})
                </button>
              </div>
            </div>

            {/* Questions List */}
            {filteredQuestions.length > 0 ? (
              <div className="space-y-4">
                {filteredQuestions.map((questionData) => (
                  <QuestionReviewCard
                    key={questionData.number}
                    number={questionData.number}
                    question={questionData.question}
                    status={questionData.status}
                    userAnswer={questionData.userAnswer}
                    correctAnswer={questionData.correctAnswer}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No questions match the selected filter
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const QuestionReviewCard = ({
  number,
  question,
  status,
  userAnswer,
  correctAnswer,
}) => {
  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-start gap-4">
        {status === "correct" ? (
          <span className="text-green-500 text-xl">✓</span>
        ) : status === "incorrect" ? (
          <span className="text-red-500 text-xl">✕</span>
        ) : (
          <span className="text-yellow-500 text-xl">!</span>
        )}
        <div className="w-full">
          <div className="font-medium">Question {number}</div>
          <p className="text-gray-600 mt-1">{question}</p>

          <div className="mt-3 space-y-2">
            {status !== "unanswered" ? (
              <div
                className={`p-2 rounded ${
                  status === "correct" ? "bg-green-50" : "bg-red-50"
                }`}
              >
                <span className="font-medium">Your answer:</span> {userAnswer}
              </div>
            ) : (
              <div className="p-2 rounded bg-yellow-50">
                <span className="font-medium">Not answered</span>
              </div>
            )}

            {status !== "correct" && (
              <div className="p-2 rounded bg-green-50">
                <span className="font-medium">Correct answer:</span>{" "}
                {correctAnswer}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizResults;
