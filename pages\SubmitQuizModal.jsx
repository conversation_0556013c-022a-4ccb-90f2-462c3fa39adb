import React from "react";
import { useNavigate } from "react-router-dom";

const SubmitQuizModal = ({ onClose, score, totalQuestions }) => {
  const navigate = useNavigate();

  const handleSubmit = () => {
    navigate("/quiz-results", {
      state: {
        score,
        totalQuestions,
      },
    });
  };

  // Calculate unanswered questions
  const unansweredQuestions = totalQuestions - score;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
     

      <button
            onClick={handleSubmit}
            className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Submit Quiz
          </button>
    </div>
  );
};

 {/* <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Quiz Summary</h2>

        <div className="space-y-4 mb-6">
          <div className="flex justify-between">
            <span className="text-gray-600">Your Score:</span>
            <span className="font-medium">
              {score}/{totalQuestions}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Percentage:</span>
            <span className="font-medium">
              {Math.round((score / totalQuestions) * 100)}%
            </span>
          </div>
        </div>

        {unansweredQuestions > 0 && (
          <div className="bg-yellow-50 border border-yellow-100 rounded-lg p-4 mb-6">
            <p className="text-yellow-800">
              You've {unansweredQuestions} unanswered question
              {unansweredQuestions !== 1 ? "s" : ""}. Are you sure you want to
              submit?
            </p>
          </div>
        )}

        <div className="flex gap-4">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            Continue Quiz
          </button>
          <button
            onClick={handleSubmit}
            className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Submit Quiz
          </button>
        </div>
      </div> */}

export default SubmitQuizModal;
