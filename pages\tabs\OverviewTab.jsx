import { use, useEffect, useState } from "react";

function OverviewTab() {
  const [rewards, setRewards] = useState(0);
  const [totalRewards, setTotalRewards] = useState(0);

  useEffect(() => {
    const userData = localStorage.getItem("user-data");
    setRewards(userData ? JSON.parse(userData).coins : null);
    setTotalRewards(userData ? JSON.parse(userData).totalCoins : null);
  }, []);
  console.log(rewards);
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-blue-600">
          Your Rewards Balance
        </h2>

        <div className="flex items-center gap-4 mt-4">
          <div className="bg-blue-600 rounded-full p-4">
            <svg
              className="w-8 h-8 text-white"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="12"
                cy="12"
                r="9"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M8 12H16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M12 8L12 16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>

          <div>
            <p className="text-gray-600">Current Balance</p>
            <p className="text-3xl font-bold text-blue-600">
              {rewards} <span className="text-2xl">coins</span>
            </p>
          </div>
        </div>

        <div className="mt-6">
          <div className="h-2 bg-gray-100 rounded-full">
            <div className="h-2 bg-blue-600 rounded-full w-[35%]"></div>
          </div>
        </div>

        <p className="text-gray-600 mt-4">
          You've earned {totalRewards} coins in total
        </p>
      </div>

      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h2 className="text-lg font-semibold text-blue-600">How It Works</h2>

        <p className="text-gray-700 mt-2">
          Earn coins through your learning journey and redeem them for exciting
          rewards!
        </p>

        <div className="mt-6 space-y-4">
          <div className="flex items-center gap-4">
            <div className="bg-green-100 rounded-full p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-green-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <p className="text-gray-700">Complete assignments and tests</p>
          </div>

          <div className="flex items-center gap-4">
            <div className="bg-blue-100 rounded-full p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
            </div>
            <p className="text-gray-700">Attend lectures and tutorials</p>
          </div>

          <div className="flex items-center gap-4">
            <div className="bg-purple-100 rounded-full p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-purple-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <p className="text-gray-700">Consistent daily learning streaks</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OverviewTab;
