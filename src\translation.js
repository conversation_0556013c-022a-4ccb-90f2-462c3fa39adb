// translations.js
const translations = {
  English: {
    welcome: "Welcome",
    about: "About Us",
    contact: "Contact Us",
  },
  Hindi: {
    welcome: "स्वागत हे",
    about: "हमारे बारे में",
    contact: "संपर्क करें",
  },
  // Add all other languages
};

export const getTranslation = (key, lang = "English") => {
  const langTranslations = translations[lang] || translations["English"];
  return langTranslations[key] || key; // Fallback to key if translation not found
};
