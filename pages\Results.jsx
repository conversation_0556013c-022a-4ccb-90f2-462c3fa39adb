import React, { useState,useEffect } from 'react';
const selected=[
  'mr.<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
  '<EMAIL>',
  '<EMAIL>',
  'anura<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
  'born<PERSON><PERSON><PERSON><PERSON>@gmail.com',
  'yuv<PERSON><PERSON><PERSON><PERSON><EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  'a<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
]
let socketdata="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
let socketdata1=socketdata.split(",").map((email) => email.trim());

const ScreenPage = () => {
  const [email, setEmail] = useState("");
  const [isSelected, setIsSelected] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);

  useEffect(() => {
    const userData = localStorage.getItem("user-data");
    const storedEmail = JSON.parse(userData)?.email?.trim();

    if (!storedEmail) {
      alert("Please login first");
      window.location.href = "/";
    } else {
      setEmail(storedEmail);
    }
  }, []);

  useEffect(() => {
    if (!email) return;

    const registered = socketdata1.includes(email);
    const selectedCandidate = selected.includes(email);

    setIsRegistered(registered);
    setIsSelected(registered && selectedCandidate);
  }, [email, socketdata1, selected]);

  const renderContent = () => {
    if (isRegistered && isSelected) {
      return (
        <>
          <h1 className="text-green-600 text-xl font-bold">Congratulations!</h1>
          <p>You have advanced for the Interview round. We'll contact you soon with further details.</p>
        </>
      );
    }

    if (isRegistered) {
        return (
            <div className="bg-white shadow-md rounded-xl p-6 max-w-xl mx-auto text-center space-y-4">
              <h1 className="text-yellow-600 text-2xl font-bold">We appreciate your interest!</h1>
              <p className="text-gray-700 text-base">
            After careful consideration, we regret to inform you that you have not been shortlisted for the interview round this time.
              </p>
              <div className="border-t border-gray-200 pt-4">
            <h2 className="text-lg font-semibold text-gray-800">Note:</h2>
            <p className="text-gray-600">
              If you have an interest in UI/UX design, we are currently exploring opportunities in that area. Kindly share your previous UI/UX projects by emailing us at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-blue-600 underline hover:text-blue-800"
              >
                <EMAIL>
              </a>{" "}
              for further consideration.
            </p>
              </div>
            </div>
          );
          
    }

    return (
      <>
        <h1 className="text-red-600 text-xl font-semibold">Access Denied</h1>
        <p>You have not registered for this exam.</p>
      </>
    );
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 p-6 text-center">
      <div className="bg-white rounded-2xl shadow-md p-8 max-w-md w-full">
        {renderContent()}
      </div>
    </div>
  );
};

export default ScreenPage;
