
import { Link } from "react-router-dom"
import { useLanguage, languages } from "../context/LanguageContext"
import { useTranslation } from "../hooks/useTranslation"
import navlogo from "./navlogo.png"
export default function NavBar() {
  const { language, toggleLanguage } = useLanguage()
  const { t } = useTranslation()

  return (
    <header className="border-b border-gray-100 py-4">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <Link to="/" className="flex items-center">
          <img src={navlogo} alt="AI Classroom Logo" className="h-10 w-auto" />
          <span className="ml-2 font-bold text-xl">AI Classroom</span>
        </Link>

      

        {/* Mobile menu button - would expand to show mobile menu */}
        <button className="md:hidden">
          <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </header>
  )
}