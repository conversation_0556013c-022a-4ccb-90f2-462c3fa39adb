import { useState, useEffect, useRef } from "react";
import { useLocation,useNavigate } from "react-router-dom"
const returnPage = () => {
  const location = useLocation();
  let {title,data} = location.state || {}
  const derivekeypoints = async(dater)=>{
    const response = await fetch("https://api.abiv.in",{
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message:dater,
        prompt:"Generate key points from the text in paragraph format",
      }),
    })
    const data2 = await response.json();
    return data2.output_text;
  }
  useEffect(() => {
data.map((item,index) => {
  derivekeypoints(item.original).then((res) => {
    const data2 = res;
    console.log(data);
    data[index].keypoints = data2;
  })
},[data])
    console.log(data)}, [data]);
  return(
    <>
    {data.map((item, index) => (
      <LessonPage key={index} title={title} content={item} />
    ))}
    </>
  );
}
const LessonPage = ({title,content}) => {
    return (
      <div className="max-w-6xl mx-auto p-8 bg-white rounded-lg shadow-sm">
        <div className="flex items-start justify-between mb-8 pb-4 border-b border-gray-200">
          <a
            href="#"
            className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-700"
          >
            <span className="text-xl">←</span>
          </a>
          <div className="flex-1 mx-4">
            <h1 className="text-2xl font-semibold text-gray-800 mb-1">
              {title}
            </h1>
            <p className="text-sm text-gray-500">Last updated: {new Date().toLocaleDateString()}</p>
          </div>
        
        </div>
  {content.map((item, index) => (

  
        <div key={index} className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Left Content Section */}
          <div className="flex-1 border border-blue-100 rounded-lg p-8">
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b border-dashed border-blue-100">
                {item.title}
              </h2>
              <p className="mb-4 leading-relaxed">
                {item.original}
              </p>
            </div>
  
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b border-dashed border-blue-100">
                Key Concepts
              </h2>
              <ul className="space-y-3">
                {item.keypoints?.split('.').map((concept, index) => {
                  if(index==item.original.split('.').length-1){
                    return null;
                  }
                 return (

                  <li key={index} className="flex items-start">
                    <span className="text-indigo-600 font-bold mr-2">•</span>
                    <div>
                      <strong>{concept.trim()}</strong>
                    </div>
                  </li>
                )})}
               
              </ul>
            </div>
  
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b border-dashed border-blue-100">
                Applications of ML
              </h2>
              <p className="mb-4 leading-relaxed">
                Machine learning is powering many of the services we use today:
              </p>
  
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-md border-l-4 border-indigo-600">
                  <h3 className="font-semibold mb-2">Recommendation Systems</h3>
                  <p className="text-sm text-gray-600">
                    Used by Netflix, Amazon, Spotify
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-md border-l-4 border-indigo-600">
                  <h3 className="font-semibold mb-2">Image Recognition</h3>
                  <p className="text-sm text-gray-600">
                    Facebook photo tagging, medical imaging
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-md border-l-4 border-indigo-600">
                  <h3 className="font-semibold mb-2">
                    Natural Language Processing
                  </h3>
                  <p className="text-sm text-gray-600">
                    Chatbots, translation services
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-md border-l-4 border-indigo-600">
                  <h3 className="font-semibold mb-2">Predictive Analytics</h3>
                  <p className="text-sm text-gray-600">
                    Stock market, weather forecasting
                  </p>
                </div>
              </div>
            </div>
          </div>
  
          {/* Right Image Section */}
          <div className="w-full lg:w-[400px] flex flex-col gap-8">
            <div className="text-center">
              <img
                src={item.chunkimage}
                alt="Machine learning process flow diagram"
                className="w-full rounded-lg mb-2"
              />
              <p className="text-xs text-gray-500">
                Fig 1.1 - Machine learning process flow diagram showing data
                input, model training, and prediction output.
              </p>
            </div>
  
            <div className="text-center">
              <img
                src={item.titleimage}
                alt="Types of machine learning"
                className="w-full rounded-lg mb-2"
              />
              <p className="text-xs text-gray-500">
                Fig 1.2 - The three main types of machine learning: supervised,
                unsupervised, and reinforcement learning.
              </p>
            </div>
          </div>
        </div>
  ))}
        <div className="flex justify-end">
          <button className="bg-gray-100 text-gray-800 px-4 py-2 rounded flex items-center gap-2 text-sm hover:bg-gray-200">
            <span className="text-lg">↓</span>
            Download
          </button>
        </div>
      </div>
    );
  };
  
  export default returnPage;