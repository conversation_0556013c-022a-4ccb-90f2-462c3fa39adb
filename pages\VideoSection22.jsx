import { useState, useEffect } from 'react';
import { IoMdClose } from "react-icons/io";
import { FaCloudDownloadAlt } from "react-icons/fa";
import { IoSend } from "react-icons/io5";
import { IoMdDownload } from "react-icons/io";
import { MdDownloadForOffline } from "react-icons/md";
import { FaMicrophone } from "react-icons/fa";
import axios from 'axios';
import { jsPDF } from 'jspdf';

// Mock data to use if API fetch fails
const mockData = {
    lesson: {
        day: 'Day 1',
        date: 'Monday, June 5',
        courseTitle: 'Introduction to Quantum Mechanics',
        lessonTitle: 'Lesson 3: Wave-Particle Duality',
        videoUrl: '',
        isMarkedDone: false
    },
    notes: {
        keyConcepts: [
            'Particles can exhibit wave-like properties and waves can exhibit particle-like properties',
            'The de Broglie wavelength reveals a particle\'s momentum in its wavelength',
            'The double-slit experiment demonstrates quantum superposition',
            '<PERSON><PERSON><PERSON>\'s uncertainty principle limits simultaneous knowledge of position and momentum'
        ],
        summary: 'Wave-particle duality is a fundamental concept in quantum mechanics that states every particle or quantum entity may be described as either a particle or a wave. This challenges classical concepts where particles and waves were distinct phenomena. The concept emerged from observations that light could behave as both waves (interference patterns) and particles (photons) and was later extended to all matter.'
    },
    transcript: [
        { timestamp: '[00:00]', text: 'Welcome to today\'s lesson on wave-particle duality...' },
        { timestamp: '[00:30]', text: 'Let\'s start by understanding what we mean by particles and waves...' },
        { timestamp: '[01:15]', text: 'The famous double-slit experiment demonstrates this phenomenon...' }
    ],
    resources: [
        { title: 'Interactive Double-Slit Experiment Simulation', url: '#' },
        { title: 'Quantum Mechanics Textbook - Chapter 3', url: '#' },
        { title: 'Practice Problems: Wave-Particle Duality', url: '#' }
    ],
    chat: [
        {
            id: 1,
            type: 'assistant',
            content: "Hello! I'm your learning assistant! I can help you with questions, answer your questions, or provide examples. Just ask me!"
        }
    ]
};

const VideoSection = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [activeTab, setActiveTab] = useState('notes');
    const [isMarkedDone, setIsMarkedDone] = useState(false);
    const [newMessage, setNewMessage] = useState('');
    const [isMessageLoading, setIsMessageLoading] = useState(false);

    // Data states
    const [lessonData, setLessonData] = useState(mockData.lesson);
    const [notesData, setNotesData] = useState(mockData.notes);
    const [transcriptData, setTranscriptData] = useState(mockData.transcript);
    const [resourcesData, setResourcesData] = useState(mockData.resources);
    const [chatMessages, setChatMessages] = useState(mockData.chat);

    const downloadChatAsPDF = () => {
        const doc = new jsPDF();
        const pageWidth = doc.internal.pageSize.getWidth();
        const margin = 14;
        const maxWidth = pageWidth - (2 * margin);

        // Add title
        doc.setFontSize(16);
        doc.text('Chat Conversation', 105, 15, { align: 'center' });

        // Add lesson info
        doc.setFontSize(12);
        doc.text(`Lesson: ${lessonData.courseTitle} - ${lessonData.lessonTitle}`, margin, 25);
        doc.text(`Date: ${new Date().toLocaleDateString()}`, margin, 32);

        doc.setFontSize(12);
        let yPosition = 45; // Starting position for chat messages

        // Function to split text into multiple lines
        const splitText = (text, maxWidth) => {
            return doc.splitTextToSize(text, maxWidth);
        };

        // Filter out the first default AI message
        const filteredMessages = chatMessages.filter((message, index) => index !== 0);

        // Add each chat message with proper formatting
        filteredMessages.forEach((message) => {
            if (message.content.trim() === '') return;

            const prefix = message.type === 'assistant' ? 'AI: ' : 'YOU: ';
            const fullText = prefix + message.content;
            const lines = splitText(fullText, maxWidth);

            // Set font style (bold for YOU, normal for AI)
            doc.setFont('helvetica', message.type === 'user' ? 'bold' : 'normal');

            // Add each line of text
            lines.forEach((line) => {
                if (yPosition > 270) { // Check if we need a new page
                    doc.addPage();
                    yPosition = 20;
                }
                doc.text(line, margin, yPosition);
                yPosition += 7; // Line height
            });

            // Add spacing after each message
            if (message.type === 'user') {
                // One line gap after user messages
                yPosition += 7;
            } else {
                // Two line gaps after AI messages
                yPosition += 14;
            }
        });

        // Save the PDF
        doc.save(`Chat_Conversation_${new Date().toISOString().slice(0, 10)}.pdf`);
    };
    // Fetch data from API
    useEffect(() => {
        const fetchYoutubeData = async () => {
            const wbStrId = '683641792727f31f3fb45915';
            const topicId = '3349a81d-b149-45cb-b7ca-41134020f999';
            const url = `http://localhost:8000/api/v1/videoData/generateVideoLink/${wbStrId}/${topicId}`;
            try {
                const response = await axios.get(url);
                console.log('Youtube Data:', response.data);

                if (response.data && response.data.data && response.data.data.videoList && response.data.data.videoList.length > 0) {
                    console.log('First video URL:', response.data.data.videoList[0].videoURL);
                    // Update lessonData with the first video URL
                    setLessonData(prev => ({
                        ...prev,
                        videoUrl: response.data.data.videoList[0].videoURL,
                        courseTitle: response.data.data.videoList[0].title,
                    }));

                    setNotesData(prev => ({
                        ...prev,
                        summary: response.data.data.videoList[0].description
                    }));
                } else {
                    console.log('No video data found in the response');
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        const fetchData = async () => {
            setIsLoading(true);
            try {
                // Fetch lesson data
                const lessonResponse = await fetch('api/lesson');
                const lessonResult = await lessonResponse.json();

                // Fetch notes data
                const notesResponse = await fetch('api/notes');
                const notesResult = await notesResponse.json();

                // Fetch transcript data
                const transcriptResponse = await fetch('api/transcript');
                const transcriptResult = await transcriptResponse.json();

                // Fetch resources data
                const resourcesResponse = await fetch('api/resources');
                const resourcesResult = await resourcesResponse.json();

                // Fetch chat data
                const chatResponse = await fetch('api/chat');
                const chatResult = await chatResponse.json();

                // Update state with fetched data
                setLessonData(lessonResult);
                setNotesData(notesResult);
                setTranscriptData(transcriptResult);
                setResourcesData(resourcesResult);
                setChatMessages(chatResult);
                setIsMarkedDone(lessonResult.isMarkedDone || false);
            } catch (err) {
                console.error('Error fetching data:', err);
                // Use mock data instead of showing error
                setLessonData(mockData.lesson);
                setNotesData(mockData.notes);
                setTranscriptData(mockData.transcript);
                setResourcesData(mockData.resources);
                setChatMessages(mockData.chat);
                setIsMarkedDone(mockData.lesson.isMarkedDone);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
        fetchYoutubeData();
    }, []);

    const handleSendMessage = async () => {
        if (newMessage.trim()) {
            // Add user message to chat
            const newUserMessage = {
                id: chatMessages.length + 1,
                type: 'user',
                content: newMessage
            };

            const updatedMessages = [...chatMessages, newUserMessage];
            setChatMessages(updatedMessages);
            setNewMessage('');
            setIsMessageLoading(true);

            try {
                // Prepare conversation history with the counselor prompt and lesson context
                const messagesForApi = [
                    {
                        role: "system",
                        content: `You are a kind and supportive academic counselor for students.

                        Your role:
                        - Listen with empathy and validate emotions.
                        - Give clear, caring, and helpful responses.
                        - Use a friendly and conversational tone, not robotic or formal.
                        - Comfort stressed students and guide confused ones.
                        - If unsure what they mean, ask a kind follow-up.
                        - Always reply in not more than 50 words.
                        - Only when asked "Who are you?", reply: "I'm an AI assistant developed by RnPsoft Private Limited to help students academically and emotionally."
                        - Never mention OpenAI, ChatGPT, or any other AI provider.
                        
                        Current lesson: ${lessonData?.courseTitle || 'N/A'} - ${lessonData?.lessonTitle || 'N/A'}
                        Key concepts: ${notesData?.keyConcepts?.join(', ') || 'Not specified'}`
                    },
                    // Map all previous messages with correct roles
                    ...updatedMessages.map(msg => ({
                        role: msg.type === 'user' ? 'user' : 'assistant',
                        content: msg.content
                    }))
                ];

                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ********************************************************************************************************************************************************************`
                    },
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        messages: messagesForApi,
                        temperature: 0.7
                    }),
                });

                const data = await response.json();
                if (data.choices && data.choices[0].message) {
                    const assistantMessage = {
                        id: updatedMessages.length + 1,
                        type: 'assistant',
                        content: data.choices[0].message.content
                    };
                    setChatMessages([...updatedMessages, assistantMessage]);
                }
            } catch (error) {
                console.error('Error:', error);
                // Add error message to chat
                const errorMessage = {
                    id: updatedMessages.length + 1,
                    type: 'assistant',
                    content: "Sorry, I'm having trouble responding right now. Please try again later."
                };
                setChatMessages([...updatedMessages, errorMessage]);
            } finally {
                setIsMessageLoading(false);
            }
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            handleSendMessage();
        }
    };

    const handleMarkAsDone = async () => {
        const newStatus = !isMarkedDone;
        setIsMarkedDone(newStatus);

        try {
            // Update status on the server
            await fetch('api/lesson/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ isMarkedDone: newStatus }),
            });
        } catch (err) {
            console.error('Error updating lesson status:', err);
            // Silently fail - the UI is already updated
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
                <div className="text-xl text-gray-600">Loading lesson data...</div>
            </div>
        );
    }

    const translator = (word1, word2) =>
        localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
            ? word1
            : localStorage.getItem("lang")
                ? word2
                : word1;

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            {error && (
                <div className="max-w-7xl mx-auto mb-4">
                    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                            <div className="ml-3">
                                <p className="text-sm text-yellow-700">
                                    {error}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/*header*/}
            <div className="max-w-7xl mx-auto mb-4">
                <div className="p-4 border-b- border-gray-200">
                    <div className="flex justify-center">
                        <div className="ml-3 w-full">
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-4">
                                    <h1 className="text-2xl font-bold text-gray-900">{translator("Day", "दिन")} {lessonData.day}</h1>
                                    <span className="text-gray-600">{lessonData.date}</span>
                                </div>
                                <div className="flex items-center justify-center">
                                    <button className="p-2 hover:bg-gray-100 rounded-lg">
                                        <IoMdClose />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content Area */}
                    <div className="lg:col-span-2">
                        {/* Header Card */}
                        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-6">
                            <div className="p-4">
                                {/* Course Title */}
                                <h2 className="text-xl font-semibold text-gray-900 mb-2">{lessonData.courseTitle}</h2>

                                {/* Lesson Title with Mark as Done Toggle */}
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg text-gray-700">{lessonData.lessonTitle}</h3>

                                    <div className="flex items-center gap-2">
                                        <button
                                            onClick={handleMarkAsDone}
                                            className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-300 ease-in-out"
                                            style={{ backgroundColor: isMarkedDone ? '#3b82f6' : '#d1d5db' }}
                                            aria-pressed={isMarkedDone}
                                            aria-labelledby="mark-as-done-label"
                                        >
                                            <span
                                                className={`inline-block h-5 w-5 transform rounded-full bg-white shadow transition-transform duration-300 ease-in-out ${isMarkedDone ? 'translate-x-5' : 'translate-x-1'
                                                    }`}
                                            />
                                        </button>
                                        <span id="mark-as-done-label" className="text-sm font-medium text-gray-700">{!isMarkedDone ? translator("Mark as Done", "डॉन करें") : translator("Mark as Not Done", "डॉन नहीं करें")}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Video Container */}
                            <div className="bg-black rounded-lg mx-4 mb-4 overflow-hidden">
                                <div className="relative" style={{ paddingTop: "56.25%" }}>
                                    <iframe
                                        className="absolute top-0 left-0 w-full h-full"
                                        src={`https://www.youtube.com/embed/${lessonData.videoUrl?.split('v=')[1] || ''}`}
                                        title={lessonData.lessonTitle}
                                        frameBorder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                        allowFullScreen
                                    ></iframe>
                                </div>
                            </div>

                            {/* Download Notes Button */}
                            <div className="flex justify-end px-4 mb-4">
                                <button className="flex items-center gap-2 text-blue-600 hover:text-blue-950">
                                    <IoMdDownload />
                                    <span className="font-medium">{translator("Download Notes", "नोट्स डाउनलोड करें")}</span>
                                </button>
                            </div>

                            {/* Tabs and Content */}
                            <div className="border-t border-gray-200">
                                {/* Tab Navigation */}
                                <div className="flex border-b border-gray-200">
                                    <button
                                        onClick={() => setActiveTab('notes')}
                                        className={`px-6 py-3 text-sm font-medium ${activeTab === 'notes'
                                            ? 'bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] text-white rounded-t-lg'
                                            : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        Notes
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('resources')}
                                        className={`px-6 py-3 text-sm font-medium ${activeTab === 'resources'
                                            ? 'bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] text-white rounded-t-lg'
                                            : 'text-gray-600 hover:text-gray-900'
                                            }`}
                                    >
                                        Resources
                                    </button>
                                </div>

                                {/* Tab Content */}
                                <div className="p-6">
                                    {activeTab === 'notes' && (
                                        <div>
                                            <h4 className="text-lg font-semibold text-gray-900 mb-4">{translator("Key Concepts", "कुंजी अवधारणाएं")}</h4>
                                            <ul className="list-disc pl-5 space-y-2 text-gray-700">
                                                {notesData.keyConcepts.map((concept, index) => (
                                                    <li key={index}>{concept}</li>
                                                ))}
                                            </ul>

                                            <h4 className="text-lg font-semibold text-gray-900 mt-6 mb-4">{translator("Summary", "सारांश")}</h4>
                                            <p className="text-gray-700">
                                                {notesData.summary}
                                            </p>
                                        </div>
                                    )}

                                    {activeTab === 'resources' && (
                                        <div>
                                            <h4 className="text-lg font-semibold text-gray-900 mb-4">{translator("Additional Resources", "अतिरिक्त संसाधन")}</h4>
                                            <ul className="space-y-3">
                                                {resourcesData.map((resource, index) => (
                                                    <li key={index}>
                                                        <a href={resource.url} className="text-blue-600 hover:text-blue-700 underline">
                                                            {resource.title}
                                                        </a>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Sidebar - Ask Your Doubts */}
                    <div className="lg:col-span-1">
                        <div className="bg-white rounded-xl shadow-md overflow-hidden h-[calc(100vh-8rem)] flex flex-col sticky top-6">
                            {/* Chat Header */}
                            <div className="flex justify-between items-center bg-gradient-to-r from-[#6BA0FF] to-[#755BFF] p-4 flex-shrink-0">
                                <h3 className="text-lg font-semibold text-white">{translator("Ask Your Doubts", "अपने सवाल पूछें")}</h3>
                                <button onClick={downloadChatAsPDF}>
                                    <MdDownloadForOffline className="text-white h-8 w-8 cursor-pointer hover:opacity-80" />
                                </button>
                            </div>

                            {/* Chat Messages */}
                            <div className="p-4 flex-grow overflow-y-auto">
                                <div className="space-y-4">
                                    {chatMessages.map((message) => (
                                        <div
                                            key={message.id}
                                            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                                        >
                                            <div
                                                className={`max-w-xs p-3 rounded-lg ${message.type === 'user'
                                                    ? 'bg-[#E0E7FF] text-gray-900'
                                                    : 'bg-gray-100 text-gray-900'
                                                    }`}
                                            >
                                                <p className="text-sm">{message.content}</p>
                                            </div>
                                        </div>
                                    ))}
                                    {isMessageLoading && (
                                        <div className="flex justify-start">
                                            <div className="max-w-xs p-3 rounded-lg bg-gray-100 text-gray-900">
                                                <div className="flex space-x-2">
                                                    <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"></div>
                                                    <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                                    <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                            {/* Chat Input */}
                            <div className="border-t border-gray-200 p-4 flex-shrink-0 bg-white">
                                <div className="flex gap-2">
                                    <button className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-[#A723FF] to-[#1A39FF] rounded-full">
                                        <FaMicrophone className="text-white h-5 w-5" />
                                    </button>
                                    <input
                                        type="text"
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        onKeyDown={handleKeyDown}
                                        placeholder={translator("Ask a question...", "एक सवाल पूछें...")}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        disabled={isMessageLoading}
                                    />
                                    <button
                                        onClick={handleSendMessage}
                                        className={`w-10 h-10 flex items-center justify-center bg-gradient-to-r from-[#E9D5FF] to-[#BFDBFE] text-gray-700 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${isMessageLoading ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90'}`}
                                        disabled={isMessageLoading}
                                    >
                                        <IoSend />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VideoSection;