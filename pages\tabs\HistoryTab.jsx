import { useState, useEffect } from "react";
import CoinBadge from "../CoinBadge";

function HistoryTab() {
  const [email, setEmail] = useState(null);
  const [historyItems, setHistoryItems] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const userData = localStorage.getItem("user-data");
    setEmail(userData ? JSON.parse(userData).email : null);
  }, []);

  useEffect(() => {
    if (!email) return;

    setIsLoading(true);

    fetch(
      `https://api.aiclassroom.in/redeem-history?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    )
      .then((response) => response.json())
      .then((data) => {
        if (data.success === false) {
          console.error("Error fetching history:", data.errors);
          setHistoryItems([]);
        } else {
          setHistoryItems(data.redeemHistory || []);
        }
      })
      .catch((error) => console.error("Fetch error:", error))
      .finally(() => setIsLoading(false));
  }, [email]);

  return (
    <div className="space-y-4">
      {isLoading ? (
        <p className="text-gray-500">Loading...</p>
      ) : historyItems.length > 0 ? (
        historyItems.map((item) => (
          <div
            key={item.id}
            className="bg-white rounded-lg p-4 shadow-sm flex items-center justify-between"
          >
            <div className="flex items-center gap-4">
              <div className="bg-blue-100 rounded-full p-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{item.title}</h3>
                <p className="text-sm text-gray-500">Redeemed on {item.date}</p>
              </div>
            </div>
            <CoinBadge amount={item.coin} />
          </div>
        ))
      ) : (
        <p className="text-gray-500">No redemption history found.</p>
      )}
    </div>
  );
}

export default HistoryTab;
