import { <PERSON> } from "react-router-dom";
import NavBar from "./Navbar1";
import Footer from "./Footer";
import { useTranslation } from "../hooks/useTranslation";
import { Swiper, SwiperSlide } from "swiper/react";
import HomeImage from "./mainscreen.png";
import Pdfslides from "./pdfslider.png"
import imager from "./image 101.png"
import {
  Navigation,
  Pagination,
  Autoplay,
  EffectCoverflow,
} from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/effect-coverflow";
import doubtSolving from "./image 100.png"
import material from "./image 102.png"
import lastimg from "./image 105.png"

const features = [
  {
    title: "Access to Materials",
    description:
      "Access curated notes, videos, and resources aligned with your syllabus.",
    icon: imager,
  },
  {
    title: "Multi-Device Seamless Sync",
    description:
      "Learn on-the-go with full sync between mobile, tablet, and desktop platforms.",
    icon: material,
  },
  {
    title: "AI POWERED VIDEO GENERATION",
    description:
      "Convert any PDF or notes into engaging, AI-generated explainer videos in seconds.",
    icon: Pdfslides,
  },
  {
    title: "LIVE DOUBT SOLVING WITH AI AND MENTORS",
    description:
      "Real-time chat support from AI tutors and human mentors for instant problem-solving.",
    icon: doubtSolving,
  },
  {
    title: "Personalized Dashboard",
    description:
      "Track your progress and manage upcoming tasks with an intuitive planner.",
    icon: lastimg,
  },
];

export default function LandingPage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white">
      <NavBar />

      <main className="container mx-auto px-4 py-12 md:py-20">
        <div className="flex flex-col lg:flex-row items-center justify-between mb-32">
          <div className="lg:w-1/2 mb-10 lg:mb-0">
            <h2 className="text-xl text-gray-800 font-medium mb-2">
              {t("landing.smart")}
            </h2>
            <h1 className="text-6xl font-bold text-purple-900 mb-4">
              {t("landing.classrooms")}
            </h1>
            <h2 className="text-3xl text-gray-800 font-medium mb-6">
              {t("landing.smart")}{" "}
              <span className="text-purple-900 font-semibold">Futures</span>
            </h2>
            <p className="text-gray-700 mb-8 max-w-lg">
              {t("landing.description")}
            </p>
            <Link
              to="/auth"
              className="inline-block bg-purple-900 text-white font-medium py-3 px-8 rounded-md hover:bg-purple-800 transition-colors"
            >
              {t("landing.getStarted")}
            </Link>
          </div>

          <div className="lg:w-1/2 relative">
            <div className="relative z-10">
              <img
                src={HomeImage}
                alt="AI Classroom Illustration"
                className="w-full h-auto"
              />
            </div>

            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-16 h-16 bg-purple-100 rounded-full opacity-50"></div>
            <div className="absolute bottom-20 left-10 w-12 h-12 bg-blue-100 rounded-full opacity-50"></div>
            <div className="absolute top-40 left-0 w-8 h-8 bg-yellow-100 rounded-full opacity-50"></div>
          </div>
        </div>

        {/* Features Section with Swiper */}
        <div className="py-16">
          <h2 className="text-4xl font-bold text-center mb-4">
            Experience The Ease Of Teaching With{" "}
            <span className="text-purple-900">Ai Classroom</span>
          </h2>
          <div className="mt-16">
            <style>
              {`
                .swiper-slide {
                  transition: all 0.3s ease;
                  padding: 20px 10px;
                }
                .swiper-slide-active {
                  transform: scale(1.03);
                  z-index: 1;
                }
                .swiper-slide-active .feature-card {
                  background-color: rgb(147, 101, 216);
                  color: white;
                }
                .swiper-slide-active .feature-card h3 {
                  color: white;
                }
                .swiper-slide-active .feature-card p {
                  color: #E5E7EB;
                }
                .swiper-slide-active .feature-icon {
                  background-color: rgba(255, 255, 255, 0.1);
                  padding: 12px;
                  border-radius: 12px;
                }
                .feature-card {
                  transition: all 0.3s ease;
                }
              `}
            </style>
            <Swiper
              modules={[Navigation, Pagination, Autoplay, EffectCoverflow]}
              spaceBetween={30}
              slidesPerView={1}
              centeredSlides={true}
              loop={true}
              navigation
              pagination={{ clickable: true }}
              autoplay={{ delay: 3000 }}
              effect="coverflow"
              coverflowEffect={{
                rotate: 0,
                stretch: 0,
                depth: 100,
                modifier: 1,
                slideShadows: false,
              }}
              breakpoints={{
                640: {
                  slidesPerView: 2,
                },
                1024: {
                  slidesPerView: 3,
                },
              }}
              className="py-8 px-12"
            >
              {features.map((feature, index) => (
                <SwiperSlide key={index}>
                  <div className="feature-card bg-white rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 min-h-[320px] flex flex-col items-center">
                    <div className="feature-icon w-14 h-14 mb-5 mx-auto flex items-center justify-center">
                      <img
                        src={feature.icon}
                        alt={feature.title}
                        className="w-10 h-10 object-contain"
                      />
                    </div>
                    <h3 className="text-lg font-semibold text-purple-900 mb-3 text-center">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 text-center text-sm flex-grow">
                      {feature.description}
                    </p>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}