import express from "express";
import cors from "cors";
import jwt from "jsonwebtoken";
import mongoose from "mongoose";
import multer from "multer";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import twilio from "twilio";
import { MongoClient } from "mongodb";
import axios from "axios";
import * as sdk from "microsoft-cognitiveservices-speech-sdk";
import OpenAIApi from "openai";
import PDFDocument from "pdfkit";
import userRoutes from "./userRoutes.js";
import pdfRoutes from "./pdfRoutes.js";
import users from "./UserModel.js";
// Removed unused import
import * as dotenv from "dotenv";
import crypto from "crypto";
import { Cashfree } from "cashfree-pg";
dotenv.config();
// console.log(process.env.CLIENT_ID);
// console.log(process.env.CLIENT_SECRET);
Cashfree.XClientId = process.env.CLIENT_ID;
Cashfree.XClientSecret = process.env.CLIENT_SECRET;
Cashfree.XEnvironment = Cashfree.Environment.PRODUCTION;

function generateOrderId() {
  const uniqueId = crypto.randomBytes(16).toString("hex");

  const hash = crypto.createHash("sha256");
  hash.update(uniqueId);

  const orderId = hash.digest("hex");

  return orderId.substring(0, 12);
}

let b;
fs.readFile("imageprompt.txt", "utf8", (_, data) => {
  b = data;
});
let c;
fs.readFile("data.txt", "utf8", (_, data) => {
  c = data;
});
const __dirname = path.dirname(fileURLToPath(import.meta.url));
// Removed unused variable
const upload = multer({ dest: "uploads/" });
const uri = "mongodb uri";
const dbName = "AI Classroom";
const collectionName = "email";
let client1 = null;
const app = express();
const accountSid = "AC no access"; // Your Twilio Account SID
const authToken = "no access"; // Your Twilio Auth Token
const client = twilio(accountSid, authToken);
let data = [[], [], [], []];
let shuntqueue = [[], []];
let interview = [[], []];
let doubt = [[], []];
let x = 0;
function generateRandomString(length) {
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }
  return result;
}

app.use(cors());

app.use(express.json());
app.use(
  express.urlencoded({
    extended: true,
  })
);
const port = 80;
// mongoose.connect("no access");
const MONGO_URL = "mongodb://127.0.0.1:27017/AI Classroom20";
mongoose
  .connect(MONGO_URL)
  .then(() => console.log("Connected to MongoDB with db name 'AI Classroom20'"))
  .catch((err) => console.error("Error connecting to MongoDB:", err));

function addPauses(text, delimiter, pauseDuration) {
  let p = "";
  for (let i = 0; i < text.split(delimiter).length; i++) {
    if (i != text.split(delimiter).length - 1)
      p =
        p +
        text.split(delimiter)[i] +
        `<break time="${pauseDuration}"/>` +
        delimiter;
  }
  p = p + text.split(delimiter)[text.split(delimiter).length - 1];
  return p;
}
app.use((req, res, next) => {
  console.log("Request received at:", req.url);
  console.log("Date Time:", new Date().toLocaleString());
  next();
});

app.use("/", userRoutes);
app.use("/pdfUpload", pdfRoutes);

app.post("/doubtassing", (req, res) => {
  let data1 = req.body.data;

  if (data[0].length > data[1].length) {
    data1.push(1);
    doubt[1].push(data1);
  } else {
    data1.push(1);
    doubt[0].push(data1);
  }
  res.json({ doubt: data1 });
});
app.post("/isdoubt", (req, res) => {
  let c = req.body.flag;
  if (doubt[c].length > 0) {
    res.json({ doubt: 1, data: doubt[c][0] });
  }
  res.json({ doubt: 0 });
});
app.post("/doubtresolve", (req, res) => {
  let c = req.body.flag;
  if (doubt[c] && doubt[c].length > 0) {
    doubt[c].shift();
    res.json({ message: "resolved" });
  } else {
    res.json({ message: "no doubts to resolve" });
  }
});

app.post("/coupons", async (req, res) => {
  const { couponCode } = req.body;
  fs.readFile("coupons.json", "utf8", (err, data) => {
    if (err) {
      console.error("Error reading file:", err);
      return res.status(500).send("Error reading file.");
    }
    let coupons = JSON.parse(data);
    let available = coupons.available;
    let coupon = "";
    for (let i = 0; i < available.length; i++) {
      if (available[i] == couponCode) {
        coupon = available[i];
        available[i] = "";
        break;
      }
    }
    if (coupon.length === 0) {
      return res.status(404).json({ message: 0 });
    } else {
      let used = coupons.used;
      used.push(coupon);
      coupons = { available, used };
      fs.writeFile("coupons.json", JSON.stringify(coupons), (err) => {
        if (err) {
          console.error("Error writing file:", err);
          return res.status(500).send("Error writing file.");
        }
      });
      res.json({ message: 1 });
    }
  });
}); //curl -X POST -H "Content-Type: application/json" -d '{"couponCode":"ABCD"}' https://api.AI Classroom.in/coupons

const addBreaks = (text) => {
  text = addPauses(text, ".", "1000ms");
  text = addPauses(text, "\n", "1000ms");
  text = addPauses(text, "plus", "50ms");
  text = addPauses(text, "minus", "50ms");
  text = addPauses(text, "multiplied", "50ms");
  text = addPauses(text, "whole", "100ms");
  text = addPauses(text, "equals", "100ms");
  text = addPauses(text, "divided", "50ms");
  text = addPauses(text, "root", "50ms");
  text = addPauses(text, "square", "50ms");
  text = addPauses(text, "cube", "50ms");
  return text;
};

app.post("/terminat", (req, res) => {
  let sessionid = req.body.sessionid;
  let temp = false;
  for (let i = 0; i < 2; i++) {
    for (let j = 0; j < data[i].length; j++) {
      if (data[i][j][13] == sessionid) {
        console.log(data[i][j]);
        data[i][j][0] = 0;
        temp = true;
        data[i + 2][j][0] = 0;
        break;
      }
    }
    if (temp) break;
  }
  res.status(200).send({ message: "data updated" });
});
app.post("/pulloff", (req, res) => {
  let r = req.body.sessionid;
  console.log("i am called");
  console.log(r);
  for (let i = 0; i < 2; i++) {
    for (let j = 0; j < data[i].length; j++) {
      if (data[i][j][13] == r) {
        let t = data[i][j];
        data[i].splice(j, 1);
        console.log("SPliced off data to");
        console.log(data);
        shuntqueue[0].push(t);
      }
    }
  }
  res.status(200).send({ message: "data updated" });
});
app.post("/pulloff1", (req, res) => {
  let r = req.body.sessionid;
  for (let i = 2; i < 4; i++) {
    for (let j = 0; j < data[i].length; j++) {
      if (data[i][j][9] == r) {
        let t = data[i][j];
        data[i].splice(j, 1);
        shuntqueue[1].push(t);
      }
    }
  }
  res.status(200).send({ message: "data updated" });
});
app.post("/activate", (req, res) => {
  let r = Number(req.body.serverdetails);
  data[r + 2][0][0] = 1;
  data[r + 2][0][2] = 1;
  data[r + 2][0][3] = req.body.summary;
  data[r + 2][0][4] = req.body.language;
  console.log(data[r + 2]);
  console.log(r);
  res.json({ message: "Summary done" });
});
app.post("/tts", (req, res) => {
  const { text } = req.body; // Extract text from the request body
  tts(text, req.body.filename); // Call the TTS function with the text
  console.log("Mp3 audio generated");
  res.send("Audio generated"); // You might want to send a more descriptive response
});

app.post("/add", async (req, res) => {
  try {
    if (!client1) {
      client1 = new MongoClient(uri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      await client1.connect();
    }

    const database = client1.db(dbName);
    const collection = database.collection(collectionName);

    const newItem = req.body;
    const email = Object.keys(newItem)[0]; // Assuming the email is the key of the object

    const updateResult = await collection.updateOne(
      { [email]: { $exists: true } },
      { $set: { [email]: newItem[email] } },
      { upsert: true }
    );

    if (updateResult.upsertedCount > 0) {
      res
        .status(201)
        .send({ message: "Item inserted", id: updateResult.upsertedId });
    } else if (updateResult.modifiedCount > 0) {
      res.status(200).send({ message: "Item updated" });
    } else {
      res.status(200).send({ message: "No changes made" });
    }
  } catch (err) {
    console.error("An error occurred:", err);
    res.status(500).send("An error occurred");
  }
});
app.get("/find/:email", async (req, res) => {
  try {
    if (!client1) {
      client1 = new MongoClient(uri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      await client1.connect();
    }

    const database = client1.db(dbName);
    const collection = database.collection(collectionName);

    const email = req.params.email;
    console.log(`Searching for email: ${email}`);
    const query = { [email]: { $exists: true } };
    const item = await collection.findOne(query);

    if (item) {
      console.log("Item found:", item);
      res.status(200).send({ message: "Item found", item });
    } else {
      console.log("Item not found");
      res.status(404).send({ message: "Item not found" });
    }
  } catch (err) {
    console.error("An error occurred:", err);
    res.status(500).send("An error occurred");
  }
});

app.post("/pop-out", (req, res) => {
  data[Number(req.headers["x-shift-id"])].shift();
  data.json({
    message: "received",
    receivedData: data,
  });
});
app.post("/send-otp", async (req, res) => {
  const { phoneNumber } = req.body;
  console.log(phoneNumber);
  if (!phoneNumber) {
    return res.status(400).json({ error: "Phone number is required" });
  }

  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  try {
    const message = await client.messages.create({
      body: `Your OTP code is ${otp}`,
      from: "+15017984093", // Replace with your Twilio Phone Number
      to: phoneNumber,
    });
    res.json({ message: "OTP sent", otp });
  } catch (error) {
    console.error("Error sending OTP:", error);
    res.status(500).json({ error: error.message });
  }
});
app.delete("/delete-file", (req, res) => {
  const { filename } = req.body;

  if (!filename) {
    return res.status(400).send("Filename is required");
  }

  const filePath = path.join(__dirname, "uploads", filename);

  fs.unlink(filePath, (err) => {
    if (err) {
      console.error("Error deleting file:", err);
      return res.status(500).send("Error deleting file");
    }

    res.send("File deleted successfully");
  });
});
app.post("/userdata", (req, res) => {
  res.json({
    message: "received",
    receivedData: require("./data.json"),
  });
});
app.get("/check-availability/:filename", (req, res) => {
  const { filename } = req.params;
  const filePath = path.join(__dirname, "uploads", filename);

  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      res.status(404).json({ available: false });
    } else {
      res.status(200).json({ available: true });
    }
  });
});

app.post("/upload-video", upload.single("video"), (req, res) => {
  if (!req.file) {
    return res.status(400).send("No video uploaded.");
  }

  const file = req.file;
  const tempPath = file.path;
  const targetPath = path.join(__dirname, "uploads", file.originalname);

  // Move the uploaded file to the target path
  fs.rename(tempPath, targetPath, (err) => {
    if (err) {
      console.error("Error moving file:", err);
      return res.status(500).send("Error uploading video.");
    }

    console.log("File uploaded successfully:", file.originalname);
    res.send("Video uploaded successfully.");
  });
});
app.get("/downloads/uploads/:filename", (req, res) => {
  const { filename } = req.params;
  const filePath = path.join(__dirname, "uploads", filename);

  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      res.status(404).send("File not found");
      return;
    }

    const stat = fs.statSync(filePath);
    const fileSize = stat.size;
    const range = req.headers.range;

    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;

      if (start >= fileSize) {
        res
          .status(416)
          .send(
            "Requested range not satisfiable\n" + start + " >= " + fileSize
          );
        return;
      }

      const chunksize = end - start + 1;
      const file = fs.createReadStream(filePath, { start, end });
      const head = {
        "Content-Range": `bytes ${start}-${end}/${fileSize}`,
        "Accept-Ranges": "bytes",
        "Content-Length": chunksize,
        "Content-Type": "video/mp4",
      };

      res.writeHead(206, head);
      file.pipe(res);
    } else {
      const head = {
        "Content-Length": fileSize,
        "Content-Type": "video/mp4",
      };
      res.writeHead(200, head);
      fs.createReadStream(filePath).pipe(res);
    }
  });
});
app.post("/submit", (req, res) => {
  let c = req.body.sessionid;
  console.log("I have been called here");
  console.log("request body accepted");
  let temp = false;
  for (let i = 0; i < 2; i++) {
    for (let j = 0; j < data[i].length; j++) {
      if (data[i][j][13] == c) {
        data[i][j] = req.body.data;
        temp = true;
        break;
      }
    }
    if (temp) break;
  }

  if (Array.isArray(data) && data.every((row) => Array.isArray(row))) {
    console.log("Received 2D array:", data);

    // Send a response
    res.json({
      message: "2D array received successfully",
      receivedData: data,
    });
  } else {
    res.json({
      message: "Invalid input, expected a two-dimensional array",
    });
  }
});
app.post("/doubtposting", (req, res) => {
  let c = req.body.sessionid;
  for (let i = 0; i < 2; i++) {
    for (let j = 0; j < data[i].length; j++) {
      if (data[i][j][13] == c) {
        data[i][j][10] = 1;
        data[i][j][11] = req.body.transcript;
        break;
      }
    }
  }
});
app.post("/submit1", (req, res) => {
  console.log(req.body);
  let p = [];
  console.log(req.headers); // Logs all headers received
  let c = req.body.sessionid;

  console.log(c);
  let temp = false;
  for (let i = 0; i < 2; i++) {
    for (let j = 0; j < data[i].length; j++) {
      if (data[i][j][13] == c) {
        console.log(data[i][j]);
        p = data[i][j];
        temp = true;
        break;
      }
    }
    if (temp) break;
  }
  for (let i = 0; i < shuntqueue[0].length; i++) {
    if (shuntqueue[0][i][13] == c) {
      console.log(shuntqueue[0][i]);
      p = shuntqueue[0][i];
      temp = true;
      break;
    }
  }
  // Read the current value of 'x' from data.json
  res.json({
    message: "received",
    receivedData: p,
  });
});
app.post("/submit6", (req, res) => {
  res.json({
    message: "received",
    receivedData:
      data[1].length > 0
        ? data[1][0]
        : [
            0,
            0,
            0,
            "",
            "",
            0,
            0,
            [],
            0,
            [],
            0,
            "",
            [],
            "",
            [],
            0,
            {},
            0,
            0,
            "",
          ],
  });
}); //curl -X POST -H "Content-Type: application/json" -d '{"sessionid":"1234"}' https://api.aiclassroom.in/submit6
app.post("/submit2", (req, res) => {
  // Read the current value of 'x' from data.json
  res.json({
    message: "received",
    receivedData:
      data[0].length > 0
        ? data[0][0]
        : [
            0,
            0,
            0,
            "",
            "",
            0,
            0,
            [],
            0,
            [],
            0,
            "",
            [],
            "",
            [],
            0,
            {},
            0,
            0,
            "",
          ],
  });
});
app.post("/submit7", (req, res) => {
  data[1][0] = req.body;
  res.json({
    message: "received",
    receivedData: data[1][0],
  });
});
app.post("/submit3", (req, res) => {
  data[0][0] = req.body;
  res.json({
    message: "received",
    receivedData: data[0][0],
  });
});

app.post("/submit4", (req, res) => {
  res.json({
    message: "recieved",
    receivedData:
      data[2].length > 0 ? data[2][0] : [0, 0, 0, "", "", 0, 0, [], 0, ""],
  });
});
app.post("/submit41", (req, res) => {
  res.json({
    message: "received",
    receivedData:
      data[3].length > 0 ? data[3][0] : [0, 0, 0, "", "", 0, 0, [], 0, ""],
  });
});
app.post("/submit8", (req, res) => {
  res.json({
    message: "received",
    receivedData:
      data[3].length > 0 ? data[3][0] : [0, 0, 0, "", "", 0, 0, [], 0, ""],
  });
});
app.post("/submit5", (req, res) => {
  data[2][0] = req.body;
  res.json({
    message: "received",
    receivedData: data[2][0],
  });
});
app.post("/submit51", (req, res) => {
  data[3][0] = req.body;
  res.json({
    message: "received",
    receivedData: data[3][0],
  });
});
app.post("/submit9", (req, res) => {
  data[3][0] = req.body;
  res.json({
    message: "received",
    receivedData: data[3][0],
  });
});
app.post("/interview", (req, res) => {
  res.json({
    message: "received",
    receivedData: interview,
  });
});
app.post("/interview1", (req, res) => {
  interview = req.body;
  if (
    Array.isArray(interview) &&
    data.every((row) => Array.isArray(interview))
  ) {
    console.log("Received 2D array:", data);

    // Send a response
    res.json({
      message: "2D array received successfully",
      receivedData: data,
    });
  } else {
    res.status(400).json({
      message: "Invalid input, expected a two-dimensional array",
    });
  }
});
app.post("/interview22", (req, res) => {
  let filename = req.body.filename;
  let data = req.body.data;
  console.log(filename);
  fs.writeFile(`uploads/${filename}`, data, (err) => {
    if (err) {
      console.error("Error writing file:", err);
      return res.status(500).send("Error writing file.");
    }
    res.send("File uploaded successfully");
  });
});
app.post("/check-file", (req, res) => {
  const { filename } = req.body;
  console.log(filename);
  if (!filename) {
    return res.status(400).send("Filename is required");
  }
  const filePath = path.join(__dirname, "uploads", filename);
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      return res.status(404).json({ message: 0 });
    }
    fs.readFile(filePath, "utf8", (err, data) => {
      if (err) {
        console.error("Error reading file:", err);
        return res.status(500).send("Error reading file");
      }
      res.status(200).json({ message: 1, content: data });
    });
  });
});
app.get("/download/:filename", (req, res) => {
  const { filename } = req.params;
  const filePath = `uploads/${filename}`; // Assuming files are stored in the 'uploads' directory
  const file = fs.createReadStream(filePath);

  file.on("error", (err) => {
    res.status(404).send("File not found");
  });

  file.pipe(res);
});

// Stream audio directly to the client
app.get("/stream/:filename", (req, res) => {
  const { filename } = req.params;
  const filePath = path.join(__dirname, "uploads", filename);
  const fileStream = fs.createReadStream(filePath);
  fileStream.on("error", (err) => {
    console.error(err);
    res.status(404).send("File not found");
  });
  // Set appropriate headers for audio streaming
  res.setHeader("Content-Type", "audio/wav"); // Change this if you're using a different audio format
  res.setHeader("Accept-Ranges", "bytes");

  fileStream.pipe(res);
});
app.post("/textupload", (req, res) => {
  console.log(req.body);
  const file = req.body.file;
  const uploadsDir = path.join(__dirname, "uploads");
  const pdfPath = path.join(uploadsDir, "textvideo.pdf");
  const doc = new PDFDocument();

  // Pipe the PDF into a file
  doc.pipe(fs.createWriteStream(pdfPath));

  // Add text to the PDF
  doc.fontSize(12).text(file, {
    align: "left",
  });

  // Finalize the PDF and end the stream
  doc.end();
  let flag = data[0].length >= data[1].length ? 0 : 1;
  let trial = [
    1,
    0,
    1,
    "",
    "",
    0,
    0,
    [],
    0,
    [],
    0,
    "",
    [],
    "",
    [],
    0,
    {},
    0,
    0,
    "",
    flag + 1,
  ];
  trial[0] = 1;
  trial[2] = 1;
  trial[3] = "textvideo.pdf";
  trial[4] = req.body.text;
  trial[13] = req.body.sessionid;
  trial[19] = req.body.email;
  data[flag].push(trial);
  let trial1 = [1, 0, 0, "", "", 1, 0, [], 0, "", flag + 1];
  trial1[9] = req.body.sessionid;
  data[flag + 2].push(trial1);
  let randomString = generateRandomString();
  res.status(200).send(String(flag));
});
app.post("/upload", upload.single("file"), (req, res) => {
  console.log(req.body.text);

  if (!req.file) {
    return res.status(400).send("No file uploaded.");
  }
  const file = req.file;
  const filePath = `${file.destination}${file.filename}`;
  if (req.body.sessionid.includes("interview")) {
    interview[0][0] = 1;
    interview[0][1] = file.originalname;
    fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error("Error reading file:", err);
        return res.status(500).send("Error reading file.");
      }

      // Write the file without encoding
      fs.writeFile(`uploads/${file.originalname}`, data, (err) => {
        if (err) {
          console.error("Error writing file:", err);
          return res.status(500).send("Error writing file.");
        }
      });
    });
  } else if (req.body.sessionid.includes("question")) {
    if (req.body.pattern.includes("false")) {
      let flagger = [0, "", "", "", ""];
      flagger[0] = 1;
      flagger[1] = file.originalname;
      flagger[4] = req.body.language;
      interview[1].push(flagger);
    } else {
      let flagger = [0, "", [], ""];
      flagger[0] = 1;
      flagger[1] = file.originalname;
      interview[0].push(flagger);
    }
    fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error("Error reading file:", err);
        return res.status(500).send("Error reading file.");
      }
      fs.writeFile(`uploads/${file.originalname}`, data, (err) => {
        if (err) {
          console.error("Error writing file:", err);
          return res.status(500).send("Error writing file.");
        }
      });
    });
  } else {
    let flag = data[0].length <= data[1].length ? 0 : 1;
    let trial = [
      1,
      0,
      1,
      "",
      "",
      0,
      0,
      [],
      0,
      [],
      0,
      "",
      [],
      "",
      [],
      0,
      {},
      0,
      0,
      "",
    ];
    trial[0] = 1;
    trial[2] = 1;
    trial[19] = req.body.email;
    trial[3] = file.originalname;
    console.log(req.body);
    trial[4] = req.body.text;
    trial[13] = req.body.sessionid;
    data[flag].push(trial);
    let trial1 = [0, 0, 0, "", "", 1, 0, [], 0, ""];
    trial1[9] = req.body.sessionid;
    data[flag + 2].push(trial1);
    console.log(data);
    // Read the file
    fs.readFile(filePath, (err, data) => {
      if (err) {
        console.error("Error reading file:", err);
        return res.status(500).send("Error reading file.");
      }

      // Write the file without encoding
      fs.writeFile(`uploads/${file.originalname}`, data, (err) => {
        if (err) {
          console.error("Error writing file:", err);
          return res.status(500).send("Error writing file.");
        }
        res.status(200).send(String(flag));
        console.log("File saved successfully");
      });
    });
  }
});
// const users = mongoose.model("users", {
//   email: {
//     type: String,
//     unique: true,
//   },
//   password: {
//     type: String,
//     default: 'no_access'
//   },
//   coins: {
//     type: Number,
//     default: 5
//   },
//   watchtime: {
//     type: Number,
//     default: 0
//   },
//   videos: {
//     type: Object,
//     default: {}
//   },
//   avatar: {
//     type: String,
//     default: "https://t3.ftcdn.net/jpg/05/70/71/06/360_F_570710660_Jana1ujcJyQTiT2rIzvfmyXzXamVcby8.jpg"
//   },
//   firstName: {
//     type: String,
//     default: 'New'
//   },
//   lastName: {
//     type: String,
//     default: 'User'
//   },
//   accounttype: {
//     type: String,
//     default: 'student'
//   },
//   examhistory: {
//     type: Array,
//     default: []
//   },
//   interview: {
//     type: Array,
//     default: []
//   },
//   numericals: {
//     type: Array,
//     default: []
//   },
//   gender: {
//     type: String,
//     default: ''
//   },
//   hindiopted: {
//     type: Number,
//     default: 0
//   },
//   englishopted: {
//     type: Number,
//     default: 0
//   },
//   hinglishopted: {
//     type: Number,
//     default: 0
//   },
//   age: {
//     type: Number,
//     default: 0
//   },
//   date: {
//     type: Date,
//     default: Date.now,
//   },
//   signinwithgoogle: {
//     type: Boolean,
//     default: false
//   },
//   contacts_filled: {
//     tpye: Object,
//     default: {}
//   }
// })
const central_scehma = mongoose.model("central_scehma", {
  topics_uploaded: {
    type: Array,
    default: [],
  },
  total_bill_spent: {
    type: Number,
    default: 0,
  },
  total_user_count: {
    type: Number,
    default: 0,
  },
  hindi_count: {
    type: Number,
    default: 0,
  },
  english_count: {
    type: Number,
    default: 0,
  },
  hinglish_count: {
    type: Number,
    default: 0,
  },
  texttovid_count: {
    type: Number,
    default: 0,
  },
  exams_appeared: {
    type: Number,
    default: 0,
  },
  interviews_appeared: {
    type: Number,
    default: 0,
  },
  numericals_solved: {
    type: Number,
    default: 0,
  },
  main_server_uptime: {
    type: Number,
    default: 0,
  },
  gpu_status: {
    type: Object,
    default: {},
  },
  male_count: {
    type: Number,
    default: 0,
  },
  female_count: {
    type: Number,
    default: 0,
  },
  chat_with_bot: {
    type: Array,
    default: [],
  },
});

//creating endpoint for registing user

app.post("/signup", async (req, res) => {
  console.log("receiving data");
  let b = req.body;

  let check = await users.findOne({ email: b.email });
  if (check) {
    return res
      .status(400)
      .json({
        success: false,
        error: "existing user found with same email id",
      });
  }
  const user = new users(b);
  await users.collection.insertOne(user);

  const data = {
    user: {
      id: user.id,
    },
  };

  const token = jwt.sign(data, "secret_ecom");
  res.json({ success: true, token });
});

//user login
app.post("/login", async (req, res) => {
  console.log("\n\nreceiving data");
  // console.log('req.body,', req.body);
  let user = await users.findOne({ email: req.body.email });
  // console.log('user: ',user);
  if (user) {
    const passCompare = req.body.password === user.password;
    if (passCompare || (user.signinwithgoogle && req.body.signinwithgoogle)) {
      const data = {
        user: {
          id: user.id,
        },
      };
      const token = jwt.sign(data, "secret_ecom");
      console.log("send: ", token);
      res.json({ success: true, token, signup: false, email: req.body.email });
    } else {
      if (req.body.signinwithgoogle) {
        const b = req.body;
        const user = new users(b);
        await users.collection.insertOne(user);
        const data = {
          user: {
            id: user.id,
          },
        };

        const token = jwt.sign(data, "secret_ecom");
        res.json({ success: true, token, signup: true });
      } else {
        res.json({ success: false, errors: "wrong password", signup: false });
      }
    }
  } else {
    res.json({ success: false, errors: "wrong email id" });
  }
});
app.post("/updatedb", async (req, res) => {
  try {
    const datam = req.body;

    // Validate incoming data
    if (!datam || !datam.email) {
      return res
        .status(400)
        .json({ success: false, error: "Email is required" });
    }

    // Check if user exists
    const check = await users.findOne({ email: datam.email });
    if (!check) {
      return res
        .status(400)
        .json({
          success: false,
          error: "No user found with the provided email id",
        });
    }

    // Update user details
    const myquery = { email: datam.email };
    const newvalues = { $set: datam }; // Use $set to update fields

    const result = await users.updateOne(myquery, newvalues);

    if (result.modifiedCount === 0) {
      return res
        .status(400)
        .json({
          success: false,
          error: "No changes were made to the document",
        });
    }

    res.json({ success: true, message: "User updated successfully" });
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ success: false, error: "An internal server error occurred" });
  }
});
app.post("/addcoins", async (req, res) => {
  try {
    let email = req.body.email;
    let coins = req.body.coins;
    let check = await users.findOne({ email: email });
    if (!check) {
      return res
        .status(400)
        .json({
          success: false,
          error: "existing user found with same email id",
        });
    }
    check.coins = check.coins + coins;
    const myquery = { email: email };
    const newvalues = { $set: check };
    const result = await users.updateOne(myquery, newvalues);
    if (result.modifiedCount === 0) {
      return res
        .status(400)
        .json({
          success: false,
          error: "No changes were made to the document",
        });
    }
    res.json({ success: true, message: "coins updated successfully" });
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ success: false, error: "An internal server error occurred" });
  }
}); // curl -X POST -H "Content-Type: application/json" -d '{"email":"<EMAIL>","coins":100}' https://api.AI Classroom.in/addcoins
app.post("/subcoins", async (req, res) => {
  try {
    let email = req.body.email;
    let coins = req.body.coins;
    let check = await users.findOne({ email: email });
    if (!check) {
      return res
        .status(400)
        .json({
          success: false,
          error: "existing user found with same email id",
        });
    }
    check.coins = check.coins - coins;
    const myquery = { email: email };
    const newvalues = { $set: check };
    const result = await users.updateOne(myquery, newvalues);
    if (result.modifiedCount === 0) {
      return res
        .status(400)
        .json({
          success: false,
          error: "No changes were made to the document",
        });
    }
    res.json({ success: true, message: "coins updated successfully" });
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ success: false, error: "An internal server error occurred" });
  }
});
app.post("/addwatchtime", async (req, res) => {
  try {
    let email = req.body.email;
    let watchtime = req.body.watchtime;
    let check = await users.findOne({ email: email });
    if (!check) {
      return res
        .status(400)
        .json({
          success: false,
          error: "existing user found with same email id",
        });
    }
    check.watchtime = check.watchtime + watchtime;
    const myquery = { email: email };
    const newvalues = { $set: check };
    const result = await users.updateOne(myquery, newvalues);
    if (result.modifiedCount === 0) {
      return res
        .status(400)
        .json({
          success: false,
          error: "No changes were made to the document",
        });
    }
    res.json({ success: true, message: "watchtime updated successfully" });
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ success: false, error: "An internal server error occurred" });
  }
});
app.post("/addresults", async (req, res) => {
  try {
    let email = req.body.email;
    let results = req.body.results;
    let check = await users.findOne({ email: email });
    if (!check) {
      return res
        .status(400)
        .json({
          success: false,
          error: "existing user found with same email id",
        });
    }
    check.examhistory.push(results);
    const myquery = { email: email };
    const newvalues = { $set: check };
    const result = await users.updateOne(myquery, newvalues);
    if (result.modifiedCount === 0) {
      return res
        .status(400)
        .json({
          success: false,
          error: "No changes were made to the document",
        });
    }
    res.json({ success: true, message: "results updated successfully" });
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ success: false, error: "An internal server error occurred" });
  }
});

app.post("/fetchdata", async (req, res) => {
  const { email } = req.body;
  let check = await users.findOne({ email: email });
  if (!check) {
    return res
      .status(400)
      .json({
        success: false,
        error: "existing user found with same email id",
      });
  }
  res.json({ success: true, data: check });
});
app.post("/updatemain", async (req, res) => {
  const datam = req.body;
  const myquery = { email: datam.email };
  const newvalues = datam;
  central_scehma.update(myquery, newvalues, function (err, res) {
    if (err) throw err;
    console.log("1 document updated");
  });
  res.json({ success: true });
}); // curl -X POST -H "Content-Type: application/json" -d '{"email":""} http://localhost:80/fetchdata

//Api creation
app.listen(3000, () => {
  console.log("server is running on " + 3000);
});
