// English translations
export const en = {
    // NavBar
    nav: {
      pricing: "Pricing",
      individual: "Individual",
      contactUs: "Contact Us",
    },
  
    // Landing Page
    landing: {
      smart: "Smart",
      classrooms: "Classrooms",
      smartFutures: "Smart Futures",
      description:
        "AI Classroom is a smart, AI-powered learning platform that turns notes into videos, solves doubts in real-time, tracks exams, and connects students and teachers seamlessly—all in one clean, modern dashboard.",
      getStarted: "Get Started",
    },
  
    // Auth Page
    auth: {
      title: "Access the Power of AI Learning",
      subtitle: "Log in to your smart classroom of tomorrow.",
      signUp: "Sign up",
      logIn: "Log in",
      emailAddress: "Email address",
      password: "Password",
      hide: "Hide",
      show: "Show",
      forgotPassword: "Forgot your password?",
      continueWithGoogle: "Continue with Google",
      continueWithFacebook: "Continue with Facebook",
      signUpWithEmail: "Sign up with email",
      termsAgreement: "By signing up, you agree to the",
      termsOfService: "Terms of Service",
      privacyPolicy: "Privacy Policy",
      andAcknowledge: "and acknowledge you've read our",
    },
  
    // Footer
    footer: {
      about: "About",
      helpCenter: "Help Center",
      termsOfService: "Terms of Service",
      privacyPolicy: "Privacy Policy",
      cookiePolicy: "Cookie Policy",
      careers: "Careers",
    },
  }
  
  // Hindi translations
  export const hi = {
    // NavBar
    nav: {
      pricing: "मूल्य निर्धारण",
      individual: "व्यक्तिगत",
      contactUs: "संपर्क करें",
    },
  
    // Landing Page
    landing: {
      smart: "स्मार्ट",
      classrooms: "कक्षाएं",
      smartFutures: "स्मार्ट भविष्य",
      description:
        "AI कक्षा एक स्मार्ट, AI-संचालित लर्निंग प्लेटफॉर्म है जो नोट्स को वीडियो में बदलता है, रीयल-टाइम में संदेहों को हल करता है, परीक्षाओं को ट्रैक करता है, और छात्रों और शिक्षकों को एक साफ, आधुनिक डैशबोर्ड में निर्बाध रूप से जोड़ता है।",
      getStarted: "शुरू करें",
    },
  
    // Auth Page
    auth: {
      title: "AI लर्निंग की शक्ति का उपयोग करें",
      subtitle: "कल की अपनी स्मार्ट कक्षा में लॉग इन करें।",
      signUp: "साइन अप करें",
      logIn: "लॉग इन करें",
      emailAddress: "ईमेल पता",
      password: "पासवर्ड",
      hide: "छिपाएं",
      show: "दिखाएं",
      forgotPassword: "पासवर्ड भूल गए?",
      continueWithGoogle: "Google के साथ जारी रखें",
      continueWithFacebook: "Facebook के साथ जारी रखें",
      signUpWithEmail: "ईमेल से साइन अप करें",
      termsAgreement: "साइन अप करके, आप",
      termsOfService: "सेवा की शर्तें",
      privacyPolicy: "गोपनीयता नीति",
      andAcknowledge: "से सहमत हैं और स्वीकार करते हैं कि आपने हमारी",
    },
  
    // Footer
    footer: {
      about: "हमारे बारे में",
      helpCenter: "सहायता केंद्र",
      termsOfService: "सेवा की शर्तें",
      privacyPolicy: "गोपनीयता नीति",
      cookiePolicy: "कुकी नीति",
      careers: "करियर",
    },
  }
  
  // Function to get translations based on language
  export function getTranslations(language) {
    return language === "Hindi" ? hi : en
  }
  