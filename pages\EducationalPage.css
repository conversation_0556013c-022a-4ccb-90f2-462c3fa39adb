.container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 16px;
}

/* Header Styles */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #000;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.greeting {
  font-size: 16px;
}

.user-name {
  font-weight: bold;
}

.user-class {
  font-size: 14px;
  color: #3b82f6;
}

.profile-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #6d28d9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

/* Main Content Styles */
.main-content {
  margin-top: 16px;
  background-color: white;
  border-radius: 12px;
  border: 1px solid #f1f1f1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 32px;
  min-height: 500px;
}

.title {
  font-size: 28px;
  color: #4b5563;
  text-align: center;
  font-weight: 500;
  margin-top: 64px;
  margin-bottom: 80px;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32px;
}

.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 320px;
}

.loading-text {
  color: #6d28d9;
  font-size: 20px;
  font-weight: 500;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: #f1f1f1;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, #8b5cf6, #3b82f6);
  border-radius: 9999px;
  transition: width 0.3s ease-out;
}

.content {
  padding: 16px;
}

@media (min-width: 768px) {
  .title {
    font-size: 32px;
  }
}
