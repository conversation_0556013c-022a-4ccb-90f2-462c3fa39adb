import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { UploadCloud, FileEdit } from "lucide-react";
import { useUploadPdf } from "./useUploadPdf";

export default function ExamCreationPage() {
  const [pdfFile, setPdfFile] = useState(null);
  const [imageslist, setImageslist] = useState(null);
  const [selectedTime, setSelectedTime] = useState("");
  const [numberquestion, setNumberquestion] = useState(1);
  const [difficulty, setDifficulty] = useState("");
  const [mode, setMode] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const { uploadPdf, fileUrl } = useUploadPdf();
  const randomId = Math.random().toString(36).substr(2, 5);
  const nav = useNavigate();

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (file && file.type === "application/pdf") {
      try {
        await uploadPdf(file);
        setPdfFile(file);
      } catch {
        alert("Failed to upload");
      }
    } else {
      alert("Please upload a valid PDF file.");
    }
  };

  useEffect(() => {
    const findData = async () => {
      if (!fileUrl) return;
      const res = await fetch("https://py.aiclassroom.in/process/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          file_uid: randomId,
          file_url: fileUrl,
        }),
      });

      if (res.ok) {
        const json = await res.json();
        setImageslist(json.Pdf_Pages_Data);
      }
    };
    findData();
  }, [fileUrl]);

  const handleStartExam = async () => {
    if (!pdfFile || !selectedTime || !difficulty || !mode) {
      alert("Please complete all fields before submitting.");
      return;
    }

    setIsLoading(true); // start loader

    try {
      const ocrRes = await fetch("https://py.aiclassroom.in/imgs2ocr/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          file_url_list: imageslist.map((x) => {
            const url = x.split("/").pop();
            return `files/pdf_pages/${url}`;
          }),
        }),
      });

      if (ocrRes.ok) {
        const ocr = await ocrRes.json();
        const ocrdata = ocr["OCR DATA"];

        const chunks = [];
        const p = Math.floor(ocrdata.length / numberquestion);
        for (let i = 0; i < ocrdata.length; i += p) {
          chunks.push(ocrdata.slice(i, i + p));
        }
        if (chunks.length > numberquestion) chunks.pop();

        const questions = [];
        for (let i = 0; i < chunks.length; i++) {
          const response = await fetch("https://api.abiv.in/prompt", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              message: chunks[i],
              prompt: `Return output strictly as raw JSON ONLY — do NOT include markdown syntax (like \`\`\`json), titles, or explanations.
Generate a multiple-choice question suitable for the "${difficulty}" level based on this content.
Format:
{
  "Question": "Your full question here",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct": "a" // indicating correct option (a, b, c, or d)
}`,
            }),
          });

          const data = await response.json();
          const parsed = JSON.parse(data.output_text);
          questions.push(parsed);
        }

        nav("/mcq-exam-quiz", {
          state: { questions, type: "mcq", time: selectedTime },
        });
      }
    } catch (err) {
      alert("Something went wrong during exam generation.");
      console.error(err);
    } finally {
      setIsLoading(false); // stop loader
    }
  };

  const times = ["10 mins", "30 mins", "60 mins"];
  const levels = ["Easy", "Medium", "Hard"];
  const modes = ["MCQ", "Answer Sheet"];

  return (
    <div className="min-h-screen bg-white px-6 py-10">
      <div className="max-w-6xl mx-auto space-y-8">
        <h1 className="text-4xl font-bold text-gray-800 flex items-center gap-3">
          <FileEdit className="w-8 h-8 text-blue-600" />
          Create Exam
        </h1>

        <div className="grid md:grid-cols-2 gap-8">
          {/* PDF Upload */}
          <div className="relative border border-white/20 backdrop-blur-xl bg-white/40 shadow-xl rounded-2xl p-6">
            {!fileUrl ? (
              <label
                htmlFor="pdf-upload"
                className="flex flex-col items-center justify-center h-80 text-center cursor-pointer rounded-xl border-2 border-dashed border-gray-300 hover:border-blue-400 transition"
              >
                <UploadCloud className="w-12 h-12 text-blue-600" />
                <p className="text-lg text-gray-800 font-medium">Upload PDF</p>
                <p className="text-sm text-gray-500">Click to select a file</p>
                <input
                  id="pdf-upload"
                  type="file"
                  accept="application/pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </label>
            ) : (
              <iframe
                src={fileUrl}
                title="PDF Preview"
                className="w-full h-[500px] rounded-xl border border-gray-300"
              />
            )}
          </div>

          {/* Form Controls */}
          <div className="flex flex-col justify-between gap-6">
            {/* Time */}
            <div className="bg-white/50 border backdrop-blur-xl border-white/20 shadow-xl rounded-2xl p-6">
              <label className="block text-gray-700 font-semibold mb-2">Time Duration</label>
              <div className="flex flex-wrap gap-3">
                {times.map((time) => (
                  <button
                    key={time}
                    onClick={() => setSelectedTime(time)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition border ${
                      selectedTime === time
                        ? "bg-blue-600 text-white border-blue-600"
                        : "bg-white text-gray-800 border-gray-200 hover:bg-blue-100"
                    }`}
                  >
                    {time}
                  </button>
                ))}
              </div>
            </div>

            {/* Difficulty & Question Count */}
            <div className="bg-white/50 border backdrop-blur-xl border-white/20 shadow-xl rounded-2xl p-6 flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <label className="block text-gray-700 font-semibold mb-2">Difficulty</label>
                <div className="flex flex-wrap gap-3">
                  {levels.map((level) => (
                    <button
                      key={level}
                      onClick={() => setDifficulty(level)}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition border ${
                        difficulty === level
                          ? "bg-blue-600 text-white border-blue-600"
                          : "bg-white text-gray-800 border-gray-200 hover:bg-blue-100"
                      }`}
                    >
                      {level}
                    </button>
                  ))}
                </div>
              </div>
              <div className="flex-1 flex flex-col gap-2">
                <label className="block text-gray-700 font-semibold mb-2">Number of Questions</label>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={() => setNumberquestion((n) => Math.max(1, n - 1))}
                    className="px-3 py-1 rounded-full bg-gray-200 hover:bg-gray-300 text-lg font-bold"
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min={1}
                    value={numberquestion}
                    onChange={(e) =>
                      setNumberquestion(Math.max(1, parseInt(e.target.value) || 1))
                    }
                    className="w-16 text-center border border-gray-200 rounded-lg p-2"
                  />
                  <button
                    type="button"
                    onClick={() => setNumberquestion((n) => n + 1)}
                    className="px-3 py-1 rounded-full bg-gray-200 hover:bg-gray-300 text-lg font-bold"
                  >
                    +
                  </button>
                </div>
              </div>
            </div>

            {/* Mode and Submit */}
            <div className="bg-white/50 border backdrop-blur-xl border-white/20 shadow-xl rounded-2xl p-6 flex flex-col md:flex-row justify-between items-center gap-6">
              <div className="flex gap-4 flex-wrap">
                {modes.map((m) => {
                  const isComingSoon = m.includes("Answer");
                  return (
                    <button
                      key={m}
                      onClick={() => !isComingSoon && setMode(m)}
                      disabled={isComingSoon}
                      className={`px-6 py-2 rounded-full font-semibold transition border ${
                        mode === m
                          ? "bg-black text-white border-black"
                          : "bg-white text-gray-800 border-gray-200 hover:bg-gray-100"
                      } ${isComingSoon ? "opacity-50 cursor-not-allowed" : ""}`}
                    >
                      {m} Mode {isComingSoon ? " (Coming Soon)" : ""}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={handleStartExam}
                className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-full shadow-md transition"
                disabled={isLoading}
              >
                {isLoading ? "Generating Exam..." : "Start Exam"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
