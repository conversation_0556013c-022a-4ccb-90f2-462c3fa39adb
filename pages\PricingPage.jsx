"use client";

import { Check } from "lucide-react";
import {
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  Facebook,
  UserCircle,
  Menu,
  X,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
const HeaderLogo = "/logo.png";
const Card = ({ children, className = "" }) => (
  <div className={`border rounded-lg bg-gray-50 overflow-hidden ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children, className = "" }) => (
  <div className={`p-6 border-b ${className}`}>{children}</div>
);

const CardContent = ({ children, className = "" }) => (
  <div className={`p-6 ${className}`}>{children}</div>
);

const CardFooter = ({ children, className = "" }) => (
  <div className={`p-6 border-t bg-gray-50 ${className}`}>{children}</div>
);
export default function PricingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  return (
    <div className="w-full min-h-screen bg-white flex flex-col items-center justify-center p-4 md:p-8">
      {/* Header - Desktop and Mobile */}

      <h1 className="text-3xl md:text-4xl font-bold text-purple-900 mt-[50px] text-center mb-10">
        Find The Perfect Plan For You
      </h1>

      <div className="w-full max-w-5xl grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Standard Plan */}
        <Card className="w-full overflow-hidden bg-gradient-to-b from-[#C4D9FF] to-[#C5BAFF] border-0 shadow-sm">
          <CardHeader className="pb-0">
            <div className="flex items-center justify-between">
              <span className="bg-yellow-400 text-black font-medium px-4 py-1 rounded-full text-sm">
                Standard
              </span>
              <div className="flex items-center gap-1">
                <span className="text-black font-semibold">100 Credits</span>
                <div className="w-4 h-4 rounded-full bg-yellow-400"></div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-end gap-1">
              <span className="text-3xl font-bold">₹249</span>
              <span className="text-gray-600 text-sm pb-1">Per Month</span>
            </div>

            <div className="mt-6">
              <p className="text-sm text-gray-600 mb-4">What's Included ?</p>

              <div className="flex items-start gap-2 mb-3">
                <Check className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                <span className="font-semibold">Popular</span>
              </div>

              <p className="text-sm mt-4">
                Get more value with additional credits and enhanced learning
                access
              </p>
            </div>
          </CardContent>
          <CardFooter className="pt-10">
            <button className="w-full bg-white hover:bg-gray-100 text-purple-900 font-medium rounded-full py-4 px-2">
              Continue <span className="ml-1">→</span>
            </button>
          </CardFooter>
        </Card>

        {/* Premium Plan */}
        <Card className="w-full overflow-hidden bg-gradient-to-b from-[#90FAFA] to-[#AA34FF] border-0 shadow-sm">
          <CardHeader className="pb-0">
            <div className="flex items-center justify-between">
              <span className="bg-yellow-400 text-black font-medium px-4 py-1 rounded-full text-sm">
                Premium
              </span>
              <div className="flex items-center gap-1">
                <span className="text-black font-semibold">210 Credits</span>
                <div className="w-4 h-4 rounded-full bg-yellow-400"></div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-end gap-1">
              <span className="text-3xl font-bold">₹499</span>
              <span className="text-gray-600 text-sm pb-1">Per Month</span>
            </div>

            <div className="mt-6">
              <p className="text-sm text-gray-600 mb-4">What's Included ?</p>

              <div className="flex items-start gap-2 mb-3">
                <Check className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                <span className="font-semibold">Extra 10 Credits</span>
              </div>

              <p className="text-sm mt-4">
                Unlock unlimited potential with the best value, extra credits,
                and limitless access.
              </p>
            </div>
          </CardContent>
          <CardFooter className="pt-10">
            <button className="w-full bg-white hover:bg-gray-100 text-purple-900 font-medium rounded-full py-2 px-4">
              Continue <span className="ml-1">→</span>
            </button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
