import React, { useState } from "react";

const Step3GeneratedPlanJS = ({ planItems, onBack, subjects }) => {
  const [activeTab, setActiveTab] = useState(0);
  // Check if we have subjects with AI responses
  const hasAIResponses =
    subjects && subjects.some((subject) => subject.aiResponse);

  // Filter subjects that have AI responses
  const subjectsWithResponses = subjects
    ? subjects.filter((subject) => subject.aiResponse)
    : [];

  // Format and display the schedule in a user-friendly way
  const renderSchedule = (schedule) => {
    if (!schedule || !Array.isArray(schedule)) return null;

    return (
      <div className="mt-4">
        <h4 className="text-lg font-medium mb-3">Study Schedule</h4>
        <div className="space-y-4">
          {schedule.map((item, idx) => (
            <div
              key={idx}
              className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-500"
            >
              <div className="flex justify-between items-center mb-2">
                <h5 className="font-bold text-lg">Day {item.day}</h5>
              </div>
              <h6 className="text-base font-semibold text-blue-700 mb-1">
                {item.subtopic}
              </h6>
              <p className="text-gray-700">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Process AI response data
  const renderAIResponse = (aiResponse) => {
    if (!aiResponse) return null;

    try {
      // If aiResponse is a string, try to parse it
      const responseData =
        typeof aiResponse === "string" ? JSON.parse(aiResponse) : aiResponse;

      // Extract topic and schedule
      const topic = responseData.topic || "Study Plan";
      const totalDays = responseData.total_days || responseData.totalDays || "";
      const schedule = responseData.schedule || [];

      return (
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-bold text-blue-800">{topic}</h3>
              {totalDays && (
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                  {totalDays} Days
                </span>
              )}
            </div>
          </div>

          {renderSchedule(schedule)}

          {/* If there's no schedule but there's other data, display it as JSON */}
          {(!schedule || !schedule.length) && (
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-medium mb-2">AI Response Data</h4>
              <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(responseData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      );
    } catch (error) {
      // If there was an error parsing the JSON, display the raw response
      console.error("Error parsing AI response:", error);

      // Check if it's a string that looks like JSON but might have formatting issues
      if (
        typeof aiResponse === "string" &&
        aiResponse.includes('"schedule":')
      ) {
        try {
          // Try to extract schedule using regex
          const scheduleMatch = aiResponse.match(/"schedule":\s*(\[.*?\])/s);
          if (scheduleMatch && scheduleMatch[1]) {
            const scheduleJson = JSON.parse(scheduleMatch[1]);
            return (
              <div>
                <h3 className="text-xl font-bold mb-3">Study Plan</h3>
                {renderSchedule(scheduleJson)}
              </div>
            );
          }
        } catch (e) {
          console.error("Failed to extract schedule with regex:", e);
        }
      }

      // Fallback to showing raw JSON
      return (
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h3 className="text-xl font-bold mb-3">Study Plan</h3>
          <p className="text-red-500 mb-2">
            There was an issue formatting the study plan. Showing raw data:
          </p>
          <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto max-h-96">
            {typeof aiResponse === "string"
              ? aiResponse
              : JSON.stringify(aiResponse, null, 2)}
          </pre>
        </div>
      );
    }
  };

  // Handle specific field format coming from the example
  const extractScheduleFromJsonField = (aiResponse) => {
    // Check if there's a json_schedule_final field
    if (aiResponse && aiResponse.json_schedule_final) {
      try {
        // If it's a string, parse it
        if (typeof aiResponse.json_schedule_final === "string") {
          return JSON.parse(aiResponse.json_schedule_final);
        }
        // If it's already an object, return it
        return aiResponse.json_schedule_final;
      } catch (error) {
        console.error("Error parsing json_schedule_final:", error);
      }
    }
    // If no json_schedule_final field or parsing failed, return the original response
    return aiResponse;
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Your AI-Generated Study Plan</h2>

      {hasAIResponses ? (
        <div>
          {/* Tabs for multiple subjects */}
          {subjectsWithResponses.length > 1 && (
            <div className="mb-6 border-b">
              <div className="flex space-x-4">
                {subjectsWithResponses.map((subject, idx) => (
                  <button
                    key={idx}
                    className={`py-2 px-4 font-medium ${
                      activeTab === idx
                        ? "border-b-2 border-blue-500 text-blue-600"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                    onClick={() => setActiveTab(idx)}
                  >
                    {subject.topic || subject.subject}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Display active subject's AI response */}
          {subjectsWithResponses.length > 0 && (
            <div className="p-4 border rounded-lg bg-gray-50">
              {renderAIResponse(
                extractScheduleFromJsonField(
                  subjectsWithResponses[activeTab]?.aiResponse
                )
              )}
            </div>
          )}
        </div>
      ) : (
        <div className="bg-yellow-50 p-4 rounded-md">
          <p>
            No AI-generated study plans are available. Make sure you've uploaded
            PDF materials in Step 1.
          </p>
        </div>
      )}

      <div className="mt-6 flex justify-between">
        <button
          onClick={onBack}
          className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
        >
          Back
        </button>
        <button
          onClick={() => (window.location.href = "/dashboard")}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Save & Go to Dashboard
        </button>
      </div>
    </div>
  );
};

export default Step3GeneratedPlanJS;
