import { useState, useRef } from "react";

const SignUp = ({ setModel }) => {
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [continueAs, setContinueAs] = useState("");
  const [captcha, setCaptcha] = useState(false);

  const [showOTP, setShowOTP] = useState(false);
  const [otp, setOtp] = useState(["", "", "", ""]);
  const inputRefs = useRef([]);
  const [generatedOTP, setGeneratedOTP] = useState("");

  const handleOtpChange = (index, value) => {
    if (!/^\d?$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      alert("Passwords do not match.");
      return;
    }

    if (!captcha) {
      alert("Please complete the captcha.");
      return;
    }

    const generated = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(generated);
    console.log("Generated OTP:", generated);
     const sendOtp = async () => {
  const response = await fetch("https://api.aiclassroom.in/sent-otp", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email, otp: generated }),
  });

  if (!response.ok) {
    alert("❌ Failed to send OTP");
    return;
  }

  const result = await response.json();
  console.log("✅ OTP Sent:", result.message);
};
sendOtp()
    setShowOTP(true);
  };

  const verifyOTPAndSubmit = async () => {
    const enteredOtp = otp.join("");
    if (enteredOtp !== generatedOTP) {
      alert("Incorrect OTP. Please try again.");
      return;
    }

    const payload = {
      email,
      firstName,
      lastName,
      password,
      accounttype: continueAs,
      signinwithgoogle: false,
    };

    try {
      const response = await fetch("https://api.aiclassroom.in/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      if (response.ok) {
        setTimeout(() => {
          fetch("https://api.aiclassroom.in/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, password }),
          })
            .then((response) => response.json())
            .then((data1) => {
              localStorage.setItem("auth-token", data1.token);
              localStorage.setItem(
                "user-data",
                JSON.stringify({
                  userId: data1.user._id,
                  email: data1.email,
                  firstName: data1.user.firstName,
                  avatar: data1.user.avatar,
                  coins: data1.user.coins,
                  totalCoins: data1.user.totalCoins,
                })
              );
              window.location.reload();
            });
        }, 1000);
        localStorage.setItem("auth-token", data.token);
        setModel(false);
      } else {
        alert(data.message || "An error occurred during sign up.");
      }
    } catch (error) {
      console.error("Error during sign up:", error);
      alert("An error occurred. Please try again.");
    }
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-15"></div>

      <div className="fixed inset-0 flex items-center justify-center z-200">
        <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md mx-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Sign Up</h2>
            <button
              onClick={() => setModel(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {!showOTP ? (
            <form onSubmit={handleFormSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <input
                  type="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Confirm Password
                </label>
                <input
                  type="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Continue as
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600"
                value={continueAs}
                onChange={(e) => setContinueAs(e.target.value)}
                required
              >
                <option value="" disabled>
                  Choose an option
                </option>
                <option value="teacher">Teacher</option>
                <option value="student">Student</option>
              </select>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="captcha"
                checked={captcha}
                onChange={(e) => setCaptcha(e.target.checked)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <label
                htmlFor="captcha"
                className="ml-2 block text-sm text-gray-700"
              >
                I'm not a robot
              </label>
            </div>

              <button
                type="submit"
                className="w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md transition duration-200"
              >
                Sign Up
              </button>
            </form>
          ) : (
            <div className="text-center">
              <h3 className="mb-2 text-lg font-semibold">Enter the 4-digit OTP</h3>
              <div className="flex justify-center gap-3 mb-4">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    type="text"
                    maxLength="1"
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    ref={(el) => (inputRefs.current[index] = el)}
                    className="w-12 h-12 text-center text-xl border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                ))}
              </div>
              <button
                onClick={verifyOTPAndSubmit}
                className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-200"
              >
                Verify & Submit
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default SignUp;
