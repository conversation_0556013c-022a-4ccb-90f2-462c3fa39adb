import axios from "axios";
import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

// --- TimeBox Component ---
const TimeBox = ({ value, label }) => (
  <div className="bg-purple-100 p-4 rounded-lg w-20">
    <div className="text-2xl font-bold text-purple-700">{value}</div>
    <div className="text-sm text-gray-600">{label}</div>
  </div>
);

// --- QuizInfoCard Component ---
const QuizInfoCard = ({ icon, title, value, bgColor }) => (
  <div className={`${bgColor} p-4 rounded-lg`}>
    <div className="flex items-center gap-3">
      <span className="text-2xl">{icon}</span>
      <div>
        <div className="text-gray-600 text-sm">{title}</div>
        <div className="font-semibold">{value}</div>
      </div>
    </div>
  </div>
);

// --- PreparationTip Component ---
const PreparationTip = ({ text }) => (
  <li className="flex items-start gap-3">
    <span className="text-green-500 mt-1">✓</span>
    <span className="text-gray-600">{text}</span>
  </li>
);

// --- Main QuizInterface Component ---
const QuizInterface = () => {
  const [bannerContest, setBannerContest] = useState();
  const [userData, setUserData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [canStartQuiz, setCanStartQuiz] = useState(false);

  const navigate = useNavigate();

  // --- Load userData and bannerContest ---
  useEffect(() => {
    const storedUserData = localStorage.getItem("user-data");
    if (storedUserData) {
      try {
        setUserData(JSON.parse(storedUserData));
      } catch (err) {
        console.error("Error parsing user data from localStorage", err);
      }
    }

    const fetchBannerContest = async () => {
      try {
        const response = await axios.get("https://api.aiclassroom.in/contest");
        setBannerContest(response?.data?.data);
        setLoading(false);
      } catch (err) {
        setError("Failed to load banner contest");
        console.error("Error fetching banner contest:", err);
        setLoading(false);
      }
    };

    fetchBannerContest();
  }, []);

  // --- Handle Timer Countdown ---
  useEffect(() => {
    if (!bannerContest?.examtime) return;

    const hours = parseInt(bannerContest.examtime.substring(0, 2));
    const minutes = parseInt(bannerContest.examtime.substring(2, 4));
    const examStartDate = new Date(bannerContest.date);
    examStartDate.setHours(hours, minutes, 0, 0);

    // Calculate exam end time (examStart + duration)
    const examEndTime = new Date(examStartDate.getTime() + bannerContest.duration);

    const updateTimer = () => {
      const now = new Date();
      const isWithinExamTimeRange = now >= examStartDate && now <= examEndTime;

      setCanStartQuiz(isWithinExamTimeRange); // Enable/Disable start button

      // Calculate remaining time for countdown display
      const diff = examStartDate - now;

      // Ensure countdown stops at 00:00:00
      if (diff <= 0) {
        setTimeLeft({ days: "00", hours: "00", minutes: "00", seconds: "00" });
        return;
      }

      // Time remaining calculations
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft({
        days: days.toString().padStart(2, "0"),
        hours: hours.toString().padStart(2, "0"),
        minutes: minutes.toString().padStart(2, "0"),
        seconds: seconds.toString().padStart(2, "0"),
      });
    };

    updateTimer();
    const timerId = setInterval(updateTimer, 1000);

    return () => clearInterval(timerId);
  }, [bannerContest]);

  // --- Helper: Format date for display ---
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  // --- Loading, Error, or No Contest Handling ---
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-xl text-purple-600">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-xl text-red-600">{error}</div>
      </div>
    );
  }

  if (!bannerContest) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-xl text-purple-600">No contest data available</div>
      </div>
    );
  }

  // --- Quiz Interface ---
  const durationMinutes = bannerContest.duration
    ? Math.floor(bannerContest.duration / 60000)
    : 0;

  return (
    <div className="min-h-screen bg-gradient-to-r from-blue-50 to-purple-50 p-6">
      <div className="max-w-4xl mx-auto">

        {/* Header */}
        <div className="flex justify-between items-center bg-white/80 backdrop-blur-sm p-4 rounded-lg mb-8">
          <div>
            <h1 className="text-xl font-bold text-purple-700">
              {bannerContest.title || "Knowledge Challenge"}
            </h1>
            <p className="text-sm text-gray-600">Get ready for the challenge !!</p>
          </div>
          <Link to="/" className="text-sm text-blue-600 hover:text-blue-700 flex items-center gap-2">
            ← Back To Home
          </Link>
        </div>

        {/* Countdown Timer */}
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold mb-8">Quiz Begins In</h2>
          <div className="flex justify-center gap-4">
            <TimeBox value={timeLeft.days} label="Days" />
            <TimeBox value={timeLeft.hours} label="Hours" />
            <TimeBox value={timeLeft.minutes} label="Minutes" />
            <TimeBox value={timeLeft.seconds} label="Seconds" />
          </div>
        </div>

        {/* Quiz Details */}
        <div className="grid grid-cols-2 gap-6">
          <QuizInfoCard icon="⏱️" title="Duration" value={`${durationMinutes} Minutes`} bgColor="bg-blue-300" />
          <QuizInfoCard icon="❓" title="Questions" value={`${bannerContest.questions.length}`} bgColor="bg-green-300" />
          <QuizInfoCard icon="📅" title="Date" value={formatDate(bannerContest.date)} bgColor="bg-yellow-300" />
          <QuizInfoCard icon="👥" title="Participants" value={`5K Registered`} bgColor="bg-purple-300" />
        </div>

        {/* Start Quiz Button */}
        <div className="mt-8 text-center">
          <button
            onClick={() => navigate("/quiz-question")}
            className={`${
              true
                ? "bg-gradient-to-r from-purple-600 to-indigo-600 text-white"
                : "bg-gray-400 text-white cursor-not-allowed"
            } px-8 py-3 rounded-lg text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl active:scale-95 transform`}
          >
            Start Quiz
            <span className="ml-2">→</span>
          </button>
        </div>

        {/* Preparation Tips */}
        <div className="mt-12 bg-white rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Preparation Tips</h3>
          <ul className="space-y-3">
            <PreparationTip text="Make sure your device is fully charged and has a stable internet connection" />
            <PreparationTip text="Find a quiet space where you won't be disturbed for the quiz duration" />
            <PreparationTip text="Have a pen and paper ready for any calculations or notes" />
          </ul>
        </div>

      </div>
    </div>
  );
};

export default QuizInterface;
