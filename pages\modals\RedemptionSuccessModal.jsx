"use client"

function RedemptionSuccessModal({ onClose, rewardDetails }) {
  const { title, price, code } = rewardDetails

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md p-6 flex flex-col items-center">
        <div className="bg-green-100 rounded-full p-4 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
          </svg>
        </div>

        <h2 className="text-2xl font-bold mb-2">Redemption successful!</h2>
        <p className="text-gray-600 mb-6">
          You've successfully redeemed {title} (${price})
        </p>

        <div className="w-full bg-white border border-gray-200 rounded-lg p-4 mb-6">
          <h3 className="font-medium mb-1">Digital Reward Details</h3>
          <p className="text-gray-600 mb-4">Your digital gift card (${price}) has been processed</p>

          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="font-medium mb-1">Redemption code:</p>
            <div className="flex items-center">
              <span className="text-blue-600 font-mono">{code}</span>
              <button className="ml-2 text-gray-400">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <button
          onClick={onClose}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          Return To Rewards
        </button>
      </div>
    </div>
  )
}

export default RedemptionSuccessModal
