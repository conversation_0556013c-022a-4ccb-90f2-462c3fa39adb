.rotating-box-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
  animation: rotate 2s infinite linear;
  transform-origin: center center;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.box-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4px;
  width: 100%;
  height: 100%;
}

.box {
  width: 20px;
  height: 20px;
  background-color: #3b82f6;
  animation: pulse 1.5s infinite;
}

.box1 {
  animation-delay: 0s;
}

.box2 {
  animation-delay: 0.2s;
}

.box3 {
  animation-delay: 0.4s;
}

.dot {
  width: 4px;
  height: 4px;
  background-color: #3b82f6;
  border-radius: 50%;
  margin-top: 8px;
  margin-left: 8px;
  animation: pulse 1.5s infinite;
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
