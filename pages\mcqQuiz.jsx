import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Mock data for initial implementation - will be replaced with API call
const mockQuizData = {
    topic: "Topic Name Fetched from Backend",
    totalQuestions: 200,
    totalMarks: 200,
    duration: 1800, // 30 minutes in seconds
    questions: [
        {
            id: 1,
            question: "The Desert National Park (DNP) is located in which state?",
            options: ["Rajasthan", "Haryana", "Gujarat", "Assam"],
            correctAnswer: 0
        },
        // Add more mock questions for testing
        ...Array.from({ length: 200 }, (_, i) => ({
            id: i + 2,
            question: `Sample question ${i + 2} for testing purposes?`,
            options: [`Option A ${i + 2}`, `Option B ${i + 2}`, `Option C ${i + 2}`, `Option D ${i + 2}`],
            correctAnswer: Math.floor(Math.random() * 4)
        }))
    ]
};

const MCQQuiz = () => {
    // State management
    const [quizData, setQuizData] = useState(null);
    const [currentQuestion, setCurrentQuestion] = useState(1);
    const [selectedAnswers, setSelectedAnswers] = useState({});
    const [markedForReview, setMarkedForReview] = useState(new Set());
    const [timeLeft, setTimeLeft] = useState(0);
    const [loading, setLoading] = useState(true);
    const [showSubmitModal, setShowSubmitModal] = useState(false);

    const navigate = useNavigate();

    // Initialize quiz data and load from localStorage
    useEffect(() => {
        // In real implementation, this would be an API call
        // const fetchQuizData = async () => {
        //   try {
        //     const response = await axios.get('/api/quiz/mcq');
        //     setQuizData(response.data);
        //   } catch (error) {
        //     console.error('Failed to load quiz data:', error);
        //   }
        // };

        // For now, use mock data
        setQuizData(mockQuizData);
        setTimeLeft(mockQuizData.duration);

        // Load saved state from localStorage
        const savedAnswers = localStorage.getItem('mcq_quiz_answers');
        const savedMarkedForReview = localStorage.getItem('mcq_quiz_marked_for_review');
        const savedCurrentQuestion = localStorage.getItem('mcq_quiz_current_question');
        const savedStartTime = localStorage.getItem('mcq_quiz_start_time');

        if (savedAnswers) {
            try {
                setSelectedAnswers(JSON.parse(savedAnswers));
            } catch (error) {
                console.error('Error parsing saved answers:', error);
            }
        }

        if (savedMarkedForReview) {
            try {
                setMarkedForReview(new Set(JSON.parse(savedMarkedForReview)));
            } catch (error) {
                console.error('Error parsing marked for review:', error);
            }
        }

        if (savedCurrentQuestion) {
            setCurrentQuestion(parseInt(savedCurrentQuestion, 10));
        }

        // Calculate remaining time if quiz was started before
        if (savedStartTime) {
            const startTime = parseInt(savedStartTime, 10);
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const remaining = Math.max(0, mockQuizData.duration - elapsed);
            setTimeLeft(remaining);
        } else {
            // First time starting quiz
            localStorage.setItem('mcq_quiz_start_time', Date.now().toString());
        }

        setLoading(false);
    }, []);

    // Timer countdown
    useEffect(() => {
        if (timeLeft <= 0) return;

        const timer = setInterval(() => {
            setTimeLeft(prev => {
                if (prev <= 1) {
                    // Auto-submit when time runs out
                    setShowSubmitModal(true);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    // Save state to localStorage whenever it changes
    useEffect(() => {
        localStorage.setItem('mcq_quiz_answers', JSON.stringify(selectedAnswers));
    }, [selectedAnswers]);

    useEffect(() => {
        localStorage.setItem('mcq_quiz_marked_for_review', JSON.stringify([...markedForReview]));
    }, [markedForReview]);

    useEffect(() => {
        localStorage.setItem('mcq_quiz_current_question', currentQuestion.toString());
    }, [currentQuestion]);

    // Format time display
    const formatTime = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')} : ${minutes.toString().padStart(2, '0')} : ${secs.toString().padStart(2, '0')}`;
    };

    // Handle answer selection
    const handleAnswerSelect = (questionId, optionIndex) => {
        setSelectedAnswers(prev => ({
            ...prev,
            [questionId]: optionIndex
        }));
    };

    // Handle mark for review
    const handleMarkForReview = () => {
        const questionId = quizData.questions[currentQuestion - 1].id;
        setMarkedForReview(prev => {
            const newSet = new Set(prev);
            if (newSet.has(questionId)) {
                newSet.delete(questionId);
            } else {
                newSet.add(questionId);
            }
            return newSet;
        });
    };

    // Navigation functions
    const handleSkip = () => {
        if (currentQuestion < quizData.totalQuestions) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handleProceed = () => {
        if (currentQuestion < quizData.totalQuestions) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handleQuestionNavigation = (questionNumber) => {
        setCurrentQuestion(questionNumber);
    };

    // Get question status for styling
    const getQuestionStatus = (questionNumber) => {
        const questionId = quizData.questions[questionNumber - 1].id;
        const isAnswered = selectedAnswers.hasOwnProperty(questionId);
        const isMarked = markedForReview.has(questionId);
        const isCurrent = questionNumber === currentQuestion;

        if (isCurrent) return 'current';
        if (isMarked) return 'marked';
        if (isAnswered) return 'answered';
        return 'unanswered';
    };

    // Calculate attempted questions count
    const getAttemptedCount = () => {
        return Object.keys(selectedAnswers).length;
    };

    // Handle quiz submission
    const handleSubmitQuiz = () => {
        setShowSubmitModal(true);
    };

    const confirmSubmit = async () => {
        // Prepare submission data
        const submissionData = {
            answers: selectedAnswers,
            markedForReview: [...markedForReview],
            timeSpent: quizData.duration - timeLeft,
            submittedAt: new Date().toISOString()
        };

        try {
            // In real implementation, submit to API
            // await axios.post('/api/quiz/submit', submissionData);

            console.log('Quiz submitted:', submissionData);

            // Clear localStorage
            localStorage.removeItem('mcq_quiz_answers');
            localStorage.removeItem('mcq_quiz_marked_for_review');
            localStorage.removeItem('mcq_quiz_current_question');
            localStorage.removeItem('mcq_quiz_start_time');

            // Navigate to results page
            navigate('/quiz-results', { state: { submissionData, quizData } });
        } catch (error) {
            console.error('Failed to submit quiz:', error);
            alert('Failed to submit quiz. Please try again.');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3A1078]"></div>
            </div>
        );
    }

    if (!quizData) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center">
                <div className="text-center">
                    <h2 className="text-xl font-semibold text-[#3A1078] mb-2">Failed to load quiz</h2>
                    <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-[#3A1078] text-white rounded-lg hover:bg-[#2A0A5A]"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    const currentQuestionData = quizData.questions[currentQuestion - 1];

    return (
        <div className="min-h-screen bg-[#F9FDFF] p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-medium text-[#3A1078] mb-4 font-poppins">
                        Topic: {quizData.topic}
                    </h1>
                    <p className="text-xl text-[#3A1078] font-medium font-poppins">
                        Choose the correct option, and submit your quiz after reviewing all questions.
                    </p>
                </div>

                {/* Main Quiz Interface */}
                <div className="flex gap-8">
                    {/* Left Section - Question Display */}
                    <div className="flex-1">
                        <div className="bg-white rounded-[38px] shadow-lg overflow-hidden">
                            {/* Question Header */}
                            <div className="bg-[#4C60A5] text-white px-6 py-4 flex justify-between items-center">
                                <h2 className="text-3xl font-medium font-poppins">Question {currentQuestion}</h2>
                                <span className="text-lg font-medium font-poppins">{currentQuestion}/{quizData.totalQuestions}</span>
                            </div>

                            {/* Question Content */}
                            <div className="p-8">
                                <h3 className="text-2xl font-medium text-black mb-8 font-advent-pro">
                                    {currentQuestionData.question}
                                </h3>

                                {/* Options */}
                                <div className="space-y-4">
                                    {currentQuestionData.options.map((option, index) => {
                                        const isSelected = selectedAnswers[currentQuestionData.id] === index;
                                        return (
                                            <div key={index} className="flex items-center gap-4">
                                                <div className="relative">
                                                    <div className={`w-6 h-6 rounded-full border-3 ${isSelected ? 'border-black' : 'border-gray-400'}`}>
                                                        {isSelected && (
                                                            <div className="w-3.5 h-3.5 bg-[#4182F9] rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                                                        )}
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={() => handleAnswerSelect(currentQuestionData.id, index)}
                                                    className="text-xl font-medium text-black font-advent-pro text-left hover:text-[#4182F9] transition-colors"
                                                >
                                                    {option}
                                                </button>
                                            </div>
                                        );
                                    })}
                                </div>

                                {/* Navigation Buttons */}
                                <div className="flex justify-between gap-4 mt-12">
                                    <button
                                        onClick={handleSkip}
                                        className="px-8 py-3 bg-[#342499] text-white rounded-lg font-bold text-base font-poppins hover:bg-[#2A1A7A] transition-colors"
                                    >
                                        Skip
                                    </button>
                                    <div className='flex gap-4'>
                                        <button
                                            onClick={handleMarkForReview}
                                            className={`px-8 py-3 rounded-lg font-bold text-base font-poppins transition-colors ${markedForReview.has(currentQuestionData.id)
                                                ? 'bg-[#D4A017] text-white hover:bg-[#B8900F]'
                                                : 'bg-[#AE7A01] text-white hover:bg-[#8B6001]'
                                                }`}
                                        >
                                            {markedForReview.has(currentQuestionData.id) ? 'Unmark Review' : 'Mark For Review'}
                                        </button>
                                        <button
                                            onClick={handleProceed}
                                            disabled={currentQuestion >= quizData.totalQuestions}
                                            className="px-8 py-3 bg-[#217C58] text-white rounded-lg font-bold text-base font-poppins hover:bg-[#1A6347] disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                                        >
                                            Proceed
                                        </button>
                                    </div>
                                </div>


                            </div>
                            {/* Bottom Info */}
                            <div className='bg-[#4C60A5] w-full pb-4'>
                                <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
                                    <span className="text-xl font-normal text-white bg-[#4C60A5] px-4 py-2 rounded font-jost">
                                        Total Number Of Questions: {quizData.totalQuestions}
                                    </span>
                                    <span className="text-xl font-normal text-white bg-[#4C60A5] px-4 py-2 rounded font-jost">
                                        Total Marks: {quizData.totalMarks}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Section - Timer and Navigation */}
                    <div className="w-90">
                        <div className="bg-white rounded-lg shadow-lg p-6 space-y-6 border-2 border-gray-200">
                            {/* Timer */}
                            <div className="text-center">
                                <h3 className="text-xl font-bold text-black mb-2 font-jost">Time Left</h3>
                                <div className="text-4xl font-extrabold text-[#08A064] font-jost">
                                    {formatTime(timeLeft)}
                                </div>
                            </div>

                            {/* Attempted Count */}
                            <div className="text-center">
                                <p className="text-xl font-semibold text-black font-poppins">
                                    Attempted: {getAttemptedCount()}/{quizData.totalQuestions}
                                </p>
                            </div>

                            {/* Question Navigation Grid */}
                            <div className="h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200">
                                <div className="space-y-3 pr-2">
                                    {Array.from({ length: Math.ceil(quizData.totalQuestions / 6) }, (_, rowIndex) => (
                                        <div key={rowIndex} className="flex gap-2">
                                            {Array.from({ length: 6 }, (_, colIndex) => {
                                                const questionNumber = rowIndex * 6 + colIndex + 1;
                                                if (questionNumber > quizData.totalQuestions) return null;

                                                const status = getQuestionStatus(questionNumber);
                                                const getButtonStyle = () => {
                                                    switch (status) {
                                                        case 'current':
                                                            return 'bg-[#4D8BD7] text-white'; //  for current
                                                        case 'answered':
                                                            return 'bg-[#217C58] text-white'; // Green for answered
                                                        case 'marked':
                                                            return 'bg-[#AE7A01] text-white'; // Orange for marked
                                                        default:
                                                            return 'bg-[#5C5C5D] text-white'; // Gray for unanswered
                                                    }
                                                };

                                                return (
                                                    <button
                                                        key={questionNumber}
                                                        onClick={() => handleQuestionNavigation(questionNumber)}
                                                        className={`w-12 h-12 rounded-lg font-normal text-xl font-prompt ${getButtonStyle()} hover:opacity-80 transition-opacity shadow-md`}
                                                    >
                                                        {questionNumber}
                                                    </button>
                                                );
                                            })}
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                onClick={handleSubmitQuiz}
                                className="w-full py-4 bg-[#005FD0] text-white rounded-lg font-medium text-lg font-afacad hover:bg-[#004BB0] transition-colors shadow-md"
                            >
                                Confirm and Submit
                            </button>
                        </div>
                    </div>
                </div>

                {/* Submit Confirmation Modal */}
                {showSubmitModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                            <h3 className="text-xl font-bold text-[#3A1078] mb-4">Confirm Submission</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to submit your quiz? You have answered {getAttemptedCount()} out of {quizData.totalQuestions} questions.
                            </p>
                            <div className="flex gap-4">
                                <button
                                    onClick={() => setShowSubmitModal(false)}
                                    className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={confirmSubmit}
                                    className="flex-1 px-4 py-2 bg-[#005FD0] text-white rounded-lg hover:bg-[#004BB0] transition-colors"
                                >
                                    Submit Quiz
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MCQQuiz;