"use client";

import { useState } from "react";
import RewardsHeader from "./RewardsHeader";
import RewardsNavigation from "./RewardsNavigation";
import OverviewTab from "./tabs/OverviewTab";
import RedeemTab from "./tabs/RedeemTab";
import HistoryTab from "./tabs/HistoryTab";

function RewardsCenter() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="max-w-4xl mx-auto p-4">
      <RewardsHeader />

      <div className="mt-4 rounded-xl overflow-hidden bg-gradient-to-r from-blue-500 to-purple-500">
        <RewardsNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
      </div>

      <div className="mt-6">
        {activeTab === "overview" && <OverviewTab />}
        {activeTab === "redeem" && <RedeemTab />}
        {activeTab === "history" && <HistoryTab />}
      </div>
    </div>
  );
}

export default RewardsCenter;
