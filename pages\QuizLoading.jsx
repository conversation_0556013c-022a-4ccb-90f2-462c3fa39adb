import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const QuizLoading = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Navigate to quiz interface after 2 seconds
    const timer = setTimeout(() => {
      navigate("/quiz-waiting-interface");
    }, 2000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
      <div className="text-center">
        <div className="w-16 h-16 mb-8 mx-auto">
          <div className="w-full h-full rounded-full border-4 border-purple-200 border-t-purple-500 animate-spin"></div>
        </div>

        <h1 className="text-2xl font-bold mb-2">AI Knowledge Test</h1>
        <p className="text-gray-600 mb-8">Almost Ready...</p>

        <div className="w-64 h-2 bg-purple-100 rounded-full overflow-hidden">
          <div className="h-full bg-purple-500 animate-[loading_2s_ease-in-out]"></div>
        </div>

        <button className="mt-8 bg-blue-50 text-blue-600 px-6 py-2 rounded-lg">
          Did You Know ?
        </button>
      </div>
    </div>
  );
};

export default QuizLoading;

// Add this to your global CSS file (e.g., index.css)
const cssToAdd = `
@keyframes loading {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
`;
