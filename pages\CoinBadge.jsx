function CoinBadge({ amount, size = "md", variant = "default" }) {
    const sizeClasses = {
      sm: "text-xs py-0.5 px-2",
      md: "text-sm py-1 px-3",
      lg: "text-base py-1.5 px-4",
    }
  
    const variantClasses = {
      default: "bg-blue-500 text-white",
      available: "bg-blue-100 text-blue-600",
    }
  
    return (
      <div className={`flex items-center gap-1 rounded-full ${sizeClasses[size]} ${variantClasses[variant]}`}>
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="9" stroke="currentColor" strokeWidth="2" />
          <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          <path d="M12 8L12 16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
        </svg>
        <span>
          {amount} coins{variant === "available" ? " available" : ""}
        </span>
      </div>
    )
  }
  
  export default CoinBadge
  