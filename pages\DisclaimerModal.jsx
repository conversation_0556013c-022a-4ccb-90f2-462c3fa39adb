import React, { useEffect, useState } from "react";

const DisclaimerModal = ({ onClose }) => {
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (countdown === 0) {
      onClose();
      return;
    }
    const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    return () => clearTimeout(timer);
  }, [countdown, onClose]);

  return (
    <div style={{
      position: "fixed",
      top: 0,
      left: 0,
      width: "100vw",
      height: "100vh",
      background: "rgba(0,0,0,0.7)",
      zIndex: 9999,
      display: "flex",
      alignItems: "center",
      justifyContent: "center"
    }}>
      <div style={{
        background: "#23272f",
        borderRadius: "16px",
        padding: "2.5rem 2rem 2rem 2rem",
        boxShadow: "0 8px 32px rgba(0,0,0,0.25)",
        maxWidth: 600,
        width: "90%",
        textAlign: "center",
        color: "#fff"
      }}>
        <h2 style={{ fontSize: "2rem", fontWeight: 700, marginBottom: "1.5rem" }}>Disclaimer</h2>
        <div style={{ fontSize: "1.1rem", marginBottom: "2.5rem", color: "#e5e7eb" }}>
          This Platform Includes Content And Images Generated By AI For Educational And Illustrative Purposes. While Efforts Are Made To Ensure Accuracy, The Outputs May Not Always Be Factually Correct, Complete, Or Contextually Appropriate. Users Should Verify Critical Information Independently. Any Resemblance To Real Persons Or Events Is Purely Coincidental.
        </div>
        <button
          style={{
            background: "#444",
            color: "#fff",
            border: "none",
            borderRadius: "8px",
            padding: "0.7rem 2.5rem",
            fontSize: "1.1rem",
            fontWeight: 600,
            cursor: countdown === 0 ? "pointer" : "not-allowed",
            opacity: countdown === 0 ? 1 : 0.7
          }}
          disabled={countdown !== 0}
          onClick={onClose}
        >
          Exit{countdown > 0 && ` (${countdown})`}
        </button>
      </div>
    </div>
  );
};

export default DisclaimerModal; 