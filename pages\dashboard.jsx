import React, { useState } from 'react';
import {
  Home,
  BookText,
  ClipboardCheck,
  CalendarDays,
  Users,
  Menu, // Changed from MoreVertical to Menu
  X, // Import X icon for closing mobile menu
  PlayCircle,
  Lightbulb,
  FileText,
  Video,
  Sparkles,
  Target,
  Flame,
} from 'lucide-react';

// Sidebar Component - Modified for responsiveness
const Sidebar = ({ isOpen, toggleSidebar }) => (
  <>
    {/* Desktop Sidebar */}
    <div className="w-64 bg-white h-screen p-6 flex-col justify-between hidden lg:flex">
      <div>
        <div className="flex items-center mb-12">
          <div className="flex items-center">
            <img src="/logo.png" alt="Logo" className="h-8 w-8" />
            <span className="ml-2 text-xl font-semibold">AI Classroom</span>
          </div>
        </div>
        <nav>
          <ul>
            <NavItem icon={<Home size={20} />} label="Dashboard" textColorClass="text-black" />
            <NavItem icon={<BookText size={20} />} label="Notes" textColorClass="text-black" />
            <NavItem icon={<ClipboardCheck size={20} />} label="Exams" textColorClass="text-black" />
            <NavItem icon={<CalendarDays size={20} />} label="Planner" textColorClass="text-black" />
            <NavItem icon={<Users size={20} />} label="Mentor & Progress" textColorClass="text-black" />
          </ul>
        </nav>
      </div>
    </div>

    {/* Mobile Sidebar Overlay (when sidebar is open, this dims the background and captures clicks) */}
    <div
      className={`fixed top-0 left-0 w-full h-full bg-black z-40 lg:hidden transition-opacity duration-300 ${isOpen ? 'opacity-20 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
      onClick={toggleSidebar}
    ></div>

    {/* Mobile Sidebar */}
    <div className={`fixed top-0 left-0 w-64 bg-white h-screen p-6 flex-col justify-between shadow-md z-50 transition-transform duration-300 ease-in-out lg:hidden ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
      <div className="flex items-center justify-between mb-12">
        <div className="flex items-center">
          <img src="/logo.png" alt="Logo" className="h-8 w-8" />
          <span className="ml-2 text-xl font-semibold">AI Classroom</span>
        </div>
        <button onClick={toggleSidebar} className="text-gray-600 hover:text-gray-900">
          <X size={24} />
        </button>
      </div>
      <nav>
        <ul>
          {/* Ensure onClick is passed and used by NavItem's clickable element */}
          <NavItem icon={<Home size={20} />} label="Dashboard" textColorClass="text-black" onClick={toggleSidebar} />
          <NavItem icon={<BookText size={20} />} label="Notes" textColorClass="text-black" onClick={toggleSidebar} />
          <NavItem icon={<ClipboardCheck size={20} />} label="Exams" textColorClass="text-black" onClick={toggleSidebar} />
          <NavItem icon={<CalendarDays size={20} />} label="Planner" textColorClass="text-black" onClick={toggleSidebar} />
          <NavItem icon={<Users size={20} />} label="Mentor & Progress" textColorClass="text-black" onClick={toggleSidebar} />
        </ul>
      </nav>
    </div>
  </>
);

// Navigation Item Component
const NavItem = ({ icon, label, active = false, textColorClass = '', onClick }) => (
  <li className="mb-4 rounded-lg">
    {/* Added active: classes for mobile tap feedback and transition-colors for smoothness */}
    <a
      href="#"
      className={`flex items-center p-3 rounded-lg
        ${active ? 'bg-indigo-600 text-white' : `hover:bg-indigo-600 hover:text-white ${textColorClass || 'text-gray-600'}`}
        active:bg-indigo-600 active:text-white transition-colors duration-150
      `}
      onClick={onClick}
    >
      {icon}
      <span className="ml-4 font-medium">{label}</span>
    </a>
  </li>
);

// Floating Action Button (FAB) for Mobile Sidebar
const MobileFAB = ({ toggleSidebar }) => (
  <button
    onClick={toggleSidebar}
    // Removed bg-indigo-600 and rounded-full, changed position and icon color
    className="fixed top-4 left-4 p-2  lg:hidden z-30 flex items-center justify-center text-purple-800"
    aria-label="Open navigation"
  >
    <Menu size={28} /> {/* Increased size slightly for better visibility without background */}
  </button>
);

// Main Content Component
const MainContent = () => (
  <div className="flex-1 bg-gray-50 p-4 lg:p-8 overflow-y-auto">
    <Header />
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
      <div className="lg:col-span-2">
        <LearningGoals />
      </div>
      <div>
        <LearningStreak />
      </div>
    </div>
    <AITools />
  </div>
);

// Header Component
const Header = () => (
  <div className="bg-[#EFF6FF] p-4 lg:p-6 rounded-lg shadow-sm flex flex-col lg:flex-row justify-between items-center border border-indigo-100 text-center lg:text-left">
    <div className="mb-4 lg:mb-0">
      <h2 className="text-xl lg:text-2xl font-semibold text-black">Welcome back, John!</h2>
      <p className="text-sm lg:text-base text-[#4B5563]">You're on week 2 of your 4-week learning plan</p>
    </div>
    <button className="bg-[#3B82F6] text-white px-4 py-2 lg:px-6 lg:py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-200 text-sm lg:text-base">
      Continue Today's Goals
    </button>
  </div>
);

// Progress Ring Component (Simplified)
const ProgressRing = ({ percentage }) => (
  <div className="relative w-20 h-20">
    <svg className="w-full h-full" viewBox="0 0 100 100">
      {/* Background circle */}
      <circle
        className="text-gray-200 stroke-current"
        strokeWidth="10"
        cx="50"
        cy="50"
        r="40"
        fill="transparent"
      ></circle>
      {/* Progress circle */}
      <circle
        className="text-indigo-600 stroke-current"
        strokeWidth="10"
        strokeLinecap="round"
        cx="50"
        cy="50"
        r="40"
        fill="transparent"
        strokeDasharray="251.2"
        strokeDashoffset={`calc(251.2 - (251.2 * ${percentage}) / 100)`}
        transform="rotate(-90 50 50)"
      ></circle>
      {/* Text */}
      <text x="50" y="50" className="font-bold text-xl text-indigo-700" textAnchor="middle" dy=".3em">
        {percentage}%
      </text>
    </svg>
  </div>
);

// Learning Goals Component
const LearningGoals = () => (
  <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
    <div className="flex justify-between items-center mb-6">
      <h3 className="text-lg lg:text-xl font-semibold text-gray-900">Today's Learning Goals</h3>
      <ProgressRing percentage={67} />
    </div>
    <LearningItem
      icon={
        <img
          src="/src/assets/dashboard1.svg"
          alt="Dashboard1 Icon"
          className="w-8 h-8"
        />
      }
      title="AI Quiz: Neural Networks"
      subtitle="10 questions"
      buttonText="Start Quiz"
      buttonColor="bg-gray-500"
      itemBgColor="bg-[#F3F4F6]"
    />
    <LearningItem
      icon={
        <img
          src="/src/assets/dashboard2.svg"
          alt="Dashboard2 Icon"
          className="w-8 h-8"
        />
      }
      title="AI Notes: Chapter 3"
      subtitle="8 Minutes read"
      buttonText="Read Now"
      buttonColor="bg-[#A855F7]"
      itemBgColor="bg-[#FAF5FF]"
    />
    <LearningItem
      icon={
        <img
          src="/src/assets/dashboard3.svg"
          alt="Dashboard3 Icon"
          className="w-8 h-8"
        />
      }
      title="AI Lecture: Neural Networks"
      subtitle="15 Minutes Video"
      buttonText="Watch Now"
      buttonColor="bg-[#3B82F6]"
      itemBgColor="bg-[#EFF6FF]"
    />
  </div>
);

// Learning Item Component
const LearningItem = ({ icon, title, subtitle, buttonText, buttonColor, itemBgColor = "bg-gray-50" }) => (
  <div className={`flex flex-col sm:flex-row items-center justify-between ${itemBgColor} p-3 sm:p-4 mb-4 rounded-lg border border-gray-100 text-center sm:text-left hover:shadow-lg transition cursor-pointer`}>
    {/* For mobile, display icon, title, and subtitle first, aligned to the left */}
    <div className="flex items-start w-full sm:w-auto mb-2 sm:mb-0">
      <div className="mr-4"> {/* Added margin-right for spacing between icon and text */}
        {icon}
      </div>
      <div className="flex flex-col items-start"> {/* Adjusted to flex-col for text stacking */}
        <p className="font-medium text-gray-800 text-base sm:text-base text-left">{title}</p>
        <p className="text-xs sm:text-sm text-gray-500 text-left">{subtitle}</p>
      </div>
    </div>
    {/* For mobile, button will be at the bottom right. For larger screens, it remains aligned with the other content */}
    <button
      className={`${buttonColor} text-white px-4 py-2 sm:px-5 sm:py-2 rounded-lg text-sm font-medium hover:opacity-90 transition self-end sm:self-auto`}
    >
      {buttonText}
    </button>
  </div>
);

// Learning Streak Component
const LearningStreak = () => (
  <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm text-center h-full flex flex-col justify-center items-center">
    <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-7">Your Learning Streak</h3>
    <div className="relative bg-orange-100 p-4 lg:p-6 rounded-full inline-flex items-center justify-center mb-5 transition-all duration-200 hover:scale-140">
      <Flame size={30} className="text-orange-500 lg:w-8 lg:h-8" />
      <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs font-bold px-1.5 py-0.5 lg:px-2 lg:py-1 rounded-full">
        5 days
      </span>
    </div>
    <p className="text-sm lg:text-base text-gray-600 mb-4">Keep going! You're on your longest streak yet.</p>
    <div className="w-full bg-gray-200 rounded-full h-2 mb-2 lg:h-2.5">
      <div className="bg-orange-500 h-2 rounded-full lg:h-2.5" style={{ width: `${(5 / 7) * 100}%` }}></div>
    </div>
    <p className="text-xs lg:text-sm text-gray-500">Next milestone: 7-day streak (+50 coins)</p>
  </div>
);

// AI Tools Component
const AITools = () => (
  <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm mt-8">
    <h3 className="text-lg lg:text-xl font-semibold text-gray-900 mb-6">AI Tools</h3>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
      <ToolCard
        icon={<img src="/src/assets/dashboard4.svg" alt="Dashboard4 Icon" className="w-12 h-12" />}
        title="Generate Notes to Video"
        subtitle="Convert your notes into video"
        cardBackgroundColor="bg-blue-50"
        iconContainerBg="bg-blue-50"
        iconColor="text-blue-600"
      />
      <ToolCard
        icon={<img src="/src/assets/dashboard5.svg" alt="Dashboard5 Icon" className="w-12 h-12" />}
        title="Topic Based AI Video"
        subtitle="Generate Videos On Any Topic"
        cardBackgroundColor="bg-[#F0FDFA]"
        iconContainerBg="bg-blue-50"
        iconColor="text-green-600"
      />
      <ToolCard
        icon={<img src="/src/assets/dashboard6.svg" alt="Dashboard6 Icon" className="w-12 h-12" />}
        title="PDF to Animations"
        subtitle="Get AI explanations"
        cardBackgroundColor="bg-[#FAF5FF]"
        iconContainerBg="bg-purple-100"
        iconColor="text-purple-600"
      />
      <ToolCard
        icon={<img src="/src/assets/dashboard7.svg" alt="Dashboard7 Icon" className="w-12 h-12" />}
        title="AI Interview"
        subtitle="1 on 1 AI Guidance"
        cardBackgroundColor="bg-[#FFFCF5]"
        iconContainerBg="bg-yellow-100"
        iconColor="text-yellow-600"
      />
    </div>
  </div>
);

// Tool Card Component
const ToolCard = ({ icon, title, subtitle, cardBackgroundColor = "bg-white", iconContainerBg = "bg-blue-50", iconColor }) => (
  <div className={`${cardBackgroundColor} p-4 lg:p-5 rounded-xl border border-gray-100 hover:shadow-lg transition cursor-pointer`}>
    <div className={`w-10 h-10 lg:w-12 lg:h-12 ${iconContainerBg} rounded-lg mb-3 lg:mb-4 flex items-center justify-center`}>
      <div className={iconColor}>
        {icon}
      </div>
    </div>
    <h4 className="font-semibold text-gray-800 mb-1 text-base lg:text-base">{title}</h4>
    <p className="text-xs lg:text-sm text-gray-500">{subtitle}</p>
  </div>
);

// Main App Component
function App() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex flex-col min-h-screen lg:flex-row">
      {/* Mobile-specific FAB */}
      <MobileFAB toggleSidebar={toggleSidebar} />

      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      <MainContent />
    </div>
  );
}

export default App;
//helloo