import React from "react";
import { useNavigate } from "react-router-dom";
import NavBar from "./NavBar";

const translator = (word1, word2) =>
    localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
        ? word1
        : localStorage.getItem("lang")
            ? word2
            : word1;

const ExamIntro = () => {
    const navigate = useNavigate();

    const handleProceed = () => {
        navigate("/mcq-exam-quiz");
    };

    return (
        <div className="min-h-screen bg-white">
            <NavBar />

            <div className="max-w-4xl mx-auto mt-8 p-6">
                <h1 className="text-4xl font-bold text-[#4B0082] mb-4">
                    {translator("All The Best!", "सभी को शुभकामनाएँ!")}
                </h1>

                <div className="flex justify-between items-start mb-8">
                    <div>
                        <p className="text-xl text-[#4B0082] mb-2">
                            {translator("Please read the instructions carefully before starting the exam.", "कृपया परीक्षा शुरू करने से पहले निर्देशों को ध्यान से पढ़ें।")}
                        </p>
                    </div>
                    <div className="text-right">
                        <p className="text-[#4B0082]">
                            {translator("Name:", "नाम:")}
                            <span className="font-semibold">
                                Arindam {/* Dynamically fetched user name */}
                            </span>
                        </p>
                        <p className="text-[#4B0082]">
                            {translator("Subject:", "विषय:")}
                            <span className="font-semibold">
                                GEOGRAPHY {/* Dynamically fetched subject or topic */}
                            </span>
                        </p>
                    </div>
                </div>

                <div className="bg-[#4A4A4A] rounded-lg p-8 text-white space-y-6">
                    <p className="leading-relaxed">
                        {translator("1. Ensure you have a stable internet connection throughout the exam.", "1. सुनिश्चित करें कि परीक्षा के दौरान आपका इंटरनेट कनेक्शन स्थिर है।")}
                    </p>
                    <p className="leading-relaxed">
                        {translator("2. Do not refresh or close the browser during the exam.", "2. परीक्षा के दौरान ब्राउज़र को रिफ्रेश या बंद न करें।")}
                    </p>
                    <p className="leading-relaxed">
                        {translator("3. Answer all questions carefully. You can review your answers before submitting.", "3. सभी प्रश्नों का उत्तर ध्यान से दें। आप सबमिट करने से पहले अपने उत्तरों की समीक्षा कर सकते हैं.")}
                    </p>
                    <p className="leading-relaxed">
                        {translator("4. Submitting the exam is final. No changes can be made afterward.", "4. परीक्षा सबमिट करना अंतिम है। इसके बाद कोई परिवर्तन नहीं किया जा सकता।")}
                    </p>

                    <div className="flex items-center gap-3 mt-6">
                        <input
                            type="checkbox"
                            className="w-5 h-5 rounded border-gray-300 text-white focus:ring-0"
                            required
                        />
                        <span className="text-white">
                            {translator("I have read and understood the exam instructions.", "मैंने परीक्षा के निर्देश पढ़ लिए हैं और समझ लिया है।")}
                        </span>
                    </div>

                    <div className="flex justify-center mt-8">
                        <button
                            onClick={handleProceed}
                            className="bg-[#666666] text-white px-8 py-2.5 rounded-md hover:bg-[#555555] transition-colors"
                        >
                            {translator("Proceed", "जारी रखें")}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ExamIntro;