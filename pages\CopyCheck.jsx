import React, { useState, useEffect } from 'react';
import axios from 'axios';

// Translator function (assuming it's defined globally or imported)
const translator = (word1, word2) =>
  localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
    ? word1
    : localStorage.getItem("lang")
      ? word2
      : word1;

const QuestionIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const EditIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
  </svg>
);

const DeleteIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
);

const CloseIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const MarkOptionalModal = ({ setShowModal }) => {
  const [optionalRules, setOptionalRules] = useState([
    { section: 'A', total: 6, optional: 2 },
    { section: 'B', total: 8, optional: 4 },
  ]);

  const [newRule, setNewRule] = useState({
    section: '',
    total: '',
    required: '',
    optional: ''
  });

  const handleInputChange = (e, field) => {
    const value = e.target.value;
    setNewRule(prev => ({
      ...prev,
      [field]: value
    }));

    // Calculate optional when required or total changes
    if ((field === 'total' || field === 'required') && newRule.total && newRule.required) {
      const optionalValue = parseInt(newRule.total) - parseInt(newRule.required);
      if (!isNaN(optionalValue)) {
        setNewRule(prev => ({
          ...prev,
          optional: optionalValue.toString()
        }));
      }
    }
  };

  const addRule = () => {
    if (!newRule.section || !newRule.total || !newRule.required) {
      alert(translator('Please fill all fields', 'कृपया सभी फ़ील्ड भरें'));
      return;
    }

    const optionalValue = parseInt(newRule.total) - parseInt(newRule.required);
    if (isNaN(optionalValue) || optionalValue < 0) {
      alert(translator('Invalid numbers', 'अमान्य संख्याएँ'));
      return;
    }

    const ruleToAdd = {
      section: newRule.section,
      total: parseInt(newRule.total),
      optional: optionalValue
    };

    setOptionalRules([...optionalRules, ruleToAdd]);

    // Reset form
    setNewRule({
      section: '',
      total: '',
      required: '',
      optional: ''
    });
  };

  const deleteRule = (index) => {
    const updatedRules = [...optionalRules];
    updatedRules.splice(index, 1);
    setOptionalRules(updatedRules);
  };

  return (
    <div className="fixed inset-0 bg-opacity-30 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-2xl w-full max-w-4xl mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-800">{translator('Mark Optional Questions', 'वैकल्पिक प्रश्न चिह्नित करें')}</h2>
          <button onClick={() => setShowModal(false)} className="text-gray-500 hover:text-gray-800">
            <CloseIcon />
          </button>
        </div>

        <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
          <div className="flex flex-wrap items-end gap-4 mb-4">
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">{translator('Section', 'अनुभाग')}</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                value={newRule.section}
                onChange={(e) => handleInputChange(e, 'section')}
              >
                <option value="">{translator('Select Section', 'अनुभाग चुनें')}</option>
                <option value="A">A</option>
                <option value="B">B</option>
                <option value="C">C</option>
              </select>
            </div>
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">{translator('Total Questions', 'कुल प्रश्न')}</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                value={newRule.total}
                onChange={(e) => handleInputChange(e, 'total')}
              >
                <option value="">{translator('Number Of Questions', 'प्रश्नों की संख्या')}</option>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                  <option key={num} value={num}>{num}</option>
                ))}
              </select>
            </div>
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">{translator('Required', 'आवश्यक')}</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                value={newRule.required}
                onChange={(e) => handleInputChange(e, 'required')}
                disabled={!newRule.total}
              >
                <option value="">{translator('Must Attempt', 'अनिवार्य प्रयास')}</option>
                {newRule.total &&
                  Array.from({ length: parseInt(newRule.total) }, (_, i) => i + 1)
                    .map(num => (
                      <option key={num} value={num}>{num}</option>
                    ))
                }
              </select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">{translator('Optional', 'वैकल्पिक')}</label>
              <input
                type="text"
                value={newRule.optional || ''}
                readOnly
                className="w-full p-2 border border-gray-300 rounded-md bg-blue-50 text-blue-700 text-center font-medium shadow-sm"
              />
            </div>
            <button
              onClick={addRule}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md shadow-sm hover:bg-indigo-700 flex items-center h-10"
            >
              {translator('Add Question', 'प्रश्न जोड़ें')} <span className="ml-2 text-xl">+</span>
            </button>
          </div>

          {optionalRules.length > 0 && (
            <table className="w-full bg-white border border-gray-200 mt-4">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-2 px-4 border-b text-left text-sm font-semibold text-gray-600">{translator('Section', 'अनुभाग')}</th>
                  <th className="py-2 px-4 border-b text-left text-sm font-semibold text-gray-600">{translator('Total Questions', 'कुल प्रश्न')}</th>
                  <th className="py-2 px-4 border-b text-left text-sm font-semibold text-gray-600">{translator('Optional', 'वैकल्पिक')}</th>
                  <th className="py-2 px-4 border-b text-left text-sm font-semibold text-gray-600">{translator('Actions', 'कार्यवाई')}</th>
                </tr>
              </thead>
              <tbody>
                {optionalRules.map((rule, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b text-sm text-gray-700">{rule.section}</td>
                    <td className="py-2 px-4 border-b text-sm text-gray-700">{rule.total}</td>
                    <td className="py-2 px-4 border-b text-sm text-gray-700">{rule.optional}</td>
                    <td className="py-2 px-4 border-b text-sm text-gray-700">
                      <div className="flex space-x-2">
                        <button className="text-blue-500 hover:text-blue-700"><EditIcon /></button>
                        <button
                          onClick={() => deleteRule(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <DeleteIcon />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

const FileInput = ({ label, acceptedTypes, onFileChange, disabled = false }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState(null);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setIsUploading(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      onFileChange(response.data.filePath);
    } catch (error) {
      console.error('Error uploading file:', error);
      setError(translator('File upload failed', 'फ़ाइल अपलोड विफल'));
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="w-full">
      <label className="block text-sm font-medium text-blue-800 mb-2">{label}</label>
      <div className="flex items-center border border-gray-300 rounded-md p-2 bg-white">
        <label 
          htmlFor={`file-upload-${label.replace(/\s+/g, '-')}`} 
          className={`cursor-pointer bg-white text-gray-700 border border-gray-300 rounded-md px-4 py-1.5 text-sm hover:bg-gray-50 ${(isUploading || disabled) ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isUploading ? translator('Uploading...', 'अपलोड हो रहा है...') : translator('Choose File', 'फ़ाइल चुनें')}
        </label>
        <input 
          id={`file-upload-${label.replace(/\s+/g, '-')}`} 
          name={`file-upload-${label.replace(/\s+/g, '-')}`} 
          type="file" 
          className="sr-only" 
          onChange={handleFileChange}
          disabled={isUploading || disabled}
        />
        <span className={`ml-3 text-sm ${error ? 'text-red-500' : 'text-gray-500'}`}>
          {error || translator('No File Chosen', 'कोई फ़ाइल नहीं चुनी गई')}
        </span>
      </div>
      {acceptedTypes && <p className="text-xs text-gray-400 mt-1 flex items-center"><QuestionIcon /> {acceptedTypes}</p>}
    </div>
  );
};

const QuestionUploadComponent = () => {
  const [showModal, setShowModal] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [isOptionalMarked, setIsOptionalMarked] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState({
    questionSheet: null,
    answerSheets: []
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchQuestions = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get('/api/questions');
        // Ensure questions is always an array
        setQuestions(Array.isArray(response.data) ? response.data : []);
        setError(null);
      } catch (error) {
        console.error('Error fetching questions:', error);
        setError(translator('Failed to load questions', 'प्रश्न लोड करने में विफल'));
        // Set questions to empty array on error
        setQuestions([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchQuestions();
  }, []);

  const handleFileUpload = (type, filePath) => {
    if (type === 'questionSheet') {
      setUploadedFiles(prev => ({ ...prev, questionSheet: filePath }));
    } else {
      setUploadedFiles(prev => ({ ...prev, answerSheets: [...prev.answerSheets, filePath] }));
    }
  };

  const handleEditMarks = async () => {
    try {
      setIsLoading(true);
      const response = await axios.put('/api/questions/marks', {
        questions: [
          { section: 'A', number: 1, marks: 2 },
          { section: 'A', number: 2, marks: 7 },
          { section: 'B', number: 3, marks: 8 },
          { section: 'B', number: 4, marks: 5 },
          { section: 'C', number: 5, marks: 10 },
        ]
      });
      // Ensure updated questions is always an array
      setQuestions(Array.isArray(response.data?.updatedQuestions) ? response.data.updatedQuestions : []);
      setIsOptionalMarked(true);
      setError(null);
    } catch (error) {
      console.error('Error updating marks:', error);
      setError(translator('Failed to update marks', 'अंक अपडेट करने में विफल'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async (e) => {
    e.preventDefault();
    try {
      setIsLoading(true);
      const response = await axios.get('/api/answer-sheets', {
        params: { search: searchTerm }
      });
      // Handle search results (would need to implement this)
      console.log(response.data);
      setError(null);
    } catch (error) {
      console.error('Error searching:', error);
      setError(translator('Search failed', 'खोज विफल'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen w-full p-4">
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mx-auto w-full max-w-screen-2xl">
        {error && (
          <div className="mb-6 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8 w-full">
          <FileInput 
            label={translator('Upload Question Sheet', 'प्रश्न पत्र अपलोड करें')} 
            acceptedTypes="(.pdf, .ppt, .txt etc.)" 
            onFileChange={(filePath) => handleFileUpload('questionSheet', filePath)}
            disabled={isLoading}
          />
          <div>
            <FileInput 
              label={translator('Upload Answer Sheets', 'उत्तर पुस्तिकाएँ अपलोड करें')} 
              onFileChange={(filePath) => handleFileUpload('answerSheets', filePath)}
              disabled={isLoading}
            />
            <div className="mt-4 flex space-x-3">
              <button 
                className={`text-sm bg-white text-blue-600 border border-gray-300 px-4 py-1.5 rounded-md hover:bg-gray-50 shadow-sm ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={isLoading}
              >
                {translator('Add More', 'और जोड़ें')} <span className="ml-2 text-xl">+</span>
              </button>
              <button 
                className={`text-sm bg-white text-blue-600 border border-gray-300 px-4 py-1.5 rounded-md hover:bg-gray-50 shadow-sm ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={isLoading}
              >
                {translator('Use AI Copy Checking', 'एआई कॉपी जाँच का उपयोग करें')}
              </button>
            </div>
          </div>
        </div>

        <hr className="my-8" />

        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-800">{translator('List Of Questions', 'प्रश्नों की सूची')}</h2>
            <div className="flex space-x-3">
              {isOptionalMarked ? (
                <div className="flex items-center bg-gray-100 border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm">
                  {translator('2 Optional Question', '2 वैकल्पिक प्रश्न')}
                  <button
                    onClick={() => setShowModal(true)}
                    className="ml-2 text-gray-500 hover:text-gray-800"
                    disabled={isLoading}
                  >
                    <EditIcon />
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowModal(true)}
                  className={`text-sm bg-white text-gray-800 border border-gray-300 px-4 py-2 rounded-md hover:bg-gray-50 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={isLoading}
                >
                  {translator('Mark Optional Questions', 'वैकल्पिक प्रश्न चिह्नित करें')}
                </button>
              )}
              <button
                onClick={handleEditMarks}
                className={`text-sm text-white bg-purple-400 px-4 py-2 rounded-md hover:bg-purple-500 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={isLoading}
              >
                {isLoading ? translator('Updating...', 'अपडेट हो रहा है...') : translator('Edit Marks', 'अंक संपादित करें')}
              </button>
            </div>
          </div>

          {isLoading && questions.length === 0 ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full bg-white border border-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="py-3 px-6 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{translator('Section', 'अनुभाग')}</th>
                    <th className="py-3 px-6 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{translator('Question Number', 'प्रश्न संख्या')}</th>
                    <th className="py-3 px-6 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{translator('Total Marks', 'कुल अंक')}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {questions && questions.length > 0 ? (
                    questions.map((q, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm text-gray-900">{q.section}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{q.number}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{q.marks}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="3" className="py-4 text-center text-sm text-gray-500">
                        {translator('No questions found', 'कोई प्रश्न नहीं मिला')}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="pt-6 border-t border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">{translator('Students Answer Sheet Upload', 'छात्रों की उत्तर पुस्तिका अपलोड करें')}</h2>
          <div className="flex justify-end">
            <form onSubmit={handleSearch} className="relative w-full max-w-md">
              <input
                type="text"
                placeholder={translator('Search', 'खोजें')}
                className="w-full p-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isLoading}
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
            </form>
          </div>
        </div>
      </div>

      {showModal && <MarkOptionalModal setShowModal={setShowModal} />}
    </div>
  );
};

export default QuestionUploadComponent;