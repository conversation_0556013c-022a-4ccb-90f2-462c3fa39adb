import React, { useState, useEffect } from "react";
import NavBar from "./NavBar";
import { useLocation } from 'react-router-dom';

const translator = (word1, word2) =>
  localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
    ? word1
    : localStorage.getItem("lang")
      ? word2
      : word1;

const ExamQuestion = () => {
  const [timeLeft, setTimeLeft] = useState({ minutes: 0, seconds: 0 });
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [answers, setAnswers] = useState({});
  const [markedQuestions, setMarkedQuestions] = useState([]);
  const [attemptedQuestions, setAttemptedQuestions] = useState([]);
  const [showResult, setShowResult] = useState(false);
  const [score, setScore] = useState(0);
  const [wrongAnswers, setWrongAnswers] = useState([]);

  const location = useLocation();
  const { questions = [], time = "30" } = location.state || {};

  // Timer
  useEffect(() => {
    const mins = parseInt(time);
    setTimeLeft({ minutes: mins, seconds: 0 });

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { minutes: prev.minutes - 1, seconds: 59 };
        }
        clearInterval(timer);
        return prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [time]);

  const selectedAnswer = answers[currentQuestion] || "";

  const handleAnswerChange = (value) => {
    setAnswers((prev) => ({
      ...prev,
      [currentQuestion]: value
    }));

    if (!attemptedQuestions.includes(currentQuestion)) {
      setAttemptedQuestions((prev) => [...prev, currentQuestion]);
    }
  };

  const handleNext = () => {
    if (currentQuestion < questions.length) {
      setCurrentQuestion((prev) => prev + 1);
    }
  };

  const handleMarkForReview = () => {
    setMarkedQuestions((prev) =>
      prev.includes(currentQuestion)
        ? prev.filter((q) => q !== currentQuestion)
        : [...prev, currentQuestion]
    );
  };

  const handleQuestionClick = (questionNumber) => {
    setCurrentQuestion(questionNumber);
  };

  const handleSubmit = () => {
    let correct = 0;
    const wrong = [];

    questions.forEach((question, index) => {
      const selected = answers[index + 1]; // answers are 1-indexed
      const correctOption = question.options["abcd".indexOf(question.correct)];

      if (selected === correctOption) {
        correct += 1;
      } else {
        wrong.push({
          number: index + 1,
          question: question.Question,
          selected,
          correct: correctOption
        });
      }
    });

    setScore(correct);
    setWrongAnswers(wrong);
    setShowResult(true);
  };

  return (
    <div className="min-h-screen bg-white">
      <NavBar />

      <div className="max-w-6xl mx-auto mt-8 p-6">
        <h1 className="text-4xl font-bold text-purple-800 mb-4">
          {translator("All The Best!", "सभी को शुभकामनाएँ!")}
        </h1>

        <div className="flex justify-between items-start mb-8">
          <p className="text-xl text-purple-800 mb-2">
            {translator("Choose the correct option, and submit your exam after reviewing all questions.", "सही विकल्प चुनें, और सभी प्रश्नों की समीक्षा करने के बाद अपनी परीक्षा सबमिट करें.")}
          </p>
          <div>

          </div>
        </div>

        <div className="grid grid-cols-3 gap-8">
          {/* Question Section */}
          <div className="col-span-2">
            <div className="bg-[#4B5BA6] text-white p-6 rounded-t-lg">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl">
                  {translator("Question", "प्रश्न")}
                  {currentQuestion}</h2>
                <span>{currentQuestion}/{questions.length}</span>
              </div>
            </div>

            <div className="border rounded-b-lg p-6">
              <p className="text-lg mb-6">
                {questions[currentQuestion - 1]?.Question || "No question found."}
              </p>

              <div className="space-y-4">
                {questions[currentQuestion - 1]?.options?.map((option, idx) => (
                  <label key={idx} className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name={`answer-${currentQuestion}`}
                      value={option}
                      checked={selectedAnswer === option}
                      onChange={(e) => handleAnswerChange(e.target.value)}
                      className="w-4 h-4"
                      disabled={showResult}
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>

              {!showResult && (
                <div className="flex gap-4 mt-8">
                  <button
                    onClick={handleNext}
                    className="bg-[#4B5BA6] text-white px-6 py-2 rounded"
                  >
                    {translator("Skip Next", "अगला छोड़ें")}
                  </button>
                  <button
                    onClick={handleMarkForReview}
                    className="bg-[#B87503] text-white px-6 py-2 rounded"
                  >
                    {markedQuestions.includes(currentQuestion) ? translator("Unmark", "अनचाहें") : translator("Mark for Review", "समीक्षा के लिए चिह्नित करें")}
                  </button>
                  <button
                    onClick={handleSubmit}
                    className="bg-[#0F7B5B] text-white px-6 py-2 rounded"
                  >
                    {translator("Submit Next", "अगला सबमिट करें")}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-span-1">
            <div className="bg-white rounded-lg p-6 border">
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">
                  {translator("Time Left", "समय शेष")}
                </h3>
                <div className="text-3xl font-bold text-[#0F7B5B]">
                  {String(timeLeft.minutes).padStart(2, "0")}:
                  {String(timeLeft.seconds).padStart(2, "0")}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2">
                  {translator("Attempted:", "प्रयास किए गए:")}
                  {attemptedQuestions.length}/{questions.length}
                </h3>
              </div>

              <div className="grid grid-cols-6 gap-2">
                {questions.map((_, index) => {
                  const number = index + 1;
                  const isCurrent = currentQuestion === number;
                  const isMarked = markedQuestions.includes(number);
                  const isAttempted = attemptedQuestions.includes(number);

                  let bgColor = "bg-gray-200";
                  if (isCurrent) bgColor = "bg-blue-500 text-white";
                  else if (isMarked) bgColor = "bg-yellow-600 text-white";
                  else if (isAttempted) bgColor = "bg-green-600 text-white";

                  return (
                    <button
                      key={number}
                      onClick={() => handleQuestionClick(number)}
                      className={`w-8 h-8 rounded ${bgColor}`}
                    >
                      {number}
                    </button>
                  );
                })}
              </div>

              {!showResult && (
                <button
                  onClick={handleSubmit}
                  className="w-full bg-blue-600 text-white py-3 rounded mt-6"
                >
                  {translator("Confirm and Submit", "पुष्टि करें और सबमिट करें")}
                </button>
              )}
            </div>

            {/* Result Display */}
            {showResult && (
              <div className="mt-6 p-4 border rounded bg-gray-50">
                <h2 className="text-2xl font-bold text-green-700 mb-2">
                  {translator("You scored", "आपने स्कोर किया")}
                  {score}
                  {translator("out of", "में")}
                  {questions.length}
                </h2>

                {wrongAnswers.length > 0 && (
                  <>
                    <h3 className="text-xl font-semibold text-red-600 mt-4 mb-2">
                      {translator("Incorrect Questions:", "गलत प्रश्न:")}
                    </h3>
                    <ul className="list-disc pl-5 text-red-700 space-y-2">
                      {wrongAnswers.map((item, idx) => (
                        <li key={idx}>
                          <strong>Q{item.number}:</strong> {item.question}
                          <br />
                          {translator("Your answer:", "आपका उत्तर:")}{" "}
                          <span className="text-red-600">
                            {item.selected || "No answer"}
                          </span>{" "}
                          | Correct answer:{" "}
                          <span className="text-green-600">{item.correct}</span>
                        </li>
                      ))}
                    </ul>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamQuestion;
