import React, { useState } from "react";

const Step2TimelineJS = ({ onSubmit, onBack, initialData, loading }) => {
  const [timelineData, setTimelineData] = useState(
    initialData.subjects.map(subject => ({
      startDate: "",
      endDate: "",
      numberOfDays: 14,
      dailyMinutes: 30,
      holidays: false
    }))
  );
  const [error, setError] = useState("");

  const handleInputChange = (index, field, value) => {
    const newData = [...timelineData];
    newData[index] = { ...newData[index], [field]: value };
    setTimelineData(newData);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all timelines
    const hasEmptyFields = timelineData.some(item => 
      !item.startDate || !item.endDate || !item.numberOfDays || !item.dailyMinutes
    );
    
    if (hasEmptyFields) {
      setError("Please fill in all timeline fields for each subject");
      return;
    }

    setError("");
    onSubmit(timelineData);
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md mt-8">
      <h2 className="text-2xl font-bold mb-6">Set Your Study Timeline</h2>
      <form onSubmit={handleSubmit}>
        {initialData.subjects.map((subject, index) => (
          <div key={index} className="mb-8 border-b pb-6">
            <h3 className="text-lg font-semibold mb-4">
              Timeline for {subject.subject} - {subject.topic}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date
                </label>
                <input
                  type="date"
                  value={timelineData[index].startDate}
                  onChange={(e) => handleInputChange(index, "startDate", e.target.value)}
                  className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Date
                </label>
                <input
                  type="date"
                  value={timelineData[index].endDate}
                  onChange={(e) => handleInputChange(index, "endDate", e.target.value)}
                  className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Number of Days
                </label>
                <input
                  type="number"
                  min="1"
                  value={timelineData[index].numberOfDays}
                  onChange={(e) => handleInputChange(index, "numberOfDays", parseInt(e.target.value))}
                  className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Daily Study Minutes
                </label>
                <input
                  type="number"
                  min="15"
                  step="15"
                  value={timelineData[index].dailyMinutes}
                  onChange={(e) => handleInputChange(index, "dailyMinutes", parseInt(e.target.value))}
                  className="w-full p-3 border rounded-md text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="md:col-span-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={timelineData[index].holidays}
                    onChange={(e) => handleInputChange(index, "holidays", e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Include holidays in the study plan
                  </span>
                </label>
              </div>
            </div>
          </div>
        ))}

        {error && (
          <div className="text-red-500 text-sm mb-4">{error}</div>
        )}

        <div className="flex justify-between">
          <button
            type="button"
            onClick={onBack}
            className="bg-gray-200 text-gray-700 px-8 py-3 rounded-md hover:bg-gray-300 
              transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Back
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-500 text-white px-8 py-3 rounded-md hover:bg-blue-600 
              transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? "Generating Plan..." : "Generate Plan"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default Step2TimelineJS;
