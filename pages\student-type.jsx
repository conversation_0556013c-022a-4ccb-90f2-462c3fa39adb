"use client"

import { useNavigate } from "react-router-dom"
import "./sign.css"
import {useLocation} from "react-router-dom"
const StudentType = () => {
  const router = useNavigate()
const location = useLocation()

  const { name, email, mobile, password,board,language } = location.state || {}
  const handleBack = () => {
    router("/education-details")
  }

  const handleContinue = (type) => {
    // Store the selected type
    if (typeof window !== "undefined") {
      localStorage.setItem("studentType", type)
    }
    // Navigate to next page
    router("/class-stream",{state:{name:name,email:email,mobile:mobile,password:password,board:board,language:language,studentType:type}})
  }

  return (
    <div className="container1">
      <div className="form-container">
        <div className="form-content">
          <h1 className="form-title">What Kind Of Student Are You ?</h1>
          <p className="form-subtitle">Help us personalize your learning experience.</p>

          <div className="student-type-grid">
            <div className="student-type-card" onClick={() => handleContinue("coaching")}>
              <div className="icon-circle">
                <div className="icon coaching-icon">
                <img src="/coaching.png" alt="Coaching Icon" />
                </div>
              </div>
              <span>Coaching Institute</span>
            </div>

            <div className="student-type-card" onClick={() => handleContinue("school")}>
              <div className="icon-circle">
                <div className="icon school-icon">
                <img src="/school.png" alt="Coaching Icon" />

                </div>
              </div>
              <span>School</span>
            </div>

            <div className="student-type-card" onClick={() => handleContinue("independent")}>
              <div className="icon-circle">
                <div className="icon independent-icon">
                <img src="/independent.png" alt="Coaching Icon" />

                </div>
              </div>
              <span>Independent Learner</span>
            </div>

            <div className="student-type-card" onClick={() => handleContinue("college")}>
              <div className="icon-circle">
                <div className="icon college-icon">
                <img src="/college.png" alt="Coaching Icon" />
                </div>
              </div>
              <span>College</span>
            </div>
          </div>

          <div className="button-group">
            <button className="btn-secondary" onClick={handleBack}>
              Back
            </button>
            <button className="btn-primary" onClick={() => handleContinue("default")}>
              Continue
            </button>
          </div>
        </div>
      </div>

      <div className="info-container">
        <div className="logo-container">
          <div className="logo">
          <img src="/logo.png" alt="AI Classroom Logo" height={30} width={30} />

            <span>AI Classroom</span>
          </div>
        </div>

        <div className="illustration">
          <img src="/images/student-illustration.png" alt="Students learning" />
        </div>

        <div className="welcome-text">
          <h2>Welcome to AI Classroom!</h2>
          <p>Let's set up your personalized AI learning experience. It only takes 2 minutes!</p>
        </div>
      </div>
    </div>
  )
}

export default StudentType
