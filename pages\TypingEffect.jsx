import { useState, useEffect } from "react";

const TypingEffect = ({ content, duration }) => {
  const [displayedContent, setDisplayedContent] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  // Calculate base delay with some randomness
  const totalCharacters = content.length;
  const baseDelay = (duration * 1000) / totalCharacters;

  useEffect(() => {
    if (currentIndex < totalCharacters) {
      // Add some randomness to make it more human-like
      // Faster for some characters, slower for others
      const randomFactor = 0.7 + Math.random() * 0.6; // Between 0.7 and 1.3
      const delay = baseDelay * randomFactor;

      // Occasionally add a longer pause (like human typing)
      const occasionalPause = Math.random() < 0.05 ? baseDelay * 5 : 0;

      const timeoutId = setTimeout(() => {
        setDisplayedContent((prev) => prev + content[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, delay + occasionalPause);

      return () => clearTimeout(timeoutId);
    }
  }, [currentIndex, content, baseDelay, totalCharacters]);

  // Reset when content changes
  useEffect(() => {
    setDisplayedContent("");
    setCurrentIndex(0);
  }, [content]);

  return (
    <div className="text-white text-xl font-bold leading-relaxed relative">
      {displayedContent}
      {/* Blinking cursor */}
      {currentIndex < totalCharacters ? (
        <span className="animate-blink ml-1 border-r-2 border-gray-700 font-bold">
          |
        </span>
      ) : (
        <span className="animate-blink ml-1 border-r-2 border-gray-700 font-bold">
        |
      </span>      )}
    </div>
  );
};

export default TypingEffect;
