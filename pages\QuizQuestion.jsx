import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";

const QuizQuestion = () => {
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30 * 60); // 30 minutes
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [userAnswers, setUserAnswers] = useState({});
  const [bannerContest, setBannerContest] = useState();
  const [userData, setUserData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [pageLoadTime, setPageLoadTime] = useState(null);

  const [tabSwitchCount, setTabSwitchCount] = useState(0);
  const [cheatingDetected, setCheatingDetected] = useState(false);

  const navigate = useNavigate();

  // Ref for video element
  const videoRef = useRef(null);
  const [cameraEnabled, setCameraEnabled] = useState(false);

  useEffect(() => {
    setPageLoadTime(new Date());
    const storedUserData = localStorage.getItem("user-data");
    if (storedUserData) {
      try {
        const parsedUserData = JSON.parse(storedUserData);
        setUserData(parsedUserData);
      } catch (err) {
        console.error("Error parsing user data", err);
      }
    }

    const fetchBannerContest = async () => {
      try {
        const response = await axios.get("https://api.aiclassroom.in/contest");
        const data = response?.data?.data;

        // Shuffle questions
        if (data?.questions) {
          data.questions = shuffleArray(data.questions);
        }

        setBannerContest(data);
        setTimeLeft(data?.duration / 1000 || 30 * 60); // Default to 30 minutes if duration is not provided
        setLoading(false);
      } catch (err) {
        setError("Failed to load contest");
        setLoading(false);
      }
    };

    fetchBannerContest();

    // Start camera
    const startCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          setCameraEnabled(true);
        }
      } catch (err) {
        console.error("Error accessing the camera:", err);
        setCameraEnabled(false);
      }
    };

    startCamera();

    return () => {
      // Cleanup on unmount
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject;
        const tracks = stream.getTracks();
        tracks.forEach(track => track.stop());
      }
    };
  }, []);

  // Shuffle questions
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Countdown Timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          handleSubmit(); // Auto-submit when time is up
          return 0;
        }
        
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Detect Tab Switching
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        setTabSwitchCount((prev) => prev + 1);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  useEffect(() => {
    if (tabSwitchCount === 1) {
      alert("Warning: Switching tabs is not allowed. Next time your exam will be submitted!");
    }
    if (tabSwitchCount >= 2 && !cheatingDetected) {
      setCheatingDetected(true);
      handleCheatingSubmit();
    }
  }, [tabSwitchCount]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleMCQSelect = (questionIndex, answerIndex) => {
    setUserAnswers((prev) => ({
      ...prev,
      [questionIndex]: answerIndex,
    }));
  };

  const handleWrittenAnswerChange = (questionIndex, text) => {
    setUserAnswers((prev) => ({
      ...prev,
      [questionIndex]: text,
    }));
  };

  const calculateScore = () => {
    if (!bannerContest?.questions) return 0;
    let score = 0;

    bannerContest.questions.forEach((question, index) => {
      if (question.type === "mcq") {
        if (userAnswers[index] === question.correct) {
          score += 1;
        }
      }
    });

    return score;
  };

  const handleSubmit = async () => {
    const submissionTime = new Date();
    const timeTakenInSeconds = Math.floor((submissionTime - pageLoadTime) / 1000);
    const score = calculateScore();

    const submissionData = {
      score,
      timeTaken: timeTakenInSeconds,
      answers: userAnswers,
    };

    try {
      await axios.post(
        `https://api.aiclassroom.in/contest/${bannerContest._id}/submit`,
        {
          email: userData.email,
          submission: submissionData,
        }
      );
      alert("Quiz submitted successfully!");
      setShowSubmitModal(false);
      navigate("/contest");
    } catch (err) {
      setError("Failed to submit quiz");
      console.error(err);
    }
  };

  const handleCheatingSubmit = async () => {
    const submissionTime = new Date();
    const timeTakenInSeconds = Math.floor((submissionTime - pageLoadTime) / 1000);

    const cheatingAnswers = {};
    if (bannerContest?.questions) {
      bannerContest.questions.forEach((_, index) => {
        cheatingAnswers[index] = "Cheated";
      });
    }

    const submissionData = {
      score: 0,
      timeTaken: timeTakenInSeconds,
      answers: cheatingAnswers,
      cheated: true,
    };
    alert("Cheating detected! Your exam will be auto-submitted.");
    try {
      await axios.post(
        `https://api.aiclassroom.in/contest/${bannerContest._id}/submit`,
        {
          email: userData.email,
          submission: submissionData,
        }
      );
      alert("Exam submitted due to tab switch violation.");
      setTimeout(() => {
        navigate("/contest");
      }, 2000);
    } catch (err) {
      setError("Failed to auto-submit quiz");
      console.error(err);
    }
  };

  const handleSubmitQuiz = () => {
    if (!userData?.email) {
      setError("User email not found");
      return;
    }
    setShowSubmitModal(true);

  };

  if (loading) return <div className="p-6 text-center">Loading...</div>;

  const currentQ = bannerContest?.questions[currentQuestion - 1];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-bold">{bannerContest?.title || "Quiz"}</h1>
            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
              <span>{bannerContest?.questions?.length || 10} Questions</span>
              <span>•</span>
              <span>{formatTime(timeLeft)}</span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm">{formatTime(timeLeft)}</div>
            <button onClick={handleSubmitQuiz} className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
              Submit Quiz
            </button>
          </div>
        </div>
      </div>

      {/* Camera View */}
      {cameraEnabled && (
        <div className="fixed top-6 right-6 bg-white p-2 rounded-lg shadow-lg">
          <video
            ref={videoRef}
            autoPlay
            muted
            width="150"
            height="150"
            style={{ borderRadius: "8px" }}
          />
        </div>
      )}

      {/* Question Card */}
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-3">
            <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm">
              Question {currentQuestion}/{bannerContest?.questions?.length || 10}
            </span>
           
          </div>
        </div>

        {currentQ && (
          <div className="space-y-6">
            <h2 className="text-lg font-medium">{currentQ.title}</h2>

            {currentQ.type === "mcq" ? (
              <div className="space-y-3">
                {currentQ.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleMCQSelect(currentQuestion - 1, index)}
                    className={`w-full text-left p-4 rounded-lg border ${
                      userAnswers[currentQuestion - 1] === index
                        ? "border-indigo-500 bg-indigo-50"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            ) : (
              <textarea
                value={userAnswers[currentQuestion - 1] || ""}
                onChange={(e) => handleWrittenAnswerChange(currentQuestion - 1, e.target.value)}
                className="w-full p-4 border border-gray-300 rounded-lg"
                rows={5}
                placeholder="Write your answer here..."
              />
            )}
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <button
            onClick={() => setCurrentQuestion(Math.max(1, currentQuestion - 1))}
            className="text-indigo-600 hover:text-indigo-700 flex items-center gap-2"
            disabled={currentQuestion === 1}
          >
            ← Previous
          </button>
          <button
            onClick={() => setCurrentQuestion(Math.min(bannerContest?.questions?.length || 10, currentQuestion + 1))}
            className="text-indigo-600 hover:text-indigo-700 flex items-center gap-2"
            disabled={currentQuestion === (bannerContest?.questions?.length || 10)}
          >
            Next →
          </button>
        </div>
      </div>

      {/* Submit Modal */}
      {showSubmitModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-semibold mb-4 text-center">Confirm Submission</h2>
            <div className="flex gap-4">
              <button
                onClick={() => setShowSubmitModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Continue Quiz
              </button>
              <button
                onClick={handleSubmit}
                className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                Submit Quiz
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
    </div>
  );
};

export default QuizQuestion;
