import { useLanguage } from "../context/LanguageContext"
import { getTranslations } from "../translations"

export function useTranslation() {
  const { language } = useLanguage()
  const translations = getTranslations(language)

  // Function to get a nested translation by key path
  const t = (keyPath) => {
    const keys = keyPath.split(".")
    let value = translations

    for (const key of keys) {
      if (value[key] === undefined) {
        console.warn(`Translation key "${keyPath}" not found`)
        return keyPath
      }
      value = value[key]
    }

    return value
  }

  return { t, language }
}
export default useTranslation;
