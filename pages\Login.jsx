import { useState } from "react";
import { useNavigate } from "react-router-dom";

const Login = ({ setModel }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [forgotPasswordMode, setForgotPasswordMode] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [ogotp,setOgotp]=useState("")

  const navigate = useNavigate();

  const handleClose = (e) => {
    e.preventDefault();
    setModel(false);
  };

  const handleLogin = (e) => {
    e.preventDefault();
    setIsLoading(true);

    fetch("https://api.aiclassroom.in/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    })
      .then((res) => res.json())
      .then((data) => {
        if (!data.token) throw new Error("Login failed");

        localStorage.setItem("auth-token", data.token);
        localStorage.setItem(
          "user-data",
          JSON.stringify({
            userId: data.user._id,
            email: data.email,
            firstName: data.user.firstName,
            avatar: data.user.avatar,
            coins: data.user.coins,
            totalCoins: data.user.totalCoins,
          })
        );
        window.location.reload();
      })
      .catch((err) => {
        console.error(err);
        alert("Invalid login credentials.");
      })
      .finally(() => setIsLoading(false));
  };

  const handleSendOtp = async (e) => {
    e.preventDefault();
    if (!email) return alert("Please enter your email");
    // Generate a random 4-digit OTP (for demo/testing only; real OTP should come from backend)
    const generatedOtp = Math.floor(1000 + Math.random() * 9000).toString();
    setOgotp(generatedOtp);
    const res = await fetch("https://api.aiclassroom.in/sent-otp", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email ,otp:generatedOtp}),
    });

    if (res.ok) {
      setOtpSent(true);
      alert("OTP sent to your email.");
    } else {
      alert("Failed to send OTP.");
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      return alert("Passwords do not match.");
    }
if(otp!=ogotp){
  alert("Otp didnot match");
  return;
}
    const res = await fetch("https://api.aiclassroom.in/reset-password", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password:newPassword }),
    });

    if (res.ok) {
      alert("Password reset successful!");
      setForgotPasswordMode(false);
      setOtpSent(false);
      setEmail("");
      setOtp("");
      setNewPassword("");
      setConfirmPassword("");
    } else {
      alert("Invalid OTP or error resetting password.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-md flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md mx-4 z-50">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">
            {forgotPasswordMode ? "Reset Password" : "Login"}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {!forgotPasswordMode ? (
          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <div className="text-right">
              <button
                type="button"
                className="text-sm text-purple-600 hover:underline"
                onClick={() => setForgotPasswordMode(true)}
              >
                Forgot Password?
              </button>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md"
            >
              {isLoading ? "Logging in..." : "Login"}
            </button>
          </form>
        ) : (
          <form onSubmit={otpSent ? handleResetPassword : handleSendOtp} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>

            {otpSent && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">OTP</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                  <input
                    type="password"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                  <input
                    type="password"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                  />
                </div>
              </>
            )}

            <button
              type="submit"
              className="w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md"
            >
              {otpSent ? "Reset Password" : "Send OTP"}
            </button>

            <div className="text-sm text-center">
              <button
                type="button"
                onClick={() => {
                  setForgotPasswordMode(false);
                  setOtpSent(false);
                  setOtp("");
                  setNewPassword("");
                  setConfirmPassword("");
                }}
                className="text-gray-500 hover:text-purple-600 mt-2"
              >
                Back to Login
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default Login;
