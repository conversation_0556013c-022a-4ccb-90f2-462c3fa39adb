.planner-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .planner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .planner-header h2 {
    font-size: 24px;
    font-weight: 600;
  }
  
  .planner-tabs {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .tab {
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    transition: background-color 0.2s, color 0.2s;
  }
  
  .tab:hover {
    background-color: var(--bg-secondary);
  }
  
  .tab.active {
    background-color: var(--primary-color);
    color: white;
  }
  
  .active-plan-card {
    background-color: white;
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-sm);
  }
  
  .plan-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
  }
  
  .plan-date {
    color: var(--text-secondary);
    margin-bottom: 24px;
  }
  
  .progress-section {
    margin-bottom: 24px;
  }
  
  .progress-section h4 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
  }
  
  .progress-bar-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .progress-bar {
    flex: 1;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
  }
  
  .progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 4px;
  }
  
  .progress-percentage {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
  }
  
  .tasks-section h4 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  .tasks-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .task-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
  }
  
  .task-indicator {
    width: 4px;
    height: 40px;
    border-radius: 2px;
    margin-right: 16px;
  }
  
  .video-indicator {
    background-color: var(--primary-color);
  }
  
  .assessment-indicator {
    background-color: var(--secondary-color);
  }
  
  .reading-indicator {
    background-color: var(--success-color);
  }
  
  .practice-indicator {
    background-color: var(--warning-color);
  }
  
  .task-content {
    flex: 1;
  }
  
  .task-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .task-time {
    color: var(--text-tertiary);
    font-size: 14px;
  }
  
  .task-type {
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 500;
  }
  
  .video-type {
    background-color: var(--primary-light);
    color: var(--primary-color);
  }
  
  .assessment-type {
    background-color: var(--secondary-light);
    color: var(--secondary-color);
  }
  
  .reading-type {
    background-color: var(--success-light);
    color: var(--success-color);
  }
  
  .practice-type {
    background-color: var(--warning-light);
    color: var(--warning-color);
  }
  
  @media (max-width: 768px) {
    .task-item {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .task-indicator {
      width: 100%;
      height: 4px;
      margin-right: 0;
      margin-bottom: 12px;
    }
  
    .task-type {
      margin-top: 12px;
      align-self: flex-start;
    }
  }
  