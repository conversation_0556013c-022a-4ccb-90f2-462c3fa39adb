"use client"

import { useState } from "react"
import { useNavigate } from "react-router-dom"
import "./sign.css"
import { useLocation } from "react-router-dom"

const ClassStream = () => {
  const router = useNavigate()
  const location = useLocation()

  const { name, email, mobile, password, board, language, studentType } = location.state || {}
  const [formData, setFormData] = useState({
    class: "",
    stream: "",
    subjects: [],
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }

  const handleStreamSelect = (stream) => {
    setFormData({
      ...formData,
      stream,
    })
  }

  const handleSubjectToggle = (subject) => {
    const subjects = [...formData.subjects]
    if (subjects.includes(subject)) {
      const index = subjects.indexOf(subject)
      subjects.splice(index, 1)
    } else {
      subjects.push(subject)
    }
    setFormData({
      ...formData,
      subjects,
    })
  }

  const handleBack = () => {
    router("/student-type")
  }

  const handleContinue = async() => {
    // Navigate to next page
    const payload = {
      email,
      firstName: name.split(" ")[0],
      lastName: name.split(" ")[1],
      password,
      accounttype: studentType,
      signinwithgoogle: false,
    };
    try {
      const response = await fetch("https://api.aiclassroom.in/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      console.log("id", data);
      if (response.ok) {

        setTimeout(()=>{fetch("https://api.aiclassroom.in/login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({email, password}),
        })
          .then((response) => response.json())
          .then((data1) => {
            console.log("user data", data1.user);
            console.log("user Name", data1.user.firstName);
            localStorage.setItem("auth-token", data1.token);
            localStorage.setItem(
              "user-data",
              JSON.stringify({
                userId: data1.user._id,
                email: data1.email,
                firstName: data1.user.firstName,
                avatar: data1.user.avatar,
                coins: data1.user.coins,
                totalCoins: data1.user.totalCoins,
              })
            );
            router("/dashboard",{state:{name:name,email:email,mobile:mobile,password:password,board:board,language:language,studentType:studentType,class:formData.class,stream:formData.stream,subjects:formData.subjects}})
            // Reload the page to reflect the new user state
          },1000)
          })
        localStorage.setItem("auth-token", data.token);
      } else {
        alert(data.message || "An error occurred during sign up.");
      }
    } catch (error) {
      console.error("Error during sign up:", error);
      alert("An error occurred. Please try again.");
    
  };
    router("/dashboard",{state:{name:name,email:email,mobile:mobile,password:password,board:board,language:language,studentType:studentType,class:formData.class,stream:formData.stream,subjects:formData.subjects}})
  }

  return (
    <div className="container1">
      <div className="form-container">
        <div className="form-content">
          <h1 className="form-title">Your Class & Stream</h1>
          <p className="form-subtitle">Select your current class and academic stream</p>

          <div className="form-group mt-40">
            <label>Select Your Class</label>
            <div className="select-wrapper">
              <select name="class" value={formData.class} onChange={handleChange}>
                <option value="" disabled selected>
                  Choose Class
                </option>
                <option value="9">Class 9</option>
                <option value="10">Class 10</option>
                <option value="11">Class 11</option>
                <option value="12">Class 12</option>
              </select>
            </div>
          </div>

          <div className="form-group mt-40">
            <label>Select Stream</label>
            <div className="stream-options">
              <div
                className={`stream-option ${formData.stream === "science" ? "selected" : ""}`}
                onClick={() => handleStreamSelect("science")}
              >
                Science
              </div>
              <div
                className={`stream-option ${formData.stream === "commerce" ? "selected" : ""}`}
                onClick={() => handleStreamSelect("commerce")}
              >
                Commerce
              </div>
              <div
                className={`stream-option ${formData.stream === "arts" ? "selected" : ""}`}
                onClick={() => handleStreamSelect("arts")}
              >
                Arts
              </div>
            </div>
          </div>

          <div className="form-group mt-40">
            <label>
              Subject Interests <span className="optional">( Optional )</span>
            </label>
            <div className="subject-options">
              <div
                className={`subject-option ${formData.subjects.includes("physics") ? "selected" : ""}`}
                onClick={() => handleSubjectToggle("physics")}
              >
                Physics
              </div>
              <div
                className={`subject-option ${formData.subjects.includes("maths") ? "selected" : ""}`}
                onClick={() => handleSubjectToggle("maths")}
              >
                Maths
              </div>
              <div
                className={`subject-option ${formData.subjects.includes("chemistry") ? "selected" : ""}`}
                onClick={() => handleSubjectToggle("chemistry")}
              >
                Chemistry
              </div>
              <div
                className={`subject-option ${formData.subjects.includes("english") ? "selected" : ""}`}
                onClick={() => handleSubjectToggle("english")}
              >
                English
              </div>
            </div>
          </div>

          <div className="button-group">
            <button className="btn-secondary" onClick={handleBack}>
              Back
            </button>
            <button className="btn-primary" onClick={handleContinue}>
              Continue
            </button>
          </div>
        </div>
      </div>

      <div className="info-container">
        <div className="logo-container">
          <div className="logo">
            <div className="logo-icon"></div>
            <span>AI Classroom</span>
          </div>
        </div>

        <div className="illustration">
          <img src="/images/class-stream-illustration.png" alt="Online learning" />
        </div>

        <div className="welcome-text">
          <h2>Welcome to AI Classroom!</h2>
          <p>Let's set up your personalized AI learning experience. It only takes 2 minutes!</p>
        </div>
      </div>
    </div>
  )
}

export default ClassStream
