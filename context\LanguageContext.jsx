"use client"

import { createContext, useContext, useState, useEffect } from "react"

// Define available languages
export const languages = {
  ENGLISH: "English",
  HINDI: "Hindi",
}

// Create the context
const LanguageContext = createContext()

// Create a provider component
export function LanguageProvider({ children }) {
  // Initialize with browser language or default to English
  const [language, setLanguage] = useState(languages.ENGLISH)

  // Load saved language preference from localStorage on initial render
  useEffect(() => {
    const savedLanguage = localStorage.getItem("language")
    if (savedLanguage && Object.values(languages).includes(savedLanguage)) {
      setLanguage(savedLanguage)
    }
  }, [])

  // Save language preference to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("language", language)
  }, [language])

  // Function to toggle or set language
  const toggleLanguage = (lang) => {
    if (lang) {
      setLanguage(lang)
    } else {
      setLanguage(language === languages.ENGLISH ? languages.HINDI : languages.ENGLISH)
    }
  }

  return <LanguageContext.Provider value={{ language, toggleLanguage }}>{children}</LanguageContext.Provider>
}

// Custom hook to use the language context
export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
