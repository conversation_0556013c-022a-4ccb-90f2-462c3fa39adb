"use client"

function RewardsNavigation({ activeTab, setActiveTab }) {
  return (
    <div className="flex justify-between p-2">
      <button
        onClick={() => setActiveTab("overview")}
        className={`flex items-center justify-center gap-2 py-2 px-4 rounded-full transition-colors ${
          activeTab === "overview" ? "bg-white/20 text-white" : "text-white/80 hover:bg-white/10"
        }`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
          />
        </svg>
        <span>Overview</span>
      </button>

      <button
        onClick={() => setActiveTab("redeem")}
        className={`flex items-center justify-center gap-2 py-2 px-4 rounded-full transition-colors ${
          activeTab === "redeem" ? "bg-white/20 text-white" : "text-white/80 hover:bg-white/10"
        }`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
          />
        </svg>
        <span>Redeem</span>
      </button>

      <button
        onClick={() => setActiveTab("history")}
        className={`flex items-center justify-center gap-2 py-2 px-4 rounded-full transition-colors ${
          activeTab === "history" ? "bg-white/20 text-white" : "text-white/80 hover:bg-white/10"
        }`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span>History</span>
      </button>
    </div>
  )
}

export default RewardsNavigation
