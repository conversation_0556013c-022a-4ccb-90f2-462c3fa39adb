import React, { useState } from 'react';

const ExamsReportsPage = () => {
  // State for active and past exams
  const [activeExams] = useState([
    {
      id: 1,
      title: 'Mid Term Quiz',
      subject: 'C Programming',
      status: 'Upcoming',
      statusColor: 'text-amber-500',
      duration: '45 Minutes',
      date: 'April 9, 10 Am',
      action: 'Notify'
    },
    {
      id: 2,
      title: 'Mid Term Quiz',
      subject: 'Chemistry',
      status: 'Ongoing',
      statusColor: 'text-green-600',
      duration: '45 Minutes',
      date: 'April 9, 10 Am',
      action: 'Join Now'
    }
  ]);

  const [pastExams] = useState([
    {
      id: 1,
      name: 'Mid Term Exam',
      date: 'March 20',
      subject: 'Chemistry',
      score: 'Pending',
      scoreColor: 'text-gray-500'
    },
    {
      id: 2,
      name: 'Class Test',
      date: 'March 10',
      subject: 'Physics',
      score: '45%',
      scoreColor: 'text-black'
    }
  ]);

  // Handle join exam action
  const handleJoinExam = (examId) => {
    console.log(`Joining exam ${examId}`);
    // Additional logic to join the exam
  };

  // Handle notify action
  const handleNotify = (examId) => {
    console.log(`Set notification for exam ${examId}`);
    // Additional logic to set notifications
  };

  // Handle view paper action
  const handleViewPaper = (examId) => {
    console.log(`Viewing paper for exam ${examId}`);
    // Additional logic to view the exam paper
  };

  return (
    <div className="flex-1 p-6 bg-gray-50">
      {/* User greeting - consistent with the dashboard */}
      <div className="mb-6">
        <div className="text-sm text-gray-500">Hello, <span className="font-medium text-gray-800">Mary James</span></div>
        <div className="text-sm text-blue-500">1st Year - Section A</div>
      </div>
      
      {/* Active Exams Section */}
      <div className="mb-8">
        <h2 className="text-xl font-medium mb-4">Active Exams</h2>
        
        <div className="grid grid-cols-2 gap-6">
          {activeExams.map((exam) => (
            <div key={exam.id} className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between mb-2">
                <h3 className="font-medium text-lg">{exam.title}</h3>
                <div className={exam.statusColor}>Status: {exam.status}</div>
              </div>
              
              <div className="text-blue-500 font-medium mb-4">{exam.subject}</div>
              
              <div className="flex justify-between mb-6">
                <div>
                  <div className="text-sm text-gray-500">Duration:</div>
                  <div className="font-medium">{exam.duration}</div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm text-gray-500"></div>
                  <div className="font-medium">{exam.date}</div>
                </div>
              </div>
              
              {exam.action === 'Notify' ? (
                <button 
                  onClick={() => handleNotify(exam.id)}
                  className="w-full bg-purple-700 text-white py-2 rounded-md flex items-center justify-center"
                >
                  <span className="mr-2">Notify</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                  </svg>
                </button>
              ) : (
                <button 
                  onClick={() => handleJoinExam(exam.id)}
                  className="w-full bg-green-600 text-white py-2 rounded-md flex items-center justify-center"
                >
                  <span className="mr-2">Join Now</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Past Exams Section */}
      <div>
        <h2 className="text-xl font-medium mb-4">Past Exams</h2>
        
        <div className="bg-white rounded-lg shadow-sm">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-4 px-6">Exam Name</th>
                <th className="text-left py-4 px-6">Date</th>
                <th className="text-left py-4 px-6">Subject</th>
                <th className="text-left py-4 px-6">Score</th>
                <th className="text-left py-4 px-6">Actions</th>
              </tr>
            </thead>
            <tbody>
              {pastExams.map((exam) => (
                <tr key={exam.id} className="border-b">
                  <td className="py-4 px-6">{exam.name}</td>
                  <td className="py-4 px-6">{exam.date}</td>
                  <td className="py-4 px-6">{exam.subject}</td>
                  <td className={`py-4 px-6 ${exam.scoreColor}`}>{exam.score}</td>
                  <td className="py-4 px-6">
                    <button 
                      onClick={() => handleViewPaper(exam.id)}
                      className="text-purple-700 hover:underline"
                    >
                      View Paper
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ExamsReportsPage;