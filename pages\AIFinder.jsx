import React, { useState } from 'react';

// Translator function (assuming it's defined globally or imported)
const translator = (word1, word2) =>
  localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
    ? word1
    : localStorage.getItem("lang")
      ? word2
      : word1;

// Placeholder icons - in a real app, you might use an icon library like lucide-react or heroicons
const XIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const ChevronDownIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-400">
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
  </svg>
);

const ArrowRightIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 ml-2">
    <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
  </svg>
);

export default function AIFinder() {
  const [selectedStream, setSelectedStream] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');

  const streamOptions = [
    { value: '', label: translator('Select Stream', 'स्ट्रीम चुनें') },
    { value: 'engineering', label: translator('Engineering', 'इंजीनियरिंग') },
    { value: 'medical', label: translator('Medical', 'मेडिकल') },
    { value: 'arts', label: translator('Arts & Humanities', 'कला और मानविकी') },
    { value: 'commerce', label: translator('Commerce', 'वाणिज्य') },
    { value: 'technology', label: translator('Technology', 'प्रौद्योगिकी') },
  ];

  const languageOptions = [
    { value: '', label: translator('Select Language', 'भाषा चुनें') },
    { value: 'english', label: translator('English', 'अंग्रेज़ी') },
    { value: 'hindi', label: translator('Hindi', 'हिंदी') },
    { value: 'hinglish', label: translator('Hinglish', 'हिंग्लिश') },
  ];

  const handleClose = () => {
    console.log('Close button clicked');
  };

  const handleGeneratePersonalized = () => {
    console.log('Generate Personalized clicked');
  };

  const handleGenerateStudyPlan = () => {
    console.log('Generate Study Plan clicked. Stream:', selectedStream, 'Language:', selectedLanguage);
  };

  return (
    <div className="min-h-screen bg-white from-slate-900 to-slate-700 flex items-center justify-center p-4 font-sans">
      <div className="absolute top-4 bg-white rounded-xl shadow-2xl w-full max-w-4x1 mt-1 overflow-hidden">
        <div
          className="p-6 md:p-8 text-white relative"
          style={{ background: 'linear-gradient(95.21deg, #A78BFA 0%, #818CF8 100%)' }}
        >

          <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-purple-200 hover:text-white transition-colors"
            aria-label={translator("Close", "बंद करें")}
          >
            <XIcon />
          </button>
          <h1 className="text-2xl md:text-3xl font-bold">{translator('AI Resource Finder', 'एआई संसाधन खोजक')}</h1>
          <p className="text-sm md:text-base text-purple-200 mt-1">{translator('Get your Resources all at one place', 'अपने सभी संसाधन एक ही स्थान पर प्राप्त करें')}</p>
        </div>

        <div className="p-6 md:p-8 space-y-6 md:space-y-10">
          <div className="grid md:grid-cols-2 gap-6 md:gap-8">
            <div>
              <label htmlFor="stream" className="block text-sm font-medium text-gray-700 mb-1">
                {translator('Choose Your Stream from the Predefined List Below', 'नीचे दी गई पूर्वनिर्धारित सूची से अपनी स्ट्रीम चुनें')}
              </label>
              <div className="relative">
                <select
                  id="stream"
                  value={selectedStream}
                  onChange={(e) => setSelectedStream(e.target.value)}
                  className="w-full pl-3 pr-10 py-3 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 appearance-none"
                >
                  {streamOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ChevronDownIcon />
                </div>
              </div>
            </div>
            <div>
              <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                {translator('Choose Your Preferred Language (if any!)', 'अपनी पसंदीदा भाषा चुनें (यदि कोई हो!)')}
              </label>
              <div className="relative">
                <select
                  id="language"
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="w-full pl-3 pr-10 py-3 text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 appearance-none"
                >
                  {languageOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ChevronDownIcon />
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center my-6 md:my-8">
            <hr className="flex-grow border-[#7461FF]" />
            <span className="mx-4 text-gray-500 font-medium">{translator('OR', 'या')}</span>
            <hr className="flex-grow border-[#7461FF]" />
          </div>

          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <button
              onClick={handleGeneratePersonalized}
              className="w-full sm:w-auto px-4 py-3 text-purple-600 font-semibold bg-white border border-purple-200 rounded-lg hover:bg-purple-200 hover:text-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-150 relative -top-9"
            >
              {translator('Generate Personalized', 'वैयक्तिकृत जनरेट करें')}
            </button>
            <button
              onClick={handleGenerateStudyPlan}
              style={{ background: 'linear-gradient(90deg, #6BA0FF 0%, #755BFF 100%)' }}
              className="w-full sm:w-auto px-6 py-3 flex items-center justify-center text-white font-semibold rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150 mt-22"
            >
              {translator('Generate Study Plan', 'अध्ययन योजना बनाएं')}
              <ArrowRightIcon />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}