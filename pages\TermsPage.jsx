import React from "react";

const TermsAndConditions = () => {
    return (
        <div className="bg-white min-h-screen flex items-center justify-center px-4 py-10">
            <div className="w-full max-w-5xl max-h-[90vh] overflow-hidden rounded-2xl bg-white/20 backdrop-blur-md border border-white/30 shadow-2xl p-8">
                <h1 className="text-4xl font-bold text-center text-gray-900 mb-6">
                    Terms and Conditions
                </h1>
                <div className="overflow-y-auto h-[70vh] pr-4 space-y-6 text-gray-800 text-base leading-relaxed">
                    <div>
                        <h2 className="font-semibold text-lg mb-1">1. Credits and Usage</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>₹249 provides 100 credit coins.</li>
                            <li>Credits are deducted automatically based on usage.</li>
                            <li>Unused credits are non-refundable and non-transferable.</li>
                            <li>
                                <strong>Normal Class Mode (Video View Mode)</strong>
                                <ul className="list-disc ml-6">
                                    <li>1 credit coin provides 1 to 1.5 minutes of watch time.</li>
                                    <li>Includes MCQs, key points, and notes for comprehensive learning.</li>
                                </ul>
                            </li>
                            <li>
                                <strong>Examination Mode</strong>
                                <ul className="list-disc ml-6">
                                    <li>4 credit coins per test attempt.</li>
                                    <li>Includes the entire test, detailed feedback, and a performance report.</li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">2. AI-Generated Content</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>All content on AI Classroom is AI-generated, including videos, notes, MCQs, and feedback.</li>
                            <li>While efforts are made to ensure accuracy, content may occasionally contain errors.</li>
                            <li>Users are advised to independently verify critical information.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">3. Evaluation Feedback and Reports</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Feedback and performance reports are purely AI-generated.</li>
                            <li>Use these evaluations as guidance, not sole decision-making criteria.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">4. Ethical Use Policy</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Content must not be used for unethical or unlawful purposes.</li>
                            <li>Plagiarism, fraud, and illegal activity are strictly prohibited.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">5. Liability Disclaimer</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>AI Classroom is not responsible for decisions made based on AI-generated content.</li>
                            <li>No guarantees are provided regarding outcomes or results.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">6. General Terms</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>Users must keep their account credentials secure.</li>
                            <li>Violating terms may result in suspension without refund.</li>
                            <li>Terms may be updated without prior notice.</li>
                        </ul>
                    </div>

                    <div>
                        <h2 className="font-semibold text-lg mb-1">7. AI-Generated Images Disclaimer</h2>
                        <ul className="list-disc ml-5 space-y-1">
                            <li>All images are AI-generated and may depict fictional scenarios.</li>
                            <li>They do not represent real people, places, or events.</li>
                            <li>Any resemblance is coincidental and unintentional.</li>
                            <li>No intention to mislead, offend, or harm any individual or group.</li>
                            <li>Do not rely on these visuals as factual representations.</li>
                            <li>Misuse for harmful or unethical purposes is prohibited.</li>
                        </ul>
                    </div>

                    <p className="text-sm text-center text-gray-600 mt-4">
                        By using AI Classroom, you acknowledge and agree to these terms.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default TermsAndConditions;
