import React, { useState, useEffect } from 'react';

// Add this style component for the font-face definition
const FontStyles = () => (
  <style jsx global>{`
    @font-face {
      font-family: 'Digital-7';
      src: url('/fonts/Digital-7.ttf') format('truetype');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
    
    .font-digital-seven {
      font-family: 'Digital-7', monospace;
    }
  `}</style>
);

// Helper component for individual time units (Days, Hours, Minutes)
const TimeCard = ({ value, label }) => {
  const svgWidth = "w-16 sm:w-20";
  const svgHeight = "h-20 sm:h-24";

  return (
    <div className="flex flex-col items-center">
      <div className="flex space-x-1">
        {String(value).padStart(2, '0').split('').map((digit, index) => (
          <div
            key={index}
            className={`relative ${svgWidth} ${svgHeight} flex items-center justify-center`}
          >
            <img
              src="/lockbox.svg"
              alt=""
              className="absolute inset-0 w-full h-full object-contain"
            />
            <span className="relative z-10 font-digital-seven text-5xl sm:text-6xl text-violet-950">
              {digit}
            </span>
          </div>
        ))}
      </div>
      <span className="mt-3 text-sm font-semibold text-gray-600 uppercase tracking-wider">{label}</span>
    </div>
  );
};

// Countdown Timer Component
const CountdownTimer = ({ targetDate }) => {
  const calculateTimeLeft = () => {
    const difference = +new Date(targetDate) - +new Date();
    let timeLeft = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
      };
    } else {
      timeLeft = { days: 0, hours: 0, minutes: 0 };
    }
    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);
    return () => clearInterval(timer);
  }, [targetDate]);

  return (
    <div className="flex justify-center items-start space-x-2 sm:space-x-4 my-10">
      <TimeCard value={timeLeft.days} label="Days" />
      <TimeCard value={timeLeft.hours} label="Hours" />
      <TimeCard value={timeLeft.minutes} label="Minutes" />
    </div>
  );
};

// Email Form Component
const EmailForm = () => {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState({ text: '', type: '' });

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Email submitted for notification:', email);
    setMessage({ text: `Thank you! We will notify you at ${email} when the contest starts.`, type: 'success' });
    setEmail(''); 
    
    setTimeout(() => setMessage({ text: '', type: '' }), 5000);
  };

  return (
    <div className="max-w-md mx-auto">
      <form onSubmit={handleSubmit} className="mt-12 flex flex-col sm:flex-row justify-center">
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Email Address"
          required
          className="flex-grow px-5 py-3 mb-3 sm:mb-0 sm:mr-3 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 transition-shadow"
        />
        <button
          type="submit"
          className="px-8 py-3 font-semibold text-white bg-violet-950 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
        >
          Notify Me
        </button>
      </form>
      {message.text && (
        <div className={`mt-3 text-center ${message.type === 'success' ? 'text-green-600' : 'text-red-600'}`}>
          {message.text}
        </div>
      )}
    </div>
  );
};

// Main Popup Component
const ContestPopup = ({ onClose }) => {
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + 1);

  return (
    <>
      <FontStyles />
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 backdrop-blur-[10px] p-4">
        <div className="relative w-full max-w-7xl sm:max-w-7xl md:max-w-7xl p-40 sm:p-20 bg-white/95 rounded-2xl shadow-2xl border border-gray-200/50">
          <button
            onClick={onClose}
            className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-700 transition-colors z-20"
            aria-label="Close popup"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 sm:h-8 sm:w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <div className="text-center">
            <div className="flex items-center justify-center mb-6 sm:mb-8">
              <img 
                src="/locklogo.png" 
                alt="AI Classroom Logo" 
                className="h-auto w-48 sm:w-60 md:w-72 max-h-[60px] object-contain"
              />
            </div>

            <CountdownTimer targetDate={targetDate} />
            <EmailForm />
          </div>
        </div>
      </div>
    </>
  );
};

export default ContestPopup;