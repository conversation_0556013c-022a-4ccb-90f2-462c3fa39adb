import { useState, useEffect } from "react";

export default function ProfessionalEdTechLoader() {
  const [progress, setProgress] = useState(0);

  // Simulate loading progress
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 80) {
          clearInterval(interval);
          return 80;
        }
        return Math.min(prev + Math.random() * 5, 100);
      });
    }, 200);

    return () => clearInterval(interval);
  }, []);

  return (
    <div
      style={{
        position: "relative",
        height: "384px",
        marginTop: "24px",
        borderRadius: "8px",
        boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
        overflow: "hidden",
      }}
    >
      {/* Background gradient */}
      <div
        style={{
          position: "absolute",
          inset: 0,
          background: "linear-gradient(to bottom right, #172554, #312e81)",
        }}
      ></div>

      {/* Grid pattern overlay */}
      <div
        style={{
          position: "absolute",
          inset: 0,
          opacity: 0.1,
          backgroundImage:
            "linear-gradient(#fff 1px, transparent 1px), linear-gradient(90deg, #fff 1px, transparent 1px)",
          backgroundSize: "20px 20px",
        }}
      ></div>

      {/* Subtle animated dots */}
      {[...Array(12)].map((_, i) => (
        <div
          key={i}
          style={{
            position: "absolute",
            borderRadius: "50%",
            backgroundColor: "#60a5fa",
            opacity: 0.2,
            width: `${Math.random() * 6 + 2}px`,
            height: `${Math.random() * 6 + 2}px`,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animation: `float${i} ${Math.random() * 10 + 15}s linear infinite`,
          }}
        ></div>
      ))}

      {/* Main content card */}
      <div
        style={{
          position: "absolute",
          inset: 0,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div
          style={{
            width: "320px",
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            backdropFilter: "blur(8px)",
            borderRadius: "12px",
            padding: "32px",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            boxShadow:
              "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
          }}
        >
          {/* Logo/icon */}
          <div
            style={{
              marginBottom: "24px",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <div
              style={{
                position: "relative",
                width: "64px",
                height: "64px",
                borderRadius: "8px",
                background:
                  "linear-gradient(to bottom right, #3b82f6, #4f46e5)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                style={{
                  width: "32px",
                  height: "32px",
                  color: "white",
                }}
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                <path d="M2 17l10 5 10-5"></path>
                <path d="M2 12l10 5 10-5"></path>
              </svg>
              <div
                style={{
                  position: "absolute",
                  inset: "-4px",
                  borderRadius: "8px",
                  opacity: 0.5,
                  background:
                    "linear-gradient(to bottom right, #60a5fa, #6366f1)",
                  filter: "blur(4px)",
                  zIndex: -1,
                }}
              ></div>
            </div>
          </div>

          {/* Content */}
          <h3
            style={{
              textAlign: "center",
              fontSize: "20px",
              fontWeight: 600,
              color: "white",
              marginBottom: "8px",
            }}
          >
            Loading Learning Module
          </h3>

          <p
            style={{
              textAlign: "center",
              color: "rgba(255, 255, 255, 0.7)",
              fontSize: "14px",
              marginBottom: "24px",
            }}
          >
            Preparing your personalized content
          </p>

          {/* Progress bar */}
          <div
            style={{
              height: "8px",
              width: "100%",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              borderRadius: "9999px",
              marginBottom: "24px",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                height: "100%",
                background: "linear-gradient(to right, #3b82f6, #4f46e5)",
                borderRadius: "9999px",
                transition: "width 300ms ease-out",
                width: `${progress}%`,
              }}
            ></div>
          </div>

          {/* Status */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              fontSize: "12px",
              color: "rgba(255, 255, 255, 0.6)",
            }}
          >
            <div>Status: Initializing resources</div>
            <div>{Math.round(progress)}%</div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes float0 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(20px, -30px);
            opacity: 0.2;
          }
          100% {
            transform: translate(0, -60px);
            opacity: 0;
          }
        }
        @keyframes float1 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(-15px, -20px);
            opacity: 0.3;
          }
          100% {
            transform: translate(-5px, -50px);
            opacity: 0;
          }
        }
        @keyframes float2 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(15px, -25px);
            opacity: 0.2;
          }
          100% {
            transform: translate(5px, -55px);
            opacity: 0;
          }
        }
        @keyframes float3 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(-20px, -15px);
            opacity: 0.3;
          }
          100% {
            transform: translate(-10px, -45px);
            opacity: 0;
          }
        }
        @keyframes float4 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(25px, -35px);
            opacity: 0.2;
          }
          100% {
            transform: translate(10px, -65px);
            opacity: 0;
          }
        }
        @keyframes float5 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(-10px, -40px);
            opacity: 0.3;
          }
          100% {
            transform: translate(-15px, -70px);
            opacity: 0;
          }
        }
        @keyframes float6 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(30px, -10px);
            opacity: 0.2;
          }
          100% {
            transform: translate(15px, -40px);
            opacity: 0;
          }
        }
        @keyframes float7 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(-25px, -45px);
            opacity: 0.3;
          }
          100% {
            transform: translate(-20px, -75px);
            opacity: 0;
          }
        }
        @keyframes float8 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(35px, -5px);
            opacity: 0.2;
          }
          100% {
            transform: translate(25px, -35px);
            opacity: 0;
          }
        }
        @keyframes float9 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(-30px, -50px);
            opacity: 0.3;
          }
          100% {
            transform: translate(-25px, -80px);
            opacity: 0;
          }
        }
        @keyframes float10 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(10px, -20px);
            opacity: 0.2;
          }
          100% {
            transform: translate(5px, -50px);
            opacity: 0;
          }
        }
        @keyframes float11 {
          0% {
            transform: translate(0, 0);
            opacity: 0.1;
          }
          50% {
            transform: translate(-5px, -30px);
            opacity: 0.3;
          }
          100% {
            transform: translate(-10px, -60px);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
}
